/*
 * File: app/store/CcpInvoicesToDepositSlip.js
 *
 * This file was generated by Sencha Architect version 3.2.0.
 * http://www.sencha.com/products/architect/
 *
 * This file requires use of the Ext JS 4.2.x library, under independent license.
 * License of Sencha Architect does not include license for Ext JS 4.2.x. For more
 * details see http://www.sencha.com/license <NAME_EMAIL>.
 *
 * This file will be auto-generated each and everytime you save your project.
 *
 * Do NOT hand edit this file.
 */

Ext.define('mc2ui.store.CcpInvoicesToDepositSlip', {
    extend: 'Ext.data.Store',

    requires: [
        'mc2ui.model.CcpInvoice',
        'Ext.data.proxy.Ajax',
        'Ext.data.reader.Json'
    ],

    constructor: function(cfg) {
        var me = this;
        cfg = cfg || {};
        me.callParent([Ext.apply({
            model: 'mc2ui.model.CcpInvoice',
            storeId: 'CcpInvoicesToDepositSlip',
            proxy: {
                type: 'ajax',
                timeout: 90000,
                url: '/mc2-api/ccp/invoice',
                reader: {
                    type: 'json',
                    root: 'results'
                }
            },
            listeners: {
                beforeload: {
                    fn: me.onJsonstoreBeforeLoad,
                    scope: me
                }
            }
        }, cfg)]);
    },

    onJsonstoreBeforeLoad: function(store, operation, eOpts) {
        var params,
            expiration_date_start = Ext.getCmp('CcpDepositSlipInvoiceExpirationStart').getValue(),
            expiration_date_end = Ext.getCmp('CcpDepositSlipInvoiceExpirationEnd').getValue(),
            suffix = Ext.getCmp('CcpDepositSlipInvoiceSuffix').getValue();

        params = {
            deposit_slip: true,
            payment_method: 9
        };

        if(expiration_date_start) {
            params.expiration_date_start = expiration_date_start;
        }
        if(expiration_date_end) {
            params.expiration_date_end = expiration_date_end;
        }
        if(suffix) {
            params.suffix = suffix;
        }


        params.limit = 0;
        params.credit_note = false;
        store.getProxy().extraParams = params;

    }

});