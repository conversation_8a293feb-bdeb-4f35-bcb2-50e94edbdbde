/*
 * File: app/model/Discount.js
 *
 * This file was generated by Sencha Architect version 3.2.0.
 * http://www.sencha.com/products/architect/
 *
 * This file requires use of the Ext JS 4.2.x library, under independent license.
 * License of Sencha Architect does not include license for Ext JS 4.2.x. For more
 * details see http://www.sencha.com/license <NAME_EMAIL>.
 *
 * This file will be auto-generated each and everytime you save your project.
 *
 * Do NOT hand edit this file.
 */

Ext.define('mc2ui.model.Discount', {
    extend: 'Ext.data.Model',

    requires: [
        'Ext.data.Field'
    ],

    fields: [
        {
            name: 'id'
        },
        {
            name: 'amount',
            type: 'float'
        },
        {
            mapping: 'type.name',
            name: 'type_text'
        },
        {
            mapping: 'type.school_year',
            name: 'type_school_year'
        },
        {
            mapping: 'additional.name',
            name: 'additional_text'
        },
        {
            name: 'subject_type'
        },
        {
            name: 'discount_order',
            useNull: true
        }
    ]
});