/*
 * File: app/view/ArchivePnl.js
 *
 * This file was generated by Sencha Architect version 3.2.0.
 * http://www.sencha.com/products/architect/
 *
 * This file requires use of the Ext JS 4.2.x library, under independent license.
 * License of Sencha Architect does not include license for Ext JS 4.2.x. For more
 * details see http://www.sencha.com/license <NAME_EMAIL>.
 *
 * This file will be auto-generated each and everytime you save your project.
 *
 * Do NOT hand edit this file.
 */

Ext.define('mc2ui.view.ArchivePnl', {
    extend: 'Ext.panel.Panel',
    alias: 'widget.ArchivePnl',

    requires: [
        'Ext.form.Panel',
        'Ext.form.FieldSet',
        'Ext.form.field.Date',
        'Ext.form.field.ComboBox',
        'Ext.form.field.Number',
        'Ext.tab.Panel',
        'Ext.tab.Tab',
        'Ext.form.Label',
        'Ext.grid.Panel',
        'Ext.grid.column.Action',
        'Ext.grid.View',
        'Ext.toolbar.Spacer',
        'Ext.toolbar.Paging',
        'Ext.grid.column.Number',
        'Ext.grid.column.Date',
        'Ext.grid.column.Boolean',
        'Ext.menu.Menu',
        'Ext.menu.Separator',
        'Ext.toolbar.Separator'
    ],

    border: false,
    hidden: true,
    id: 'ArchivePnl',
    itemId: 'ArchivePnl',
    layout: 'border',
    header: false,
    title: 'Archivio Documenti',
    titleAlign: 'center',

    initComponent: function () {
        var me = this;

        Ext.applyIf(me, {
            items: [
                {
                    xtype: 'panel',
                    region: 'west',
                    split: true,
                    hidden: true,
                    id: 'ArchiveLeftPnl',
                    itemId: 'ArchiveLeftPnl',
                    width: 225,
                    autoScroll: true,
                    layout: 'fit',
                    collapseDirection: 'left',
                    collapsed: true,
                    collapsible: true,
                    iconCls: 'icon-find',
                    title: 'Filtri',
                    titleCollapse: true,
                    items: [
                        {
                            xtype: 'form',
                            applyFilter: function () {
                                if (mc2ui.app.archiveFilterEventTimeoutId > 0) {
                                    clearTimeout(mc2ui.app.archiveFilterEventTimeoutId);
                                }

                                mc2ui.app.archiveFilterEventTimeoutId = setTimeout(function () {
                                    Ext.getStore('ArchiveDocumentsArchived').loadPage(1);
                                }, 1000);
                            },
                            border: false,
                            id: 'ArchiveFilterForm',
                            itemId: 'ArchiveFilterForm',
                            autoScroll: true,
                            bodyCls: 'bck-content',
                            bodyPadding: 10,
                            header: false,
                            layout: {
                                type: 'vbox',
                                align: 'stretch'
                            },
                            items: [
                                {
                                    xtype: 'button',
                                    handler: function (button, e) {
                                        var form = Ext.getCmp('ArchiveFilterForm').getForm(),
                                            cc = Ext.getCmp('ArchiveFilterClass'),
                                            co = Ext.getCmp('ArchiveFilterOrigin');

                                        form.reset();
                                        cc.select(0);
                                        co.select(0);
                                    },
                                    margin: '0 0 10 0',
                                    iconCls: 'icon-cancel',
                                    text: 'Cancella filtri'
                                },
                                {
                                    xtype: 'fieldset',
                                    title: 'Date',
                                    layout: {
                                        type: 'vbox',
                                        align: 'stretch'
                                    },
                                    items: [
                                        {
                                            xtype: 'datefield',
                                            endDateField: 'ArchiveFilterMetadataDateEnd',
                                            id: 'ArchiveFilterMetadataDateStart',
                                            itemId: 'ArchiveFilterMetadataDateStart',
                                            fieldLabel: 'Dal',
                                            labelAlign: 'right',
                                            labelWidth: 50,
                                            name: 'date_start',
                                            vtype: 'daterange',
                                            editable: false,
                                            format: 'd/m/Y',
                                            startDay: 1,
                                            submitFormat: 'c',
                                            listeners: {
                                                change: {
                                                    fn: me.onArchiveFilterMetadataDateStartChange,
                                                    delay: 300,
                                                    buffer: 300,
                                                    scope: me
                                                }
                                            }
                                        },
                                        {
                                            xtype: 'datefield',
                                            startDateField: 'ArchiveFilterMetadataDateStart',
                                            id: 'ArchiveFilterMetadataDateEnd',
                                            itemId: 'ArchiveFilterMetadataDateEnd',
                                            fieldLabel: 'al',
                                            labelAlign: 'right',
                                            labelWidth: 50,
                                            name: 'date_end',
                                            vtype: 'daterange',
                                            editable: false,
                                            format: 'd/m/Y',
                                            startDay: 1,
                                            submitFormat: 'c',
                                            listeners: {
                                                change: {
                                                    fn: me.onArchiveFilterMetadataDateEndChange,
                                                    delay: 300,
                                                    buffer: 300,
                                                    scope: me
                                                }
                                            }
                                        }
                                    ]
                                },
                                {
                                    xtype: 'fieldset',
                                    title: 'Tipo di flusso',
                                    layout: {
                                        type: 'vbox',
                                        align: 'stretch'
                                    },
                                    items: [
                                        {
                                            xtype: 'combobox',
                                            id: 'ArchiveFilterClass',
                                            itemId: 'ArchiveFilterClass',
                                            name: 'class_id',
                                            editable: false,
                                            displayField: 'name',
                                            forceSelection: true,
                                            queryMode: 'local',
                                            store: 'ArchiveClassesFilter',
                                            valueField: 'id',
                                            listeners: {
                                                change: {
                                                    fn: me.onArchiveFilterClassChange,
                                                    delay: 300,
                                                    buffer: 300,
                                                    scope: me
                                                }
                                            }
                                        }
                                    ]
                                },
                                {
                                    xtype: 'fieldset',
                                    hidden: true,
                                    title: 'Origine',
                                    layout: {
                                        type: 'vbox',
                                        align: 'stretch'
                                    },
                                    items: [
                                        {
                                            xtype: 'combobox',
                                            id: 'ArchiveFilterOrigin',
                                            itemId: 'ArchiveFilterOrigin',
                                            name: 'origin_id',
                                            editable: false,
                                            displayField: 'name',
                                            forceSelection: true,
                                            queryMode: 'local',
                                            store: 'ArchiveOriginsFilter',
                                            valueField: 'id',
                                            listeners: {
                                                change: {
                                                    fn: me.onArchiveFilterOriginChange,
                                                    delay: 300,
                                                    buffer: 300,
                                                    scope: me
                                                }
                                            }
                                        }
                                    ]
                                },
                                {
                                    xtype: 'fieldset',
                                    title: 'Completati',
                                    layout: {
                                        type: 'vbox',
                                        align: 'stretch'
                                    },
                                    items: [
                                        {
                                            xtype: 'combobox',
                                            id: 'ArchiveFilterCompleted',
                                            itemId: 'ArchiveFilterCompleted',
                                            name: 'completed',
                                            editable: false,
                                            displayField: 'value',
                                            forceSelection: true,
                                            queryMode: 'local',
                                            store: 'ArchiveCompletedFilter',
                                            valueField: 'id',
                                            listeners: {
                                                change: {
                                                    fn: me.onArchiveFilteCompleteChange,
                                                    delay: 300,
                                                    buffer: 300,
                                                    scope: me
                                                }
                                            }
                                        }
                                    ]
                                },
                                {
                                    xtype: 'fieldset',
                                    title: 'Presa visione',
                                    layout: {
                                        type: 'vbox',
                                        align: 'stretch'
                                    },
                                    items: [
                                        {
                                            xtype: 'combobox',
                                            id: 'ArchiveFilterChecked',
                                            itemId: 'ArchiveFilterChecked',
                                            name: 'checked',
                                            editable: false,
                                            displayField: 'value',
                                            forceSelection: true,
                                            queryMode: 'local',
                                            store: 'ArchiveCheckedFilter',
                                            valueField: 'id',
                                            listeners: {
                                                change: {
                                                    fn: me.onArchiveFilteCheckedChange,
                                                    delay: 300,
                                                    buffer: 300,
                                                    scope: me
                                                }
                                            }
                                        }
                                    ]
                                },
                                {
                                    xtype: 'fieldset',
                                    title: 'Assegnato all\'utente',
                                    layout: {
                                        type: 'vbox',
                                        align: 'stretch'
                                    },
                                    items: [
                                        {
                                            xtype: 'combobox',
                                            id: 'ArchiveFilterAssignToUser',
                                            itemId: 'ArchiveFilterAssignToUser',
                                            name: 'assign_to_user',
                                            editable: false,
                                            displayField: 'user_name',
                                            forceSelection: true,
                                            queryMode: 'local',
                                            store: 'SettingsUsers',
                                            valueField: 'user_id',
                                            listeners: {
                                                change: {
                                                    fn: me.onArchiveFilteAssignToUserChange,
                                                    delay: 300,
                                                    buffer: 300,
                                                    scope: me
                                                }
                                            }
                                        }
                                    ]
                                },
                                {
                                    xtype: 'fieldset',
                                    title: 'Assegnato a ufficio',
                                    layout: {
                                        type: 'vbox',
                                        align: 'stretch'
                                    },
                                    items: [
                                        {
                                            xtype: 'combobox',
                                            id: 'ArchiveFilterAssignToOffice',
                                            itemId: 'ArchiveFilterAssignToOffice',
                                            name: 'assign_to_office',
                                            editable: false,
                                            displayField: 'name',
                                            forceSelection: true,
                                            queryMode: 'local',
                                            store: 'ArchiveOffices',
                                            valueField: 'id',
                                            listeners: {
                                                change: {
                                                    fn: me.onArchiveFilteAssignToUserChange1,
                                                    delay: 300,
                                                    buffer: 300,
                                                    scope: me
                                                }
                                            }
                                        }
                                    ]
                                },
                                {
                                    xtype: 'fieldset',
                                    hidden: true,
                                    title: 'Periodo archiviazione',
                                    layout: {
                                        type: 'vbox',
                                        align: 'stretch'
                                    },
                                    items: [
                                        {
                                            xtype: 'datefield',
                                            endDateField: 'ArchiveFilterArchiveDateEnd',
                                            id: 'ArchiveFilterArchiveDateStart',
                                            itemId: 'ArchiveFilterArchiveDateStart',
                                            fieldLabel: 'Dal',
                                            labelAlign: 'right',
                                            labelWidth: 50,
                                            name: 'archive_start',
                                            vtype: 'daterange',
                                            editable: false,
                                            format: 'd/m/Y',
                                            startDay: 1,
                                            submitFormat: 'c',
                                            listeners: {
                                                change: {
                                                    fn: me.onArchiveFilterArchiveDateStartChange,
                                                    scope: me
                                                }
                                            }
                                        },
                                        {
                                            xtype: 'datefield',
                                            startDateField: 'ArchiveFilterArchiveDateStart',
                                            id: 'ArchiveFilterArchiveDateEnd',
                                            itemId: 'ArchiveFilterArchiveDateEnd',
                                            fieldLabel: 'al',
                                            labelAlign: 'right',
                                            labelWidth: 50,
                                            name: 'archive_end',
                                            vtype: 'daterange',
                                            editable: false,
                                            format: 'd/m/Y',
                                            startDay: 1,
                                            submitFormat: 'c',
                                            listeners: {
                                                change: {
                                                    fn: me.onArchiveFilterArchiveDateEndChange,
                                                    scope: me
                                                }
                                            }
                                        }
                                    ]
                                },
                                {
                                    xtype: 'fieldset',
                                    hidden: true,
                                    title: 'Nome',
                                    layout: {
                                        type: 'vbox',
                                        align: 'stretch'
                                    },
                                    items: [
                                        {
                                            xtype: 'textfield',
                                            id: 'ArchiveFilterName',
                                            itemId: 'ArchiveFilterName',
                                            name: 'name',
                                            listeners: {
                                                change: {
                                                    fn: me.onArchiveFilterNameChange,
                                                    scope: me
                                                }
                                            }
                                        }
                                    ]
                                },
                                {
                                    xtype: 'fieldset',
                                    hidden: true,
                                    title: 'Descrizione',
                                    layout: {
                                        type: 'vbox',
                                        align: 'stretch'
                                    },
                                    items: [
                                        {
                                            xtype: 'textfield',
                                            id: 'ArchiveFilterDescription',
                                            itemId: 'ArchiveFilterDescription',
                                            name: 'description',
                                            listeners: {
                                                change: {
                                                    fn: me.onArchiveFilterDescriptionChange,
                                                    scope: me
                                                }
                                            }
                                        }
                                    ]
                                },
                                {
                                    xtype: 'fieldset',
                                    hidden: true,
                                    title: 'Protocollati',
                                    layout: {
                                        type: 'vbox',
                                        align: 'stretch'
                                    },
                                    items: [
                                        {
                                            xtype: 'combobox',
                                            flex: 1,
                                            id: 'ArchiveFilterActionProtocol',
                                            itemId: 'ArchiveFilterActionProtocol',
                                            name: 'action_protocol',
                                            editable: false,
                                            forceSelection: true,
                                            queryMode: 'local',
                                            store: 'ArchiveActionProtocolFilter',
                                            valueField: 'id',
                                            listeners: {
                                                change: {
                                                    fn: me.onArchiveFilterActionProtocolChange,
                                                    scope: me
                                                }
                                            }
                                        }
                                    ]
                                },
                                {
                                    xtype: 'fieldset',
                                    hidden: true,
                                    title: 'Pubblicati su Albo',
                                    layout: {
                                        type: 'vbox',
                                        align: 'stretch'
                                    },
                                    items: [
                                        {
                                            xtype: 'combobox',
                                            flex: 1,
                                            id: 'ArchiveFilterActionAlbo',
                                            itemId: 'ArchiveFilterActionAlbo',
                                            name: 'action_albo',
                                            editable: false,
                                            forceSelection: true,
                                            queryMode: 'local',
                                            store: 'ArchiveActionAlboFilter',
                                            valueField: 'id',
                                            listeners: {
                                                change: {
                                                    fn: me.onArchiveFilterActionAlboChange,
                                                    scope: me
                                                }
                                            }
                                        }
                                    ]
                                },
                                {
                                    xtype: 'fieldset',
                                    hidden: true,
                                    title: 'Allegati alla Trasparenza',
                                    layout: {
                                        type: 'vbox',
                                        align: 'stretch'
                                    },
                                    items: [
                                        {
                                            xtype: 'combobox',
                                            flex: 1,
                                            id: 'ArchiveFilterActionTrasparenza',
                                            itemId: 'ArchiveFilterActionTrasparenza',
                                            name: 'action_trasparenza',
                                            editable: false,
                                            forceSelection: true,
                                            queryMode: 'local',
                                            store: 'ArchiveActionTrasparenzaFilter',
                                            valueField: 'id',
                                            listeners: {
                                                change: {
                                                    fn: me.onArchiveFilterActionTrasparenzaChange,
                                                    scope: me
                                                }
                                            }
                                        }
                                    ]
                                },
                                {
                                    xtype: 'fieldset',
                                    hidden: true,
                                    title: 'Dati testuali',
                                    layout: {
                                        type: 'vbox',
                                        align: 'stretch'
                                    },
                                    items: [
                                        {
                                            xtype: 'textfield',
                                            id: 'ArchiveFilterMetadataText',
                                            itemId: 'ArchiveFilterMetadataText',
                                            name: 'metadata_text',
                                            listeners: {
                                                change: {
                                                    fn: me.onArchiveFilterMetadataTextChange,
                                                    scope: me
                                                }
                                            }
                                        }
                                    ]
                                },
                                {
                                    xtype: 'fieldset',
                                    hidden: true,
                                    title: 'Dati numerici',
                                    layout: {
                                        type: 'vbox',
                                        align: 'stretch'
                                    },
                                    items: [
                                        {
                                            xtype: 'numberfield',
                                            id: 'ArchiveFilterMetadataNumericStart',
                                            itemId: 'ArchiveFilterMetadataNumericStart',
                                            fieldLabel: 'Dal',
                                            labelAlign: 'right',
                                            labelWidth: 50,
                                            name: 'metadata_numeric_start',
                                            hideTrigger: true,
                                            allowDecimals: false,
                                            allowExponential: false,
                                            minValue: 0,
                                            listeners: {
                                                change: {
                                                    fn: me.onArchiveFilterMetadataNumericStartChange,
                                                    scope: me
                                                }
                                            }
                                        },
                                        {
                                            xtype: 'numberfield',
                                            id: 'ArchiveFilterMetadataNumericEnd',
                                            itemId: 'ArchiveFilterMetadataNumericEnd',
                                            fieldLabel: 'al',
                                            labelAlign: 'right',
                                            labelWidth: 50,
                                            name: 'metadata_numeric_end',
                                            hideTrigger: true,
                                            allowDecimals: false,
                                            allowExponential: false,
                                            minValue: 0,
                                            listeners: {
                                                change: {
                                                    fn: me.onArchiveFilterMetadataNumericEndChange,
                                                    scope: me
                                                }
                                            }
                                        }
                                    ]
                                }
                            ]
                        }
                    ],
                    listeners: {
                        boxready: {
                            fn: me.onArchiveLeftPnlBoxReady,
                            scope: me
                        }
                    }
                },
                {
                    xtype: 'tabpanel',
                    permissible: true,
                    region: 'center',
                    split: true,
                    id: 'ArchiveMainPnl',
                    activeTab: 0,
                    listeners: {
                        boxready: {
                            fn: me.onArchiveMainPnlBoxReady,
                            scope: me
                        },
                        beforetabchange: {
                            fn: me.onArchiveMainPnlBeforeTabChange,
                            scope: me
                        }
                    },
                    items: [
                        {
                            xtype: 'panel',
                            updateDashboard: function () {
                                var totalCnt = Ext.getCmp('ArchiveDashboardTotalCnt'),
                                    usersCnt = Ext.getCmp('ArchiveDashboardUsersCnt'),
                                    officesCnt = Ext.getCmp('ArchiveDashboardOfficesCnt'),
                                    pnl, wds, wcs, wes;

                                officesCnt.removeAll();
                                usersCnt.removeAll();
                                totalCnt.removeAll();
                                Ext.Ajax.request({
                                    url: '/mc2-api/archive/document/dashboard',
                                    success: function (r) {
                                        var res = Ext.decode(r.responseText);
                                        if (res.success === true) {
                                            totalCnt.add(Ext.create('Ext.form.Label', {
                                                html: '<h2>Da completare: ' + res.results.totals.to_do + '<h2>'
                                            }));
                                            totalCnt.add(Ext.create('Ext.form.Label', {
                                                html: '<h2>Documento più vecchio: ' + res.results.totals.oldest_doc + '<h2>'
                                            }));
                                            Ext.each(res.results.users, function (u) {
                                                wcs = u.to_do_warning === true ? 'style="color:red;"' : '';
                                                wds = u.oldest_doc_warning === true ? 'style="color:red;"' : '';
                                                wes = u.oldest_expired_color ? 'style="color:' + u.oldest_expired_color + ';"' : '';
                                                pnl = Ext.create('Ext.container.Container', {
                                                    layout: {
                                                        type: 'vbox',
                                                        align: 'stretch'
                                                    },
                                                    style: 'cursor: pointer;border-color: #000 !important;box-shadow: 2px 2px 2px #888888; background-color: rgb(202, 179, 220);',
                                                    margin: 10,
                                                    listeners: {
                                                        element: 'el',
                                                        click: function () {
                                                            Ext.widget('ArchiveDashboardWin').show();
                                                            Ext.getStore('ArchiveDashboard').load({
                                                                params: {
                                                                    'completed': 0,
                                                                    assign_to_user: u.id,
                                                                    "hide_all_user_doc": 1
                                                                }
                                                            });
                                                        }
                                                    },
                                                    items: [
                                                        {
                                                            xtype: 'container',
                                                            padding: "5px",
                                                            items: [
                                                                {
                                                                    xtype: 'label',
                                                                    html: '<i>' + u.name + '</i>'
                                                                }
                                                            ]
                                                        },
                                                        {
                                                            xtype: 'container',
                                                            layout: {
                                                                type: 'vbox',
                                                                align: 'center',
                                                                pack: 'center'
                                                            },
                                                            items: [
                                                                {
                                                                    xtype: 'label',
                                                                    html: '<h2 ' + wcs + '>Da completare: ' + u.to_do + '<h2>'
                                                                },
                                                                {
                                                                    xtype: 'label',
                                                                    html: '<h2 ' + wds + '>Documento più vecchio: ' + u.oldest_doc + '<h2>'
                                                                },
                                                                {
                                                                    xtype: 'label',
                                                                    html: '<h2 ' + wes + '>Data prima scadenza: ' + u.oldest_expired + '<h2>'
                                                                }
                                                            ]
                                                        }
                                                    ]
                                                });

                                                usersCnt.add(pnl);
                                            });

                                            Ext.each(res.results.offices, function (o) {
                                                wcs = o.to_do_warning === true ? 'style="color:red;"' : '';
                                                wds = o.oldest_doc_warning === true ? 'style="color:red;"' : '';
                                                wes = o.oldest_expired_color ? 'style="color:' + o.oldest_expired_color + ';"' : '';
                                                pnl = Ext.create('Ext.container.Container', {
                                                    layout: {
                                                        type: 'vbox',
                                                        align: 'stretch'
                                                    },
                                                    style: 'cursor: pointer;border-color: #000 !important;box-shadow: 2px 2px 2px #888888; background-color: rgb(220, 179, 179);',
                                                    margin: 10,
                                                    listeners: {
                                                        element: 'el',
                                                        click: function () {
                                                            Ext.widget('ArchiveDashboardWin').show();
                                                            Ext.getStore('ArchiveDashboard').load({
                                                                params: {
                                                                    'completed': 0,
                                                                    assign_to_office: Ext.encode([o.id]),
                                                                    "hide_all_user_doc": 1
                                                                }
                                                            });
                                                        }
                                                    },
                                                    items: [
                                                        {
                                                            xtype: 'container',
                                                            padding: "5px",
                                                            items: [
                                                                {
                                                                    xtype: 'label',
                                                                    html: '<i>' + o.name + '</i>'
                                                                }
                                                            ]
                                                        },
                                                        {
                                                            xtype: 'container',
                                                            layout: {
                                                                type: 'vbox',
                                                                align: 'center',
                                                                pack: 'center'
                                                            },
                                                            items: [
                                                                {
                                                                    xtype: 'label',
                                                                    html: '<h2 ' + wcs + '>Da completare: ' + o.to_do + '<h2>'
                                                                },
                                                                {
                                                                    xtype: 'label',
                                                                    html: '<h2 ' + wds + '>Documento più vecchio: ' + o.oldest_doc + '<h2>'
                                                                },
                                                                {
                                                                    xtype: 'label',
                                                                    html: '<h2 ' + wes + '>Data prima scadenza: ' + o.oldest_expired + '<h2>'
                                                                }
                                                            ]
                                                        }
                                                    ]
                                                });
                                                officesCnt.add(pnl);
                                            });
                                        }
                                    }
                                });
                            },
                            height: 200,
                            id: 'ArchiveDashboardPnl',
                            itemId: 'ArchiveDashboardPnl',
                            title: 'Pannello di controllo',
                            layout: {
                                type: 'vbox',
                                align: 'stretch'
                            },
                            tabConfig: {
                                xtype: 'tab',
                                iconCls: 'icon-application'
                            },
                            items: [
                                {
                                    xtype: 'container',
                                    margin: 10,
                                    style: 'box-shadow: 2px 2px 2px #888888; background-color: #b3c9dc;',
                                    autoScroll: true,
                                    items: [
                                        {
                                            xtype: 'container',
                                            padding: 5,
                                            style: 'position:absolute !important',
                                            items: [
                                                {
                                                    xtype: 'label',
                                                    html: '<i>Totali</i>'
                                                }
                                            ]
                                        },
                                        {
                                            xtype: 'container',
                                            id: 'ArchiveDashboardTotalCnt',
                                            itemId: 'ArchiveDashboardTotalCnt',
                                            layout: {
                                                type: 'vbox',
                                                align: 'center',
                                                pack: 'center'
                                            }
                                        }
                                    ]
                                },
                                {
                                    xtype: 'container',
                                    flex: 1,
                                    layout: {
                                        type: 'hbox',
                                        align: 'stretch'
                                    },
                                    items: [
                                        {
                                            xtype: 'container',
                                            flex: 1,
                                            id: 'ArchiveDashboardUsersCnt',
                                            itemId: 'ArchiveDashboardUsersCnt',
                                            overflowY: 'auto',
                                            layout: {
                                                type: 'vbox',
                                                align: 'stretch'
                                            }
                                        },
                                        {
                                            xtype: 'container',
                                            flex: 1,
                                            id: 'ArchiveDashboardOfficesCnt',
                                            itemId: 'ArchiveDashboardOfficesCnt',
                                            overflowY: 'auto',
                                            layout: {
                                                type: 'vbox',
                                                align: 'stretch'
                                            }
                                        }
                                    ]
                                }
                            ],
                            listeners: {
                                show: {
                                    fn: me.onArchiveDashboardPnlShow,
                                    scope: me
                                },
                                render: {
                                    fn: me.onArchiveDashboardPnlRender,
                                    scope: me
                                }
                            }
                        },
                        {
                            xtype: 'panel',
                            permissible: true,
                            border: false,
                            id: 'ArchiveQueuePnl',
                            itemId: 'ArchiveQueuePnl',
                            iconCls: 'icon-page_white_magnify',
                            title: 'In corso',
                            layout: {
                                type: 'vbox',
                                align: 'stretch'
                            },
                            tabConfig: {
                                xtype: 'tab',
                                id: 'ArchiveQueueTab',
                                itemId: 'ArchiveQueueTab',
                                textAlign: 'left'
                            },
                            items: [
                                {
                                    xtype: 'menu',
                                    hidden: true,
                                    id: 'ArchiveSignMn',
                                    itemId: 'ArchiveSignMn',
                                    width: 100,
                                    items: [
                                        {
                                            xtype: 'menuitem',
                                            handler: function (item, e) {
                                                var record = Ext.getCmp('ArchiveSignMn').record,
                                                    store = Ext.getStore('ArchiveSignTypes');

                                                store.removeFilter();
                                                Ext.widget('ArchiveSignRemoteWin').show();
                                                Ext.getCmp('ArchiveSignRemoteWin').record = record;
                                                if (record.get('only_pdf') === false) {
                                                    Ext.getCmp('ArchiveSignRemoteLabel').show();
                                                    store.filter('id', 'CADES');
                                                }

                                            },
                                            iconCls: 'icon-weather_cloud',
                                            text: 'Firma remota'
                                        },
                                        {
                                            xtype: 'menuitem',
                                            handler: function (item, e) {
                                                Ext.widget('ArchiveSignWin').show();

                                                var form = Ext.getCmp('SignFilesFrm'),
                                                    record = Ext.getCmp('ArchiveSignMn').record;


                                                Ext.Ajax.request({
                                                    method: 'GET',
                                                    url: '/mc2-api/archive/document_file',
                                                    params: {
                                                        document: record.get('id')
                                                    },
                                                    success: function (res) {
                                                        var r = Ext.decode(res.responseText);
                                                        r.results.forEach(function (file) {
                                                            fu = Ext.create("Ext.form.field.File", {
                                                                fieldLabel: "<a href='/mc2-api/archive/document_file/" + file.id + "'>" + file.filename + "</a>",
                                                                name: 'file_' + file.id,
                                                                labelWidth: 200,
                                                                id: 'SignFile_' + file.id,
                                                                lastFileName: file.filename,
                                                                listeners: {
                                                                    change: function (a, field) {
                                                                        var currentValue = field.split(/(\\|\/)/g).pop();
                                                                        if (this.lastFileName !== currentValue && this.lastFileName + '.p7m' != currentValue) {
                                                                            Ext.Msg.alert('ATTENZIONE',
                                                                                'Il nome del file caricato è diverso dall\'originale. <br /> Se è corretto proseguite pure, altrimenti caricate il file corretto.'
                                                                            );
                                                                        }
                                                                    }
                                                                }
                                                            });

                                                            form.add(fu);
                                                        });
                                                        Ext.getCmp('ArchiveSignWin').center();
                                                        Ext.getCmp('ArchiveSignWin').record = record;
                                                    }
                                                });
                                            },
                                            iconCls: 'icon-computer_edit',
                                            text: 'Firma locale'
                                        }
                                    ]
                                },
                                {
                                    xtype: 'container',
                                    flex: 1,
                                    layout: 'accordion',
                                    items: [
                                        {
                                            xtype: 'panel',
                                            title: '<b>Personali</b>',
                                            layout: {
                                                type: 'vbox',
                                                align: 'stretch'
                                            },
                                            items: [
                                                {
                                                    xtype: 'gridpanel',
                                                    flex: 1,
                                                    border: false,
                                                    id: 'ArchiveDocumentsUserGrid',
                                                    itemId: 'ArchiveDocumentsUserGrid',
                                                    title: 'Da fare',
                                                    emptyText: 'Nessun documento da esaminare.',
                                                    enableColumnHide: false,
                                                    enableColumnMove: false,
                                                    sortableColumns: false,
                                                    store: 'ArchiveDocumentsUser',
                                                    columns: [
                                                        {
                                                            xtype: 'gridcolumn',
                                                            width: 90,
                                                            align: 'center',
                                                            dataIndex: 'assign_from_user_name',
                                                            text: 'Assegnato da'
                                                        },
                                                        {
                                                            xtype: 'gridcolumn',
                                                            renderer: function (value, metaData, record, rowIndex, colIndex, store, view) {
                                                                /*if (record.get('origin_id') == 1){
                                                                return value;
                                                                }*/

                                                                return record.get('short_description');
                                                            },
                                                            dataIndex: 'filename',
                                                            hideable: false,
                                                            text: 'Nome',
                                                            flex: 1
                                                        },
                                                        /*{
                                                            xtype: 'gridcolumn',
                                                            width: 132,
                                                            dataIndex: 'class_name',
                                                            text: 'Tipo di flusso'
                                                        },*/
                                                        {
                                                            xtype: 'gridcolumn',
                                                            width: 132,
                                                            dataIndex: 'dossier',
                                                            text: 'Fascicolo'
                                                        },
                                                        /*{
                                                            xtype: 'gridcolumn',
                                                            align: 'center',
                                                            dataIndex: 'model_text',
                                                            text: 'Modello'
                                                        },*/
                                                        {
                                                            xtype: 'datecolumn',
                                                            width: 80,
                                                            align: 'center',
                                                            dataIndex: 'upload_date',
                                                            hideable: false,
                                                            text: 'Aggiunto',
                                                            format: 'd/m/Y'
                                                        },
                                                        {
                                                            xtype: 'datecolumn',
                                                            width: 80,
                                                            align: 'center',
                                                            dataIndex: 'expiration_date',
                                                            hideable: false,
                                                            text: 'Scadenza',
                                                            format: 'd/m/Y'
                                                        },
                                                        {
                                                            xtype: 'actioncolumn',
                                                            width: 100,
                                                            align: 'center',
                                                            text: 'Firma',
                                                            items: [
                                                                {
                                                                    getClass: function (v, metadata, r, rowIndex, colIndex, store) {
                                                                        var action = r.get('action_sign'),
                                                                            date = r.get('action_sign_date');

                                                                        if (action) {
                                                                            if (date === null) {
                                                                                return 'icon-text_signature';
                                                                            } else {
                                                                                return 'icon-accept';
                                                                            }
                                                                        }
                                                                    },
                                                                    handler: function (view, rowIndex, colIndex, item, e, record, row) {

                                                                        if (!record.get('action_sign_date') && record.get('action_sign')) {
                                                                            e.stopEvent();
                                                                            var newX = e.xy[0];
                                                                            var newY = e.xy[1];

                                                                            Ext.getCmp('ArchiveSignMn').showAt([newX, newY]);
                                                                            Ext.getCmp('ArchiveSignMn').record = record;
                                                                        }

                                                                    }
                                                                }
                                                            ]
                                                        },
                                                        {
                                                            xtype: 'actioncolumn',
                                                            id: 'ArchiveQueueActionProtocolColumn',
                                                            itemId: 'ArchiveQueueActionProtocolColumn',
                                                            width: 100,
                                                            align: 'center',
                                                            hideable: false,
                                                            text: 'Protocollare',
                                                            items: [
                                                                {
                                                                    getClass: function (v, metadata, r, rowIndex, colIndex, store) {
                                                                        var action = r.get('action_protocol'),
                                                                            date = r.get('action_protocol_date');

                                                                        if (action) {
                                                                            if (date === null) {
                                                                                return 'icon-folder';
                                                                            } else {
                                                                                return 'icon-accept';
                                                                            }
                                                                        }
                                                                    },
                                                                    getTip: function (v, metadata, r, rowIndex, colIndex, store) {
                                                                        var action = r.get('action_protocol'),
                                                                            date = r.get('action_protocol_date');

                                                                        if (action) {
                                                                            if (date === null) {
                                                                                return 'Clicca per protocollare';
                                                                            } else {
                                                                                return 'Protocollo numero ' + r.get('action_protocol_number') + ' effettuato il: ' + Ext.util.Format.date(date, 'd/m/Y H:i');
                                                                            }
                                                                        }
                                                                    },
                                                                    handler: function (view, rowIndex, colIndex, item, e, record, row) {
                                                                        var date = record.get('action_protocol_date'),
                                                                            action = record.get('action_protocol');

                                                                        if (date === null && action) {
                                                                            var protocolWin = Ext.widget('ProtocolProtocolNewWin').show();
                                                                            Ext.getCmp('ProtocolProtocolNewWin').record = record;
                                                                            Ext.getCmp('ProtocolNewDate').setValue(new Date());
                                                                            Ext.getStore('ProtocolLinkedCorrespondentsForm').removeAll();
                                                                            Ext.getStore('ProtocolLinkedProtocolsForm').removeAll();
                                                                            Ext.getStore('ProtocolLinkedDocumentsForm').removeAll();
                                                                            Ext.getCmp('ProtocolNewLinkedDocumentsGrid').getView().disable();
                                                                            Ext.getCmp('ProtocolDocumentToolBar').disable();
                                                                            // protocolWin.fromDocument = true;

                                                                            Ext.Ajax.request({
                                                                                method: 'GET',
                                                                                url: '/mc2-api/archive/document_file',
                                                                                params: {
                                                                                    document: record.get('id')
                                                                                },
                                                                                success: function (res) {
                                                                                    var r = Ext.decode(res.responseText);
                                                                                    Ext.getStore('ProtocolLinkedDocumentsForm').add(r.results);
                                                                                }
                                                                            });

                                                                            Ext.getCmp('ArchivePnl').populateProtocol(record.get('model'));
                                                                        }
                                                                    }
                                                                }
                                                            ]
                                                        },
                                                        {
                                                            xtype: 'actioncolumn',
                                                            id: 'ArchiveQueueActionAlboColumn',
                                                            itemId: 'ArchiveQueueActionAlboColumn',
                                                            width: 100,
                                                            align: 'center',
                                                            hideable: false,
                                                            text: 'Albo',
                                                            items: [
                                                                {
                                                                    getClass: function (v, metadata, r, rowIndex, colIndex, store) {
                                                                        var action = r.get('action_albo'),
                                                                            date = r.get('action_albo_date');

                                                                        if (action) {
                                                                            if (date === null) {
                                                                                return 'icon-book_open_mark';
                                                                            } else {
                                                                                return 'icon-accept';
                                                                            }
                                                                        }
                                                                    },
                                                                    getTip: function (v, metadata, r, rowIndex, colIndex, store) {
                                                                        var action = r.get('action_albo'),
                                                                            date = r.get('action_albo_date');

                                                                        if (action) {
                                                                            if (date === null) {
                                                                                return 'Clicca per pubblicare su albo pretorio';
                                                                            } else {
                                                                                return 'Pubblicato il: ' + Ext.util.Format.date(date, 'd/m/Y H:i');
                                                                            }
                                                                        }
                                                                    },
                                                                    handler: function (view, rowIndex, colIndex, item, e, record, row) {
                                                                        var date = record.get('action_albo_date'),
                                                                            action = record.get('action_albo');

                                                                        if (date === null && action) {
                                                                            Ext.widget('AlboPublicationEditWin').show();
                                                                            Ext.getStore('AlboLinkedDocumentsForm').removeAll();
                                                                            Ext.getCmp('AlboPublicationEditLinkedDocumentsGrid').getView().disable();
                                                                            Ext.getCmp('AlboPublicationEditLinkedTlb').disable();

                                                                            Ext.getStore('ArchiveDocumentFiles').load({
                                                                                params: {
                                                                                    document: record.get('id')
                                                                                },
                                                                                callback: function (res) {
                                                                                    Ext.getStore('AlboLinkedDocumentsForm').add(res);
                                                                                    Ext.getCmp('AlboPublicationEditLinkedDocumentsGrid').disable();
                                                                                }
                                                                            });
                                                                            Ext.getCmp('ArchivePnl').populateAlbo(record.get('model'));
                                                                        }
                                                                    }
                                                                }
                                                            ]
                                                        },
                                                        {
                                                            xtype: 'actioncolumn',
                                                            id: 'ArchiveQueueActionTrasparenzaColumn',
                                                            itemId: 'ArchiveQueueActionTrasparenzaColumn',
                                                            width: 100,
                                                            align: 'center',
                                                            hideable: false,
                                                            text: 'Trasparenza',
                                                            items: [
                                                                {
                                                                    getClass: function (v, metadata, r, rowIndex, colIndex, store) {
                                                                        var action = r.get('action_trasparenza'),
                                                                            date = r.get('action_trasparenza_date');

                                                                        if (action) {
                                                                            if (date === null) {
                                                                                return 'icon-world_edit';
                                                                            } else {
                                                                                return 'icon-accept';
                                                                            }
                                                                        }
                                                                    },
                                                                    getTip: function (v, metadata, r, rowIndex, colIndex, store) {
                                                                        var action = r.get('action_trasparenza'),
                                                                            date = r.get('action_trasparenza_date');

                                                                        if (action) {
                                                                            if (date === null) {
                                                                                return 'Clicca per allegare a trasparenza';
                                                                            } else {
                                                                                return 'Allegato il: ' + Ext.util.Format.date(date, 'd/m/Y H:i');
                                                                            }
                                                                        }
                                                                    },
                                                                    handler: function (view, rowIndex, colIndex, item, e, record, row) {
                                                                        var date = record.get('action_trasparenza_date'),
                                                                            action = record.get('action_trasparenza');

                                                                        if (date === null && action) {
                                                                            Ext.widget('TrasparenzaVoicePickerWin').show();
                                                                            Ext.getCmp('TrasparenzaVoicePickerWin').record = record;
                                                                            Ext.getStore('ArchiveDocumentFiles').load({
                                                                                params: {
                                                                                    document: record.get('id')
                                                                                }
                                                                            });

                                                                            Ext.getCmp('ArchivePnl').populateTrasparenza(record.get('model'));
                                                                        }
                                                                    }
                                                                }
                                                            ]
                                                        },
                                                        {
                                                            xtype: 'actioncolumn',
                                                            id: 'ArchiveQueueActionArchiveColumn',
                                                            itemId: 'ArchiveQueueActionArchiveColumn',
                                                            width: 100,
                                                            align: 'center',
                                                            hideable: false,
                                                            text: 'Conservazione',
                                                            items: [
                                                                {
                                                                    getClass: function (v, metadata, r, rowIndex, colIndex, store) {
                                                                        var date = r.get('action_archive_date'),
                                                                            action = r.get('action_archive');

                                                                        if (action) {
                                                                            if (date === null) {
                                                                                return 'icon-database';
                                                                            } else {
                                                                                return 'icon-accept';
                                                                            }
                                                                        }
                                                                    },
                                                                    getTip: function (v, metadata, r, rowIndex, colIndex, store) {
                                                                        var date = r.get('action_archive_date'),
                                                                            conserved = r.get('conserved');

                                                                        if (date === null) {
                                                                            return 'Clicca per archiviare';
                                                                        } else {
                                                                            return 'Archiviato il: ' + Ext.util.Format.date(date, 'd/m/Y H:i');
                                                                        }
                                                                    },
                                                                    handler: function (view, rowIndex, colIndex, item, e, record, row) {
                                                                        /*
                                                                        var date = record.get('action_archive_date');

                                                                        if (date === null) {
                                                                        //Ext.widget('ArchiveDocumentArchiveWin').show();
                                                                        //Ext.getCmp('ArchiveDocumentArchiveWin').setTitle('Archiviazione ' + record.get('filename'));
                                                                        //Ext.getCmp('ArchiveArchiveForm').loadRecord(record);
                                                                        //Ext.getCmp('ArchiveArchiveFile').setValue(record.get('filename'));
                                                                        //Ext.getCmp('ArchiveArchiveId').setValue(record.get('id'));
                                                                        Ext.Msg.confirm('CONSERVAZIONE DIGITALE','Tutti i documenti di questo flusso verranno caricati in conservazione. Confermare?',
                                                                        function(btnText, sInput){
                                                                            if(btnText === 'yes'){
                                                                                Ext.Ajax.request({
                                                                                    url: '/mc2-api/archive/document/' + record.get('id') + '/remote',
                                                                                    success: function(res, r){
                                                                                        r = Ext.decode(res.responseText);
                                                                                        if(r.success === true){
                                                                                            Ext.getCmp('ArchiveFilterForm').applyFilter();
                                                                                        } else {
                                                                                            Ext.Msg.alert('ERRORE', r.message);
                                                                                        }
                                                                                    }
                                                                                });
                                                                            }
                                                                        });
                                                                    }
                                                                    */

                                                                        // Ext.getCmp('ArchiveDocumentsQueueGrid').getSelectionModel().select(rowIndex);
                                                                        var amfs = Ext.getStore('ArchiveMetadataFiles'),
                                                                            id = record.get('id');

                                                                        amfs.removeAll();
                                                                        if (record.get('action_archive')) {
                                                                            if (record.get('action_archive_date')) {
                                                                                Ext.Ajax.request({
                                                                                    url: '/mc2-api/archive/document_file?document=' + id,
                                                                                    success: function (r) {
                                                                                        Ext.widget('ArchiveMetadataFileWin').show();
                                                                                        Ext.getCmp('ArchiveMetadataFileWin').record = record;
                                                                                        Ext.getCmp('ArchiveMetadataFileTb').disable();
                                                                                        Ext.getCmp('ArchiveMetadataFileGrid').disable();
                                                                                        res = Ext.decode(r.responseText);
                                                                                        Ext.each(res.results, function (a) {
                                                                                            a.metadata.name = a.filename;
                                                                                            amfs.add(a.metadata);
                                                                                        });
                                                                                    }
                                                                                });
                                                                            } else {
                                                                                Ext.widget('ArchiveMetadataFileWin').show();
                                                                                Ext.getCmp('ArchiveMetadataFileWin').record = record;
                                                                            }
                                                                        }
                                                                    }
                                                                }
                                                            ]
                                                        }
                                                    ],
                                                    viewConfig: {
                                                        getRowClass: function (record, rowIndex, rowParams, store) {
                                                            if (record.get('to_check') === true) {
                                                                return 'archive-check';
                                                            }
                                                        }
                                                    },
                                                    listeners: {
                                                        itemcontextmenu: {
                                                            fn: me.onArchiveDocumentsQueueGridItemContextMenu,
                                                            scope: me
                                                        },
                                                        itemdblclick: {
                                                            fn: me.onArchiveDocumentsUserGridItemDblClick,
                                                            scope: me
                                                        }
                                                    },
                                                    dockedItems: [
                                                        {
                                                            xtype: 'toolbar',
                                                            dock: 'top',
                                                            items: [
                                                                {
                                                                    xtype: 'textfield',
                                                                    width: 200,
                                                                    fieldLabel: '',
                                                                    emptyText: 'Cerca ...',
                                                                    listeners: {
                                                                        change: {
                                                                            fn: me.onTextfieldChange1,
                                                                            delay: 300,
                                                                            buffer: 300,
                                                                            scope: me
                                                                        }
                                                                    }
                                                                },
                                                                {
                                                                    xtype: 'tbspacer',
                                                                    flex: 1
                                                                }
                                                            ]
                                                        },
                                                        {
                                                            xtype: 'pagingtoolbar',
                                                            dock: 'bottom',
                                                            width: 360,
                                                            displayInfo: true,
                                                            store: 'ArchiveDocumentsUser'
                                                        }
                                                    ]
                                                },
                                                {
                                                    xtype: 'gridpanel',
                                                    flex: 1,
                                                    title: 'Da definire',
                                                    store: 'ArchiveMailsUser',
                                                    columns: [
                                                        {
                                                            xtype: 'gridcolumn',
                                                            width: 90,
                                                            align: 'center',
                                                            dataIndex: 'assign_from',
                                                            text: 'Assegnato da'
                                                        },
                                                        {
                                                            xtype: 'gridcolumn',
                                                            width: 200,
                                                            dataIndex: 'from',
                                                            text: 'In arrivo da'
                                                        },
                                                        {
                                                            xtype: 'gridcolumn',
                                                            dataIndex: 'subject',
                                                            text: 'Oggetto',
                                                            flex: 1
                                                        },
                                                        {
                                                            xtype: 'gridcolumn',
                                                            dataIndex: 'account_text',
                                                            text: 'Account'
                                                        },
                                                        {
                                                            xtype: 'gridcolumn',
                                                            renderer: function (value, metaData, record, rowIndex, colIndex, store, view) {
                                                                if (value > 0) {
                                                                    return '<img src="./resources/icons/attach.png">';
                                                                }
                                                                return '';
                                                            },
                                                            width: 60,
                                                            dataIndex: 'attachments',
                                                            text: 'Allegati'
                                                        },
                                                        {
                                                            xtype: 'gridcolumn',
                                                            renderer: function (value, metaData, record, rowIndex, colIndex, store, view) {
                                                                return mc2ui.app.convertTimestamp(value);
                                                            },
                                                            dataIndex: 'date',
                                                            text: 'Data mail'
                                                        },
                                                        {
                                                            xtype: 'actioncolumn',
                                                            id: 'ArchiveActionArchivedCol1',
                                                            itemId: 'ArchiveActionArchivedCol1',
                                                            width: 25,
                                                            align: 'center',
                                                            items: [
                                                                {
                                                                    handler: function (view, rowIndex, colIndex, item, e, record, row) {
                                                                        Ext.Ajax.request({
                                                                            method: 'PUT',
                                                                            url: '/mc2-api/archive/mail/mail/' + record.get('id'),
                                                                            params: {
                                                                                deleted: true
                                                                            },
                                                                            success: function () {
                                                                                Ext.getCmp('ArchiveMailPnl').countMail();
                                                                                Ext.getStore('ArchiveMailsUser').load();
                                                                            }
                                                                        });


                                                                    },
                                                                    iconCls: 'icon-package_down',
                                                                    tooltip: 'Archivia'
                                                                }
                                                            ]
                                                        }
                                                    ],
                                                    viewConfig: {
                                                        listeners: {
                                                            itemdblclick: {
                                                                fn: me.onViewItemDblClick1,
                                                                scope: me
                                                            },
                                                            itemcontextmenu: {
                                                                fn: me.onViewItemContextMenu1,
                                                                scope: me
                                                            }
                                                        }
                                                    },
                                                    dockedItems: [
                                                        {
                                                            xtype: 'toolbar',
                                                            dock: 'top',
                                                            items: [
                                                                {
                                                                    xtype: 'textfield',
                                                                    fieldLabel: '',
                                                                    emptyText: 'Cerca ...',
                                                                    listeners: {
                                                                        change: {
                                                                            fn: me.onTextfieldChange2,
                                                                            delay: 300,
                                                                            buffer: 300,
                                                                            scope: me
                                                                        }
                                                                    }
                                                                },
                                                                {
                                                                    xtype: 'tbspacer',
                                                                    flex: 1
                                                                }
                                                            ]
                                                        },
                                                        {
                                                            xtype: 'pagingtoolbar',
                                                            dock: 'bottom',
                                                            width: 360,
                                                            displayInfo: true,
                                                            store: 'ArchiveMailsUser'
                                                        }
                                                    ]
                                                }
                                            ]
                                        },
                                        {
                                            xtype: 'panel',
                                            title: '<b>Uffici</b>',
                                            layout: {
                                                type: 'vbox',
                                                align: 'stretch'
                                            },
                                            items: [
                                                {
                                                    xtype: 'gridpanel',
                                                    flex: 1,
                                                    border: false,
                                                    id: 'ArchiveDocumentsOfficeGrid',
                                                    itemId: 'ArchiveDocumentsOfficeGrid',
                                                    title: 'Da fare',
                                                    emptyText: 'Nessun documento da esaminare.',
                                                    enableColumnHide: false,
                                                    enableColumnMove: false,
                                                    sortableColumns: false,
                                                    store: 'ArchiveDocumentsOffice',
                                                    columns: [
                                                        {
                                                            xtype: 'gridcolumn',
                                                            width: 90,
                                                            align: 'center',
                                                            dataIndex: 'assign_from_user_name',
                                                            text: 'Assegnato da'
                                                        },
                                                        {
                                                            xtype: 'gridcolumn',
                                                            renderer: function (value, metaData, record, rowIndex, colIndex, store, view) {
                                                                /*if (record.get('origin_id') == 1){
                                                                return value;
                                                                }*/

                                                                return record.get('short_description');
                                                            },
                                                            dataIndex: 'filename',
                                                            hideable: false,
                                                            text: 'Nome',
                                                            flex: 1
                                                        },
                                                        /*{
                                                            xtype: 'gridcolumn',
                                                            width: 132,
                                                            dataIndex: 'class_name',
                                                            text: 'Tipo di flusso'
                                                        },*/
                                                        {
                                                            xtype: 'gridcolumn',
                                                            width: 132,
                                                            dataIndex: 'dossier',
                                                            text: 'Fascicolo'
                                                        },
                                                        /*{
                                                            xtype: 'gridcolumn',
                                                            align: 'center',
                                                            dataIndex: 'model_text',
                                                            text: 'Modello'
                                                        },*/
                                                        {
                                                            xtype: 'datecolumn',
                                                            width: 80,
                                                            align: 'center',
                                                            dataIndex: 'upload_date',
                                                            hideable: false,
                                                            text: 'Aggiunto',
                                                            format: 'd/m/Y'
                                                        },
                                                        {
                                                            xtype: 'datecolumn',
                                                            width: 80,
                                                            align: 'center',
                                                            dataIndex: 'expiration_date',
                                                            hideable: false,
                                                            text: 'Scadenza',
                                                            format: 'd/m/Y'
                                                        },
                                                        {
                                                            xtype: 'actioncolumn',
                                                            width: 100,
                                                            align: 'center',
                                                            text: 'Firma',
                                                            items: [
                                                                {
                                                                    getClass: function (v, metadata, r, rowIndex, colIndex, store) {
                                                                        var action = r.get('action_sign'),
                                                                            date = r.get('action_sign_date');

                                                                        if (action) {
                                                                            if (date === null) {
                                                                                return 'icon-text_signature';
                                                                            } else {
                                                                                return 'icon-accept';
                                                                            }
                                                                        }
                                                                    },
                                                                    handler: function (view, rowIndex, colIndex, item, e, record, row) {

                                                                        if (!record.get('action_sign_date')) {
                                                                            e.stopEvent();
                                                                            var newX = e.xy[0];
                                                                            var newY = e.xy[1];

                                                                            Ext.getCmp('ArchiveSignMn').showAt([newX, newY]);
                                                                            Ext.getCmp('ArchiveSignMn').record = record;

                                                                        }

                                                                    }
                                                                }
                                                            ]
                                                        },
                                                        {
                                                            xtype: 'actioncolumn',
                                                            id: 'ArchiveQueueActionProtocolColumn1',
                                                            itemId: 'ArchiveQueueActionProtocolColumn',
                                                            width: 100,
                                                            align: 'center',
                                                            hideable: false,
                                                            text: 'Protocollare',
                                                            items: [
                                                                {
                                                                    getClass: function (v, metadata, r, rowIndex, colIndex, store) {
                                                                        var action = r.get('action_protocol'),
                                                                            date = r.get('action_protocol_date');

                                                                        if (action) {
                                                                            if (date === null) {
                                                                                return 'icon-folder';
                                                                            } else {
                                                                                return 'icon-accept';
                                                                            }
                                                                        }
                                                                    },
                                                                    getTip: function (v, metadata, r, rowIndex, colIndex, store) {
                                                                        var action = r.get('action_protocol'),
                                                                            date = r.get('action_protocol_date');

                                                                        if (action) {
                                                                            if (date === null) {
                                                                                return 'Clicca per protocollare';
                                                                            } else {
                                                                                return 'Protocollo numero ' + r.get('action_protocol_number') + ' effettuato il: ' + Ext.util.Format.date(date, 'd/m/Y H:i');
                                                                            }
                                                                        }
                                                                    },
                                                                    handler: function (view, rowIndex, colIndex, item, e, record, row) {
                                                                        var date = record.get('action_protocol_date');

                                                                        if (date === null) {
                                                                            var protocolWin = Ext.widget('ProtocolProtocolNewWin').show();
                                                                            Ext.getCmp('ProtocolProtocolNewWin').record = record;
                                                                            Ext.getCmp('ProtocolNewDate').setValue(new Date());
                                                                            Ext.getStore('ProtocolLinkedCorrespondentsForm').removeAll();
                                                                            Ext.getStore('ProtocolLinkedProtocolsForm').removeAll();
                                                                            Ext.getStore('ProtocolLinkedDocumentsForm').removeAll();
                                                                            Ext.getCmp('ProtocolNewLinkedDocumentsGrid').getView().disable();
                                                                            Ext.getCmp('ProtocolDocumentToolBar').disable();
                                                                            // protocolWin.fromDocument = true;

                                                                            Ext.Ajax.request({
                                                                                method: 'GET',
                                                                                url: '/mc2-api/archive/document_file',
                                                                                params: {
                                                                                    document: record.get('id')
                                                                                },
                                                                                success: function (res) {
                                                                                    var r = Ext.decode(res.responseText);
                                                                                    Ext.getStore('ProtocolLinkedDocumentsForm').add(r.results);
                                                                                }
                                                                            });

                                                                            Ext.getCmp('ArchivePnl').populateProtocol(record.get('model'));

                                                                        }
                                                                    }
                                                                }
                                                            ]
                                                        },
                                                        {
                                                            xtype: 'actioncolumn',
                                                            id: 'ArchiveQueueActionAlboColumn1',
                                                            itemId: 'ArchiveQueueActionAlboColumn',
                                                            width: 100,
                                                            align: 'center',
                                                            hideable: false,
                                                            text: 'Albo',
                                                            items: [
                                                                {
                                                                    getClass: function (v, metadata, r, rowIndex, colIndex, store) {
                                                                        var action = r.get('action_albo'),
                                                                            date = r.get('action_albo_date');

                                                                        if (action) {
                                                                            if (date === null) {
                                                                                return 'icon-book_open_mark';
                                                                            } else {
                                                                                return 'icon-accept';
                                                                            }
                                                                        }
                                                                    },
                                                                    getTip: function (v, metadata, r, rowIndex, colIndex, store) {
                                                                        var action = r.get('action_albo'),
                                                                            date = r.get('action_albo_date');

                                                                        if (action) {
                                                                            if (date === null) {
                                                                                return 'Clicca per pubblicare su albo pretorio';
                                                                            } else {
                                                                                return 'Pubblicato il: ' + Ext.util.Format.date(date, 'd/m/Y H:i');
                                                                            }
                                                                        }
                                                                    },
                                                                    handler: function (view, rowIndex, colIndex, item, e, record, row) {
                                                                        var date = record.get('action_albo_date');

                                                                        if (date === null) {
                                                                            Ext.widget('AlboPublicationEditWin').show();
                                                                            Ext.getStore('AlboLinkedDocumentsForm').removeAll();
                                                                            Ext.getCmp('AlboPublicationEditLinkedDocumentsGrid').getView().disable();
                                                                            Ext.getCmp('AlboPublicationEditLinkedTlb').disable();

                                                                            Ext.getStore('ArchiveDocumentFiles').load({
                                                                                params: {
                                                                                    document: record.get('id')
                                                                                },
                                                                                callback: function (res) {
                                                                                    Ext.getStore('AlboLinkedDocumentsForm').add(res);
                                                                                    Ext.getCmp('AlboPublicationEditLinkedDocumentsGrid').disable();
                                                                                }
                                                                            });

                                                                            Ext.getCmp('ArchivePnl').populateAlbo(record.get('model'));
                                                                        }
                                                                    }
                                                                }
                                                            ]
                                                        },
                                                        {
                                                            xtype: 'actioncolumn',
                                                            id: 'ArchiveQueueActionTrasparenzaColumn1',
                                                            itemId: 'ArchiveQueueActionTrasparenzaColumn',
                                                            width: 100,
                                                            align: 'center',
                                                            hideable: false,
                                                            text: 'Trasparenza',
                                                            items: [
                                                                {
                                                                    getClass: function (v, metadata, r, rowIndex, colIndex, store) {
                                                                        var action = r.get('action_trasparenza'),
                                                                            date = r.get('action_trasparenza_date');

                                                                        if (action) {
                                                                            if (date === null) {
                                                                                return 'icon-world_edit';
                                                                            } else {
                                                                                return 'icon-accept';
                                                                            }
                                                                        }
                                                                    },
                                                                    getTip: function (v, metadata, r, rowIndex, colIndex, store) {
                                                                        var action = r.get('action_trasparenza'),
                                                                            date = r.get('action_trasparenza_date');

                                                                        if (action) {
                                                                            if (date === null) {
                                                                                return 'Clicca per allegare a trasparenza';
                                                                            } else {
                                                                                return 'Allegato il: ' + Ext.util.Format.date(date, 'd/m/Y H:i');
                                                                            }
                                                                        }
                                                                    },
                                                                    handler: function (view, rowIndex, colIndex, item, e, record, row) {
                                                                        var date = record.get('action_trasparenza_date');

                                                                        if (date === null) {
                                                                            Ext.widget('TrasparenzaVoicePickerWin').show();
                                                                            Ext.getCmp('TrasparenzaVoicePickerWin').record = record;
                                                                            Ext.getStore('ArchiveDocumentFiles').load({
                                                                                params: {
                                                                                    document: record.get('id')
                                                                                }
                                                                            });

                                                                            Ext.getCmp('ArchivePnl').populateTrasparenza(record.get('model'));
                                                                        }
                                                                    }
                                                                }
                                                            ]
                                                        },
                                                        {
                                                            xtype: 'actioncolumn',
                                                            id: 'ArchiveQueueActionArchiveColumn1',
                                                            itemId: 'ArchiveQueueActionArchiveColumn',
                                                            width: 100,
                                                            align: 'center',
                                                            hideable: false,
                                                            text: 'Conservazione',
                                                            items: [
                                                                {
                                                                    getClass: function (v, metadata, r, rowIndex, colIndex, store) {
                                                                        var date = r.get('action_archive_date'),
                                                                            action = r.get('action_archive');

                                                                        if (action) {
                                                                            if (date === null) {
                                                                                return 'icon-database';
                                                                            } else {
                                                                                return 'icon-accept';
                                                                            }
                                                                        }
                                                                    },
                                                                    getTip: function (v, metadata, r, rowIndex, colIndex, store) {
                                                                        var date = r.get('action_archive_date'),
                                                                            conserved = r.get('conserved');

                                                                        if (date === null) {
                                                                            return 'Clicca per archiviare';
                                                                        } else {
                                                                            return 'Archiviato il: ' + Ext.util.Format.date(date, 'd/m/Y H:i');
                                                                        }
                                                                    },
                                                                    handler: function (view, rowIndex, colIndex, item, e, record, row) {
                                                                        /*
                                                                        var date = record.get('action_archive_date');

                                                                        if (date === null) {
                                                                        //Ext.widget('ArchiveDocumentArchiveWin').show();
                                                                        //Ext.getCmp('ArchiveDocumentArchiveWin').setTitle('Archiviazione ' + record.get('filename'));
                                                                        //Ext.getCmp('ArchiveArchiveForm').loadRecord(record);
                                                                        //Ext.getCmp('ArchiveArchiveFile').setValue(record.get('filename'));
                                                                        //Ext.getCmp('ArchiveArchiveId').setValue(record.get('id'));
                                                                        Ext.Msg.confirm('CONSERVAZIONE DIGITALE','Tutti i documenti di questo flusso verranno caricati in conservazione. Confermare?',
                                                                        function(btnText, sInput){
                                                                            if(btnText === 'yes'){
                                                                                Ext.Ajax.request({
                                                                                    url: '/mc2-api/archive/document/' + record.get('id') + '/remote',
                                                                                    success: function(res, r){
                                                                                        r = Ext.decode(res.responseText);
                                                                                        if(r.success === true){
                                                                                            Ext.getCmp('ArchiveFilterForm').applyFilter();
                                                                                        } else {
                                                                                            Ext.Msg.alert('ERRORE', r.message);
                                                                                        }
                                                                                    }
                                                                                });
                                                                            }
                                                                        });
                                                                    }
                                                                    */

                                                                        var amfs = Ext.getStore('ArchiveMetadataFiles'),
                                                                            id = record.get('id');

                                                                        amfs.removeAll();
                                                                        Ext.Ajax.request({
                                                                            url: '/mc2-api/archive/document_file?document=' + id,
                                                                            success: function (r) {
                                                                                Ext.widget('ArchiveMetadataFileWin').show();
                                                                                Ext.getCmp('ArchiveMetadataFileWin').record = record;
                                                                                res = Ext.decode(r.responseText);
                                                                                Ext.each(res.results, function (a) {
                                                                                    amfs.add(a.metadata);
                                                                                });
                                                                            }
                                                                        });
                                                                    }
                                                                }
                                                            ]
                                                        }
                                                    ],
                                                    viewConfig: {
                                                        getRowClass: function (record, rowIndex, rowParams, store) {
                                                            if (record.get('to_check') === true) {
                                                                return 'archive-check';
                                                            }
                                                        }
                                                    },
                                                    listeners: {
                                                        itemcontextmenu: {
                                                            fn: me.onArchiveDocumentsQueueGridItemContextMenu1,
                                                            scope: me
                                                        },
                                                        itemdblclick: {
                                                            fn: me.onArchiveDocumentsOfficeGridItemDblClick,
                                                            scope: me
                                                        }
                                                    },
                                                    dockedItems: [
                                                        {
                                                            xtype: 'toolbar',
                                                            dock: 'top',
                                                            items: [
                                                                {
                                                                    xtype: 'textfield',
                                                                    width: 200,
                                                                    fieldLabel: '',
                                                                    emptyText: 'Cerca ...',
                                                                    listeners: {
                                                                        change: {
                                                                            fn: me.onTextfieldChange11,
                                                                            delay: 300,
                                                                            buffer: 300,
                                                                            scope: me
                                                                        }
                                                                    }
                                                                },
                                                                {
                                                                    xtype: 'tbspacer',
                                                                    flex: 1
                                                                },
                                                                {
                                                                    xtype: 'button',
                                                                    handler: function (button, e) {
                                                                        Ext.getStore('ArchiveDocumentsOffice').load();
                                                                    },
                                                                    iconCls: 'icon-arrow_refresh',
                                                                    text: ''
                                                                }
                                                            ]
                                                        }
                                                    ]
                                                },
                                                {
                                                    xtype: 'gridpanel',
                                                    flex: 1,
                                                    title: 'Da definire',
                                                    store: 'ArchiveMailsOffice',
                                                    columns: [
                                                        {
                                                            xtype: 'gridcolumn',
                                                            width: 90,
                                                            align: 'center',
                                                            dataIndex: 'assign_from',
                                                            text: 'Assegnato da'
                                                        },
                                                        {
                                                            xtype: 'gridcolumn',
                                                            width: 200,
                                                            dataIndex: 'from',
                                                            text: 'In arrivo da'
                                                        },
                                                        {
                                                            xtype: 'gridcolumn',
                                                            dataIndex: 'subject',
                                                            text: 'Oggeto',
                                                            flex: 1
                                                        },
                                                        {
                                                            xtype: 'gridcolumn',
                                                            dataIndex: 'account_text',
                                                            text: 'Account'
                                                        },
                                                        {
                                                            xtype: 'gridcolumn',
                                                            renderer: function (value, metaData, record, rowIndex, colIndex, store, view) {
                                                                if (value > 0) {
                                                                    return '<img src="./resources/icons/attach.png">';
                                                                }
                                                                return '';
                                                            },
                                                            width: 60,
                                                            dataIndex: 'attachments',
                                                            text: 'Allegati'
                                                        },
                                                        {
                                                            xtype: 'gridcolumn',
                                                            renderer: function (value, metaData, record, rowIndex, colIndex, store, view) {
                                                                return mc2ui.app.convertTimestamp(value);
                                                            },
                                                            dataIndex: 'date',
                                                            text: 'Data mail'
                                                        },
                                                        {
                                                            xtype: 'actioncolumn',
                                                            id: 'ArchiveActionArchivedCol2',
                                                            itemId: 'ArchiveActionArchivedCol2',
                                                            width: 25,
                                                            align: 'center',
                                                            items: [
                                                                {
                                                                    handler: function (view, rowIndex, colIndex, item, e, record, row) {
                                                                        var store = Ext.getStore('ArchiveMailsOffice');
                                                                        mc2ui.app.mail = record;
                                                                        record.set('deleted', true);

                                                                        store.sync({
                                                                            callback: function () {
                                                                                Ext.getCmp('ArchiveMailPnl').countMail();
                                                                                store.load();
                                                                            }
                                                                        });


                                                                    },
                                                                    iconCls: 'icon-package_down',
                                                                    tooltip: 'Archivia'
                                                                }
                                                            ]
                                                        }
                                                    ],
                                                    viewConfig: {
                                                        listeners: {
                                                            itemdblclick: {
                                                                fn: me.onViewItemDblClick11,
                                                                scope: me
                                                            },
                                                            itemcontextmenu: {
                                                                fn: me.onViewItemContextMenu11,
                                                                scope: me
                                                            }
                                                        }
                                                    },
                                                    dockedItems: [
                                                        {
                                                            xtype: 'toolbar',
                                                            dock: 'top',
                                                            items: [
                                                                {
                                                                    xtype: 'textfield',
                                                                    fieldLabel: '',
                                                                    emptyText: 'Cerca ...',
                                                                    listeners: {
                                                                        change: {
                                                                            fn: me.onTextfieldChange21,
                                                                            delay: 300,
                                                                            buffer: 300,
                                                                            scope: me
                                                                        }
                                                                    }
                                                                },
                                                                {
                                                                    xtype: 'tbspacer',
                                                                    flex: 1
                                                                },
                                                                {
                                                                    xtype: 'button',
                                                                    handler: function (button, e) {
                                                                        Ext.getStore('ArchiveMailsOffice').load();
                                                                    },
                                                                    iconCls: 'icon-arrow_refresh',
                                                                    text: ''
                                                                }
                                                            ]
                                                        }
                                                    ]
                                                }
                                            ]
                                        }
                                    ]
                                }
                            ]
                        },
                        {
                            xtype: 'panel',
                            id: 'ArchiveDossierPnl',
                            itemId: 'ArchiveDossierPnl',
                            layout: 'fit',
                            title: 'Fascicoli',
                            tabConfig: {
                                xtype: 'tab',
                                id: 'ArchiveDossierTab',
                                itemId: 'ArchiveDossierTab',
                                iconCls: 'icon-book_addresses'
                            },
                            items: [
                                {
                                    xtype: 'gridpanel',
                                    title: '',
                                    store: 'ArchiveDocumentDossier',
                                    viewConfig: {
                                        getRowClass: function (record, rowIndex, rowParams, store) {

                                            var c = record.get('completed').split('/');
                                            if (parseInt(c[0]) == parseInt(c[1])) {
                                                return 'archive-completed';
                                            }
                                            return '';
                                        },
                                        listeners: {
                                            itemdblclick: {
                                                fn: me.onViewItemClick1,
                                                scope: me
                                            }
                                        }
                                    },
                                    columns: [
                                        {
                                            xtype: 'actioncolumn',
                                            width: 30,
                                            align: 'center',
                                            items: [
                                                {
                                                    getClass: function (v, metadata, r, rowIndex, colIndex, store) {
                                                        if (r.get('dossier') === true) {
                                                            return 'icon-folder_page';
                                                        } else {
                                                            return 'icon-table_multiple';
                                                        }
                                                    }
                                                }
                                            ]
                                        },
                                        {
                                            xtype: 'gridcolumn',
                                            dataIndex: 'name',
                                            text: 'Nome',
                                            flex: 1
                                        },
                                        {
                                            xtype: 'datecolumn',
                                            align: 'center',
                                            dataIndex: 'insert_date',
                                            text: 'Data inserimento',
                                            format: 'm/j/Y'
                                        },
                                        {
                                            xtype: 'datecolumn',
                                            width: 114,
                                            align: 'center',
                                            dataIndex: 'modified_date',
                                            text: 'Data ultima modifica',
                                            format: 'm/j/Y'
                                        },
                                        {
                                            xtype: 'datecolumn',
                                            align: 'center',
                                            dataIndex: 'first_expiration_date',
                                            text: 'Prima scadenza',
                                            format: 'm/j/Y'
                                        },
                                        {
                                            xtype: 'datecolumn',
                                            align: 'center',
                                            dataIndex: 'last_expiration_date',
                                            text: 'Ultima scadenza',
                                            format: 'm/j/Y'
                                        },
                                        {
                                            xtype: 'numbercolumn',
                                            width: 80,
                                            align: 'center',
                                            dataIndex: 'files',
                                            text: 'Documenti',
                                            format: '00'
                                        },
                                        {
                                            xtype: 'gridcolumn',
                                            width: 80,
                                            align: 'center',
                                            dataIndex: 'completed',
                                            text: 'Completati'
                                        },
                                        {
                                            xtype: 'actioncolumn',
                                            width: 50,
                                            align: 'center',
                                            tooltip: 'Firma massiva',
                                            items: [
                                                {
                                                    handler: function (view, rowIndex, colIndex, item, e, record, row) {
                                                        Ext.widget('ArchiveMassiveSignWin').show();
                                                        Ext.getCmp('ArchiveMassiveSignWin').record = record;

                                                        var dossierPath = '';
                                                        Ext.each(Ext.getCmp('ArchiveDossierPnl').paginazione, function (val) {
                                                            if (dossierPath)
                                                                dossierPath = dossierPath + '/' + val.text;
                                                            else
                                                                dossierPath = val.text;
                                                        });

                                                        if (dossierPath)
                                                            dossierPath = dossierPath + '/' + record.get('name');
                                                        else
                                                            dossierPath = record.get('name');

                                                        Ext.getStore('ArchiveDashboard').load({
                                                            params: {
                                                                dossier: dossierPath
                                                            },
                                                            callback: function (f, res) {
                                                                var r = Ext.decode(res.response.responseText);
                                                                Ext.each(r.results, function (v) {
                                                                    if (!v.action_sign_date) {
                                                                        var doc = Ext.getStore('ArchiveDashboard').getById(v.id);
                                                                        Ext.getCmp('ArchiveMassiveSignGrid').getSelectionModel().select(doc, true);
                                                                    }
                                                                });
                                                            }
                                                        });
                                                    },
                                                    iconCls: 'icon-bookmark_edit'
                                                },
                                                {
                                                    handler: function (view, rowIndex, colIndex, item, e, record, row) {
                                                        if (record.get('dossier') === true) {
                                                            Ext.widget('ArchiveDashboardWin').show();

                                                            var dossierPath = '';
                                                            Ext.each(Ext.getCmp('ArchiveDossierPnl').paginazione, function (val) {
                                                                if (dossierPath)
                                                                    dossierPath = dossierPath + '/' + val.text;
                                                                else
                                                                    dossierPath = val.text;
                                                            });

                                                            if (dossierPath)
                                                                dossierPath = dossierPath + '/' + record.get('name');
                                                            else
                                                                dossierPath = record.get('name');

                                                            Ext.getStore('ArchiveDashboard').load({
                                                                params: {
                                                                    'dossier': dossierPath
                                                                }
                                                            });
                                                        } else {
                                                            Ext.getCmp('ArchivePnl').viewDocument(record);
                                                        }

                                                    },
                                                    iconCls: 'icon-information'
                                                }
                                            ]
                                        }
                                    ],
                                    dockedItems: [
                                        {
                                            xtype: 'pagingtoolbar',
                                            dock: 'bottom',
                                            width: 360,
                                            displayInfo: true,
                                            store: 'ArchiveDocumentDossier'
                                        }
                                    ]
                                }
                            ],
                            dockedItems: [
                                {
                                    xtype: 'toolbar',
                                    dock: 'top',
                                    items: [
                                        {
                                            xtype: 'container',
                                            reloadPagination: function () {
                                                while (Ext.getCmp('paginazioneCnt').items.items.length > 0) {
                                                    Ext.getCmp('paginazioneCnt').remove(Ext.getCmp('paginazioneCnt').items.items[0]);
                                                }

                                                Ext.getCmp('paginazioneCnt').add(
                                                    Ext.create('Ext.Button', {
                                                        text: 'Fascicoli',
                                                        id_mc: null,
                                                        handler: function () {
                                                            Ext.getCmp('ArchiveDossierPnl').paginazione = [];
                                                            Ext.getCmp('paginazioneCnt').reloadPagination();
                                                            Ext.getStore('ArchiveDocumentDossier').load();
                                                        }
                                                    })
                                                );

                                                Ext.each(Ext.getCmp('ArchiveDossierPnl').paginazione, function (val, index) {
                                                    Ext.getCmp('paginazioneCnt').add(Ext.create('Ext.form.Label', {
                                                        text: ' / '
                                                    }));
                                                    Ext.getCmp('paginazioneCnt').add(
                                                        Ext.create('Ext.Button', {
                                                            text: val.text,
                                                            id_mc: val.id,
                                                            handler: function () {
                                                                Ext.getCmp('ArchiveDossierPnl').paginazione = Ext.getCmp('ArchiveDossierPnl').paginazione.splice(0, index + 1);
                                                                Ext.getCmp('paginazioneCnt').reloadPagination();
                                                                Ext.getStore('ArchiveDocumentDossier').load({
                                                                    url: Ext.getStore('ArchiveDocumentDossier').getProxy().url + '/' + val.id
                                                                });
                                                            }
                                                        })
                                                    );
                                                });


                                                Ext.getCmp('paginazioneCnt').add(Ext.create('Ext.form.Label', {
                                                    text: ' / '
                                                }));

                                                Ext.getCmp('paginazioneCnt').add(
                                                    Ext.create('Ext.Button', {
                                                        width: 24,
                                                        iconCls: 'icon-add',
                                                        handler: function () {
                                                            var p = Ext.getCmp('ArchiveDossierPnl').paginazione,
                                                                lastEl;

                                                            Ext.widget('ArchiveDossierNewWin').show();
                                                            if (p.length > 0) {
                                                                lastEl = p[p.length - 1];
                                                                Ext.getCmp('ArchiveDossierNewWin').path = lastEl.id;
                                                                Ext.getCmp('dossierPathCmb').setValue(lastEl.text);
                                                            } else {
                                                                Ext.getCmp('ArchiveDossierNewWin').path = -1;
                                                            }


                                                            Ext.getCmp('dossierPathCmb').hide();

                                                        }
                                                    })
                                                );
                                            },
                                            flex: 1,
                                            id: 'paginazioneCnt',
                                            itemId: 'paginazioneCnt',
                                            listeners: {
                                                boxready: {
                                                    fn: me.onPaginazioneLblShow,
                                                    scope: me
                                                }
                                            }
                                        },
                                        {
                                            xtype: 'tbspacer',
                                            flex: 1
                                        },
                                        {
                                            xtype: 'textfield',
                                            hidden: true,
                                            width: 200,
                                            fieldLabel: '',
                                            emptyText: 'Cerca ...',
                                            listeners: {
                                                change: {
                                                    fn: me.onTextfieldChange3,
                                                    delay: 300,
                                                    buffer: 300,
                                                    scope: me
                                                }
                                            }
                                        }
                                    ]
                                }
                            ],
                            listeners: {
                                boxready: {
                                    fn: me.onArchiveDossierPnlBoxReady,
                                    scope: me
                                }
                            }
                        },
                        {
                            xtype: 'panel',
                            id: 'ArchiveDossierMcPnl',
                            itemId: 'ArchiveDossierMcPnl',
                            layout: 'fit',
                            title: 'Fascicolo didattico',
                            tabConfig: {
                                xtype: 'tab',
                                hidden: true,
                                iconCls: 'icon-user'
                            },
                            dockedItems: [
                                {
                                    xtype: 'toolbar',
                                    dock: 'top',
                                    items: [
                                        {
                                            xtype: 'container',
                                            reloadPagination: function () {
                                                while (Ext.getCmp('paginazioneMcCnt').items.items.length > 0) {
                                                    Ext.getCmp('paginazioneMcCnt').remove(Ext.getCmp('paginazioneMcCnt').items.items[0]);
                                                }

                                                Ext.getCmp('paginazioneMcCnt').add(
                                                    Ext.create('Ext.Button', {
                                                        text: 'Fascicoli',
                                                        id_mc: null,
                                                        handler: function () {
                                                            Ext.getCmp('ArchiveDossierMcPnl').paginazione = [];
                                                            Ext.getCmp('paginazioneMcCnt').reloadPagination();
                                                            Ext.getStore('ArchiveDocumentDossierMc').load();
                                                        }
                                                    })
                                                );

                                                Ext.each(Ext.getCmp('ArchiveDossierMcPnl').paginazione, function (val, index) {
                                                    Ext.getCmp('paginazioneMcCnt').add(Ext.create('Ext.form.Label', {
                                                        text: ' / '
                                                    }));
                                                    Ext.getCmp('paginazioneMcCnt').add(
                                                        Ext.create('Ext.Button', {
                                                            text: val.text,
                                                            id_mc: val.id,
                                                            handler: function () {
                                                                Ext.getCmp('ArchiveDossierMcPnl').paginazione = Ext.getCmp('ArchiveDossierMcPnl').paginazione.splice(0, index + 1);
                                                                Ext.getCmp('paginazioneMcCnt').reloadPagination();
                                                                Ext.getStore('ArchiveDocumentDossierMc').load({
                                                                    url: Ext.getStore('ArchiveDocumentDossierMc').getProxy().url + '/' + val.id
                                                                });
                                                            }
                                                        })
                                                    );
                                                });


                                                /*Ext.getCmp('paginazioneMcCnt').add(Ext.create('Ext.form.Label', {
                                                text: ' / '
                                                }));*/

                                                /*Ext.getCmp('paginazioneMcCnt').add(
                                                Ext.create('Ext.Button', {
                                                width: 24,
                                                iconCls: 'icon-add',
                                                handler: function() {
                                                var p = Ext.getCmp('ArchiveDossierMcPnl').paginazione,
                                                lastEl;

                                                Ext.widget('ArchiveDossierNewWin').show();
                                                if(p.length > 0) {
                                                lastEl = p[p.length-1];
                                                Ext.getCmp('ArchiveDossierNewWin').path = lastEl.id;
                                                Ext.getCmp('dossierPathCmb').setValue(lastEl.text);
                                                } else {
                                                Ext.getCmp('ArchiveDossierNewWin').path = -1;
                                                }


                                                Ext.getCmp('dossierPathCmb').hide();

                                                }
                                                })
                                                );*/
                                            },
                                            id: 'paginazioneMcCnt',
                                            itemId: 'paginazioneMcCnt',
                                            listeners: {
                                                boxready: {
                                                    fn: me.onPaginazioneMcCntBoxReady,
                                                    scope: me
                                                }
                                            }
                                        }
                                    ]
                                }
                            ],
                            items: [
                                {
                                    xtype: 'gridpanel',
                                    title: '',
                                    store: 'ArchiveDocumentDossierMc',
                                    columns: [
                                        {
                                            xtype: 'actioncolumn',
                                            width: 30,
                                            align: 'center',
                                            items: [
                                                {
                                                    getClass: function (v, metadata, r, rowIndex, colIndex, store) {
                                                        if (r.get('dossier') === true) {
                                                            return 'icon-folder_page';
                                                        } else {
                                                            return 'icon-table_multiple';
                                                        }
                                                    }
                                                }
                                            ]
                                        },
                                        {
                                            xtype: 'gridcolumn',
                                            renderer: function (value, metaData, record, rowIndex, colIndex, store, view) {
                                                if (record.get('dossier') === true)
                                                    return value + ' (Documenti ' + record.get('files') + ')';
                                                else return value;
                                            },
                                            dataIndex: 'name',
                                            text: 'Nome',
                                            flex: 1
                                        },
                                        {
                                            xtype: 'gridcolumn',
                                            dataIndex: 'stats_sign',
                                            text: 'Firmati'
                                        },
                                        {
                                            xtype: 'gridcolumn',
                                            dataIndex: 'stats_archive',
                                            text: 'Conservati'
                                        },
                                        {
                                            xtype: 'actioncolumn',
                                            items: [
                                                {
                                                    handler: function (view, rowIndex, colIndex, item, e, record, row) {
                                                        if (record.get('dossier') === true) {
                                                            Ext.widget('ArchiveDashboardWin').show();

                                                            var dossierPath = '';
                                                            Ext.each(Ext.getCmp('ArchiveDossierMcPnl').paginazione, function (val) {
                                                                if (dossierPath)
                                                                    dossierPath = dossierPath + '/' + val.text;
                                                                else
                                                                    dossierPath = val.text;
                                                            });

                                                            if (dossierPath)
                                                                dossierPath = dossierPath + '/' + record.get('name');
                                                            else
                                                                dossierPath = record.get('name');

                                                            Ext.getStore('ArchiveDashboard').load({
                                                                params: {
                                                                    'dossier': dossierPath
                                                                }
                                                            });
                                                        } else {
                                                            Ext.getCmp('ArchivePnl').viewDocument(record);
                                                        }

                                                    },
                                                    iconCls: 'icon-information'
                                                }
                                            ]
                                        }
                                    ],
                                    viewConfig: {
                                        listeners: {
                                            itemdblclick: {
                                                fn: me.onViewItemClick2,
                                                scope: me
                                            }
                                        }
                                    }
                                }
                            ],
                            listeners: {
                                boxready: {
                                    fn: me.onPanelBoxReady,
                                    scope: me
                                }
                            }
                        },
                        {
                            xtype: 'panel',
                            permissible: true,
                            border: false,
                            id: 'ArchiveArchivedPnl',
                            itemId: 'ArchiveArchivedPnl',
                            iconCls: 'icon-database',
                            title: 'Archivio',
                            layout: {
                                type: 'vbox',
                                align: 'stretch'
                            },
                            tabConfig: {
                                xtype: 'tab',
                                id: 'ArchiveArchivedTab',
                                itemId: 'ArchiveArchivedTab',
                                textAlign: 'left'
                            },
                            items: [
                                {
                                    xtype: 'gridpanel',
                                    forceAction: function (operation, record) {
                                        Ext.Msg.show({
                                            title: 'ATTENZIONE',
                                            msg: "L'operazione da archivio è concessa a tutti. Nel caso in cui tu non sia l'utente abilitato, sarà registrato il fatto che l'operazione è stata fatta da un'altro utente",
                                            buttons: Ext.Msg.YESCANCEL,
                                            buttonText:
                                            {
                                                yes: 'Conferma',
                                                cancel: 'Annulla'
                                            },
                                            fn: function (buttonValue, inputText, showConfig) {
                                                if (buttonValue == 'yes') {
                                                    switch (operation) {
                                                        case 'PROTOCOL':
                                                            var date = record.get('action_protocol_date');

                                                            if (date === null) {
                                                                Ext.widget('ProtocolProtocolNewWin').show();
                                                                Ext.getCmp('ProtocolNewDate').setValue(new Date());
                                                                Ext.getStore('ProtocolLinkedCorrespondentsForm').removeAll();
                                                                Ext.getStore('ProtocolLinkedProtocolsForm').removeAll();
                                                                Ext.getStore('ProtocolLinkedDocumentsForm').removeAll();
                                                                // Ext.getStore('ProtocolLinkedDocumentsForm').insert(0, record);
                                                                Ext.getCmp('ProtocolNewLinkedDocumentsGrid').getView().disable();
                                                                Ext.getCmp('ProtocolDocumentToolBar').disable();

                                                                Ext.Ajax.request({
                                                                    method: 'GET',
                                                                    url: '/mc2-api/archive/document_file',
                                                                    params: {
                                                                        document: record.get('id')
                                                                    },
                                                                    success: function (res) {
                                                                        var r = Ext.decode(res.responseText);
                                                                        Ext.getStore('ProtocolLinkedDocumentsForm').add(r.results);
                                                                    }
                                                                });
                                                                Ext.getCmp('ArchivePnl').populateProtocol(record.get('model'));

                                                            }
                                                            break;
                                                        case 'ALBO':
                                                            var date = record.get('action_albo_date');

                                                            if (date === null) {
                                                                Ext.widget('AlboPublicationEditWin').show();
                                                                Ext.getStore('AlboLinkedDocumentsForm').removeAll();

                                                                Ext.getCmp('AlboPublicationEditLinkedTlb').disable();

                                                                Ext.getStore('ArchiveDocumentFiles').load({
                                                                    params: {
                                                                        document: record.get('id')
                                                                    },
                                                                    callback: function (res) {
                                                                        Ext.getStore('AlboLinkedDocumentsForm').add(res);
                                                                        Ext.getCmp('AlboPublicationEditLinkedDocumentsGrid').getView().disable();
                                                                    }
                                                                });
                                                                Ext.getCmp('ArchivePnl').populateAlbo(record.get('model'));
                                                            }
                                                            break;
                                                        case 'TRASPARENZA':
                                                            var date = record.get('action_trasparenza_date');

                                                            if (date === null) {
                                                                Ext.widget('TrasparenzaVoicePickerWin').show();
                                                                Ext.getStore('ArchiveDocumentFiles').load({
                                                                    params: {
                                                                        document: record.get('id')
                                                                    }
                                                                });
                                                                Ext.getCmp('ArchivePnl').populateTrasparenza(record.get('model'));
                                                            }
                                                            break;
                                                        case 'ARCHIVIO':
                                                            var date = record.get('action_archive_date');

                                                            if (date === null) {
                                                                var amfs = Ext.getStore('ArchiveMetadataFiles'),
                                                                    id = record.get('id');

                                                                amfs.removeAll();
                                                                if (record.get('action_archive_date')) {
                                                                    Ext.Ajax.request({
                                                                        url: '/mc2-api/archive/document_file?document=' + id,
                                                                        success: function (r) {
                                                                            Ext.widget('ArchiveMetadataFileWin').show();
                                                                            Ext.getCmp('ArchiveMetadataFileWin').record = record;
                                                                            Ext.getCmp('ArchiveMetadataFileTb').disable();
                                                                            Ext.getCmp('ArchiveMetadataFileGrid').disable();
                                                                            res = Ext.decode(r.responseText);
                                                                            Ext.each(res.results, function (a) {
                                                                                a.metadata.name = a.filename;
                                                                                amfs.add(a.metadata);
                                                                            });
                                                                        }
                                                                    });
                                                                } else {
                                                                    Ext.widget('ArchiveMetadataFileWin').show();
                                                                    Ext.getCmp('ArchiveMetadataFileWin').record = record;
                                                                }
                                                            }
                                                            break;
                                                    }
                                                }
                                            },
                                            icon: Ext.Msg.WARNING
                                        });
                                    },
                                    flex: 1,
                                    border: false,
                                    id: 'ArchiveDocumentsGrid',
                                    itemId: 'ArchiveDocumentsGrid',
                                    header: false,
                                    iconCls: 'icon-folder_page',
                                    emptyText: 'Nessun documento archiviato.',
                                    enableColumnHide: false,
                                    enableColumnMove: false,
                                    enableColumnResize: false,
                                    sortableColumns: false,
                                    store: 'ArchiveDocumentsArchived',
                                    columns: [
                                        {
                                            xtype: 'actioncolumn',
                                            getTip: function (v, metadata, r, rowIndex, colIndex, store) {
                                                var action = r.get('action_archive'),
                                                    date = r.get('action_archive_date');

                                                if (action) {
                                                    if (date !== null) {
                                                        return 'Archiviato in conservazione il: ' + Ext.util.Format.date(date, 'd/m/Y H:i');
                                                    } else {
                                                        return 'Da archiviare';
                                                    }
                                                }
                                            },
                                            width: 20,
                                            text: 'C',
                                            items: [
                                                {
                                                    getClass: function (v, metadata, r, rowIndex, colIndex, store) {
                                                        var action = r.get('action_archive'),
                                                            date = r.get('action_archive_date');

                                                        if (action) {
                                                            if (date !== null) {
                                                                return 'icon-accept';
                                                            } else {
                                                                return 'icon-control_blank';
                                                            }
                                                        }
                                                    },
                                                    handler: function (view, rowIndex, colIndex, item, e, record, row) {
                                                        if (record.get('action_archive')) {
                                                            Ext.getCmp('ArchiveDocumentsGrid').forceAction('ARCHIVIO', record);
                                                        }
                                                    }
                                                }
                                            ]
                                        },
                                        {
                                            xtype: 'actioncolumn',
                                            width: 20,
                                            dataIndex: 'action_sign',
                                            hideable: false,
                                            text: 'F',
                                            items: [
                                                {
                                                    getClass: function (v, metadata, r, rowIndex, colIndex, store) {
                                                        var action = r.get('action_sign'),
                                                            date = r.get('action_sign_date');

                                                        if (action) {
                                                            if (date !== null) {
                                                                return 'icon-accept';
                                                            } else {
                                                                return 'icon-control_blank';
                                                            }
                                                        }
                                                    },
                                                    getTip: function (v, metadata, r, rowIndex, colIndex, store) {
                                                        var action = r.get('action_sign'),
                                                            date = r.get('action_sign_date');

                                                        if (action) {
                                                            if (date !== null) {
                                                                return 'Firmato il: ' + Ext.util.Format.date(date, 'd/m/Y H:i');
                                                            } else {
                                                                return 'Da Firmare';
                                                            }
                                                        }
                                                    },
                                                    handler: function (view, rowIndex, colIndex, item, e, record, row) {
                                                        if (record.get('action_sign')) {
                                                            Ext.Msg.alert('ATTENZIONE',
                                                                "La firma non può essere forzata. E' necessario loggarsi con l'utente abilitato");
                                                        }
                                                    }
                                                }
                                            ]
                                        },
                                        {
                                            xtype: 'actioncolumn',
                                            width: 20,
                                            align: 'center',
                                            hideable: false,
                                            text: 'P',
                                            tooltip: 'Stato protocollazione',
                                            items: [
                                                {
                                                    getClass: function (v, metadata, r, rowIndex, colIndex, store) {
                                                        var action = r.get('action_protocol'),
                                                            date = r.get('action_protocol_date');

                                                        if (action) {
                                                            if (r.get('forced_protocol')) {
                                                                return 'icon-accept_warning';
                                                            } else if (date !== null) {
                                                                return 'icon-accept';
                                                            } else {
                                                                return 'icon-control_blank';
                                                            }
                                                        }
                                                    },
                                                    getTip: function (v, metadata, r, rowIndex, colIndex, store) {
                                                        var action = r.get('action_protocol'),
                                                            date = r.get('action_protocol_date');

                                                        if (action) {
                                                            if (date !== null) {
                                                                if (r.get('forced_protocol')) {
                                                                    return r.get('forced_protocol');
                                                                } else {
                                                                    return 'Protocollo numero ' + r.get('action_protocol_number') + ' effettuati il: ' + Ext.util.Format.date(date, 'd/m/Y H:i');
                                                                }
                                                            } else {
                                                                return 'Da protocollare';
                                                            }
                                                        }
                                                    },
                                                    handler: function (view, rowIndex, colIndex, item, e, record, row) {
                                                        if (record.get('action_protocol')) {
                                                            Ext.getCmp('ArchiveDocumentsGrid').forceAction('PROTOCOL', record);
                                                        }
                                                    }
                                                }
                                            ]
                                        },
                                        {
                                            xtype: 'actioncolumn',
                                            width: 20,
                                            align: 'center',
                                            hideable: false,
                                            text: 'A',
                                            tooltip: 'Stato pubblicazione su Albo',
                                            items: [
                                                {
                                                    getClass: function (v, metadata, r, rowIndex, colIndex, store) {
                                                        var action = r.get('action_albo'),
                                                            date = r.get('action_albo_date');

                                                        if (action) {
                                                            if (r.get('forced_albo')) {
                                                                return 'icon-accept_warning';
                                                            } else if (date !== null) {
                                                                return 'icon-accept';
                                                            } else {
                                                                return 'icon-control_blank';
                                                            }
                                                        }
                                                    },
                                                    getTip: function (v, metadata, r, rowIndex, colIndex, store) {
                                                        var action = r.get('action_albo'),
                                                            date = r.get('action_albo_date');

                                                        if (action) {
                                                            if (date !== null) {
                                                                if (r.get('forced_albo')) {
                                                                    return r.get('forced_albo');
                                                                } else {
                                                                    return 'Pubblicato su Albo Pretorio il: ' + Ext.util.Format.date(date, 'd/m/Y H:i');
                                                                }
                                                            } else {
                                                                return 'Da pubblicare su Albo';
                                                            }
                                                        }
                                                    },
                                                    handler: function (view, rowIndex, colIndex, item, e, record, row) {
                                                        if (record.get('action_albo')) {
                                                            Ext.getCmp('ArchiveDocumentsGrid').forceAction('ALBO', record);
                                                        }
                                                    }
                                                }
                                            ]
                                        },
                                        {
                                            xtype: 'actioncolumn',
                                            width: 20,
                                            align: 'center',
                                            hideable: false,
                                            text: 'T',
                                            tooltip: 'Stato abbinamento a Trasparenza',
                                            items: [
                                                {
                                                    getClass: function (v, metadata, r, rowIndex, colIndex, store) {
                                                        var action = r.get('action_trasparenza'),
                                                            date = r.get('action_trasparenza_date');

                                                        if (action) {
                                                            if (r.get('forced_trasparenza')) {
                                                                return 'icon-accept_warning';
                                                            } else if (date !== null) {
                                                                return 'icon-accept';
                                                            } else {
                                                                return 'icon-control_blank';
                                                            }
                                                        }
                                                    },
                                                    getTip: function (v, metadata, r, rowIndex, colIndex, store) {
                                                        var action = r.get('action_trasparenza'),
                                                            date = r.get('action_trasparenza_date');

                                                        if (action) {
                                                            if (date !== null) {
                                                                if (r.get('forced_trasparenza')) {
                                                                    return r.get('forced_trasparenza');
                                                                } else {
                                                                    return 'Allegato a Trasparenza il: ' + Ext.util.Format.date(date, 'd/m/Y H:i');
                                                                }
                                                            } else {
                                                                return 'Da allegare a Trasparenza';
                                                            }
                                                        }
                                                    },
                                                    handler: function (view, rowIndex, colIndex, item, e, record, row) {
                                                        if (record.get('action_trasparenza')) {
                                                            Ext.getCmp('ArchiveDocumentsGrid').forceAction('TRASPARENZA', record);
                                                        }
                                                    }
                                                }
                                            ]
                                        },
                                        {
                                            xtype: 'gridcolumn',
                                            renderer: function (value, metaData, record, rowIndex, colIndex, store, view) {
                                                var prop = record.get('checked_users') + '/' + record.get('to_check_users');

                                                if (record.get('to_check_users') === 0) {
                                                    return '';
                                                }

                                                if (record.get('checked_users') < record.get('to_check_users')) {
                                                    return '<span style="color: red">' + prop + '</span>';
                                                } else {
                                                    return prop;
                                                }


                                            },
                                            width: 80,
                                            align: 'center',
                                            dataIndex: 'to_check_users',
                                            text: 'Presa visione'
                                        },
                                        {
                                            xtype: 'gridcolumn',
                                            renderer: function (value, metaData, record, rowIndex, colIndex, store, view) {
                                                return record.get('assign_to_user_name') ? record.get('assign_to_user_name') : record.get('assign_to_office_name');
                                            },
                                            text: 'Assegnato a'
                                        },
                                        /*{
                                            xtype: 'gridcolumn',
                                            align: 'center',
                                            dataIndex: 'class_name',
                                            text: 'Tipo di flusso'
                                        },*/
                                        {
                                            xtype: 'gridcolumn',
                                            align: 'center',
                                            dataIndex: 'dossier',
                                            text: 'Fascicolo'
                                        },
                                        /*{
                                            xtype: 'gridcolumn',
                                            align: 'center',
                                            dataIndex: 'model_text',
                                            text: 'Modello'
                                        },*/
                                        {
                                            xtype: 'datecolumn',
                                            width: 109,
                                            sortable: true,
                                            align: 'center',
                                            dataIndex: 'upload_date',
                                            hideable: false,
                                            text: 'Data caricamento',
                                            format: 'd/m/Y'
                                        },
                                        {
                                            xtype: 'datecolumn',
                                            sortable: true,
                                            align: 'center',
                                            dataIndex: 'expiration_date',
                                            text: 'Scadenza',
                                            format: 'd/m/Y'
                                        },
                                        {
                                            xtype: 'gridcolumn',
                                            dataIndex: 'short_description',
                                            text: 'Descrizione breve',
                                            flex: 1
                                        },
                                        {
                                            xtype: 'actioncolumn',
                                            width: 50,
                                            align: 'center',
                                            hideable: false,
                                            items: [
                                                {
                                                    handler: function (view, rowIndex, colIndex, item, e, record, row) {
                                                        var store = Ext.getStore('ArchiveDocumentMails');

                                                        Ext.widget('ArchiveDocumentMailWin').show();
                                                        store.load({
                                                            url: store.getProxy().url + '/' + record.get('id') + '/mail'
                                                        });
                                                    },
                                                    getClass: function (v, metadata, r, rowIndex, colIndex, store) {
                                                        if (r.get('mail') > 0) {
                                                            return 'icon-mail';
                                                        }
                                                        return '';
                                                    }
                                                }
                                            ]
                                        }
                                    ],
                                    viewConfig: {
                                        getRowClass: function (record, rowIndex, rowParams, store) {
                                            if (record.get('completed')) {
                                                return 'archive-completed';
                                            }
                                            return '';
                                        }
                                    },
                                    listeners: {
                                        itemcontextmenu: {
                                            fn: me.onArchiveDocumentsGridItemContextMenu,
                                            scope: me
                                        },
                                        itemdblclick: {
                                            fn: me.onArchiveDocumentsGridItemDblClick,
                                            scope: me
                                        }
                                    },
                                    dockedItems: [
                                        {
                                            xtype: 'pagingtoolbar',
                                            dock: 'bottom',
                                            width: 360,
                                            displayInfo: true,
                                            displayMsg: 'Documenti {0} - {1} di {2}',
                                            emptyMsg: 'Nessun documento',
                                            store: 'ArchiveDocumentsArchived'
                                        },
                                        {
                                            xtype: 'toolbar',
                                            dock: 'top',
                                            items: [
                                                {
                                                    xtype: 'textfield',
                                                    id: 'ArchiveArchivedSearchTxt',
                                                    itemId: 'ArchiveArchivedSearchTxt',
                                                    width: 200,
                                                    emptyText: 'Cerca ...',
                                                    listeners: {
                                                        change: {
                                                            fn: me.onArchiveArchivedSearchTxtChange,
                                                            delay: 300,
                                                            buffer: 300,
                                                            scope: me
                                                        }
                                                    }
                                                }
                                            ]
                                        }
                                    ]
                                },
                                {
                                    xtype: 'menu',
                                    permissible: true,
                                    flex: 1,
                                    hidden: true,
                                    id: 'ArchiveEditMn',
                                    itemId: 'ArchiveEditMn',
                                    items: [
                                        {
                                            xtype: 'menuitem',
                                            handler: function (item, e) {
                                                var doc = Ext.getCmp('ArchiveDocumentsGrid').getSelectionModel().getSelection()[0];

                                                Ext.widget('ArchiveDocumentEditWin').show();
                                                Ext.getCmp('ArchiveDocumentEditForm').loadRecord(doc);
                                                Ext.getCmp('ArchiveDocumentEditActionProtocol').setDisabled(doc.get('action_protocol_date') !== null);
                                                Ext.getCmp('ArchiveDocumentEditActionAlbo').setDisabled(doc.get('action_albo_date') !== null);
                                                Ext.getCmp('ArchiveDocumentEditActionTrasparenza').setDisabled(doc.get('action_trasparenza_date') !== null);
                                            },
                                            id: 'contextDocumentEdit',
                                            itemId: 'contextDocumentEdit',
                                            iconCls: 'icon-pencil',
                                            text: 'Modifica'
                                        },
                                        {
                                            xtype: 'menuitem',
                                            handler: function (item, e) {
                                                var record = Ext.getCmp('ArchiveDocumentsGrid').getSelectionModel().getSelection()[0];

                                                Ext.Msg.show({
                                                    title: record.get('filename'),
                                                    msg: 'Sei sicuro di voler eliminare questo Documento?',
                                                    buttons: Ext.Msg.YESNO,
                                                    fn: function (r) {
                                                        if (r == 'yes') {
                                                            store = Ext.getStore('ArchiveDocumentsArchived');
                                                            store.remove(record);
                                                            store.sync({
                                                                callback: function () {
                                                                    store.load();
                                                                },
                                                                failure: function () {
                                                                    Ext.Msg.alert('Attenzione', 'Cancellazione NON avvenuta.');
                                                                }
                                                            });
                                                        }
                                                    }
                                                });
                                            },
                                            id: 'contextDocumentDelete',
                                            itemId: 'contextDocumentDelete',
                                            iconCls: 'icon-cancel',
                                            text: 'Elimina'
                                        },
                                        {
                                            xtype: 'menuseparator'
                                        },
                                        {
                                            xtype: 'menuitem',
                                            handler: function (item, e) {
                                                var record = Ext.getCmp('ArchiveDocumentsGrid').getSelectionModel().getSelection()[0];

                                                window.open("/mc2-api/archive/document/" + record.get('id') + '/files',
                                                    "MC2DocumentDownloadWindow",
                                                    "status=0, menubar=0, location=0");
                                            },
                                            id: 'contextDocumentDownload',
                                            itemId: 'contextDocumentDownload',
                                            iconCls: 'icon-arrow_down',
                                            text: 'Scarica'
                                        },
                                        {
                                            xtype: 'menuitem',
                                            handler: function (item, e) {
                                                var sel = Ext.getCmp('ArchiveDocumentsGrid').getSelectionModel().getSelection()[0],
                                                    data = {
                                                        subject: sel.get('short_description'),
                                                        document: sel.get('id')
                                                    };

                                                Ext.widget('ArchiveMailSendWin').show();

                                                Ext.getStore('ArchiveDocumentFiles').load({
                                                    params: {
                                                        document: sel.get('id')
                                                    }
                                                });

                                                Ext.getCmp('SendMailFrm').getForm().setValues(data);

                                            },
                                            iconCls: 'icon-email',
                                            text: 'Invia mail'
                                        },
                                        {
                                            xtype: 'menuitem',
                                            handler: function (item, e) {
                                                var sel = Ext.getCmp('ArchiveDocumentsGrid').getSelectionModel().getSelection()[0];

                                                Ext.Ajax.request({
                                                    url: '/mc2-api/archive/document/' + sel.get('id'),
                                                    method: 'PUT',
                                                    params: {
                                                        completed: 0
                                                    },
                                                    success: function () {
                                                        sel.set('completed', null);
                                                        Ext.Msg.alert('SUCCESSO', 'Fascicolo riaperto correttamente');
                                                    }
                                                });
                                            },
                                            id: 'ArchiveEditMnReopen',
                                            itemId: 'ArchiveEditMnReopen',
                                            text: 'Riapri fascicolo'
                                        }
                                    ]
                                }
                            ]
                        },
                        {
                            xtype: 'panel',
                            hidden: true,
                            iconCls: 'icon-application_side_tree',
                            title: 'Raccolte',
                            layout: {
                                type: 'hbox',
                                align: 'stretch'
                            },
                            items: [
                                {
                                    xtype: 'gridpanel',
                                    flex: 1,
                                    title: '',
                                    store: 'Raccoltes',
                                    viewConfig: {
                                        listeners: {
                                            itemclick: {
                                                fn: me.onViewItemClick,
                                                scope: me
                                            }
                                        }
                                    },
                                    columns: [
                                        {
                                            xtype: 'actioncolumn',
                                            width: 39,
                                            align: 'center',
                                            dataIndex: 'type',
                                            items: [
                                                {
                                                    getClass: function (v, metadata, r, rowIndex, colIndex, store) {
                                                        if (v === 'R') {
                                                            return 'icon-folder';
                                                        }

                                                        if (v === 'F') {
                                                            return 'icon-book_addresses';
                                                        }

                                                        if (v === 'C') {
                                                            return 'icon-application_view_list';
                                                        }

                                                    }
                                                }
                                            ]
                                        },
                                        {
                                            xtype: 'gridcolumn',
                                            dataIndex: 'name',
                                            text: 'Name',
                                            flex: 1
                                        },
                                        {
                                            xtype: 'numbercolumn',
                                            dataIndex: 'elements',
                                            text: 'Elementi',
                                            format: '00'
                                        }
                                    ],
                                    listeners: {
                                        boxready: {
                                            fn: me.onGridpanelBoxReady,
                                            scope: me
                                        }
                                    },
                                    dockedItems: [
                                        {
                                            xtype: 'toolbar',
                                            dock: 'top',
                                            items: [
                                                {
                                                    xtype: 'label',
                                                    padding: 5,
                                                    text: 'My Label'
                                                }
                                            ]
                                        }
                                    ]
                                }
                            ],
                            listeners: {
                                show: {
                                    fn: me.onPanelShow,
                                    scope: me
                                }
                            }
                        }
                    ]
                }
            ],
            listeners: {
                boxready: {
                    fn: me.onArchivePnlBoxReady,
                    scope: me
                }
            },
            dockedItems: [
                {
                    xtype: 'toolbar',
                    permissible: true,
                    dock: 'top',
                    id: 'ArchiveToolbar',
                    itemId: 'ArchiveToolbar',
                    items: [
                        {
                            xtype: 'button',
                            handler: function (button, e) {
                                Ext.widget('ArchiveDocumentUploadWin').show();
                            },
                            id: 'ArchiveNewBtn',
                            itemId: 'ArchiveNewBtn',
                            iconCls: 'icon-folder_up',
                            text: 'Carica'
                        },/*
                        {
                            xtype: 'button',
                            handler: function (button, e) {
                                Ext.widget('ArchiveClassWin').show();
                            },
                            id: 'ArchiveClassBtn',
                            itemId: 'ArchiveClassBtn',
                            iconCls: 'icon-application_view_list',
                            text: 'Tipi di flusso'
                        },*/
                        {
                            xtype: 'button',
                            handler: function (button, e) {
                                Ext.widget('ArchiveTemplateWin').show();
                            },
                            hidden: true,
                            text: 'Tipi di documento'
                        },
                        /*{
                            xtype: 'button',
                            handler: function (button, e) {
                                Ext.widget('ArchiveTemplateWin').show();
                            },
                            iconCls: 'icon-application_side_boxes',
                            text: 'Modelli'
                        },*/
                        {
                            xtype: 'button',
                            handler: function (button, e) {
                                Ext.widget('ArchiveOfficeWin').show();
                            },
                            iconCls: 'icon-building',
                            text: 'Uffici'
                        },
                        {
                            xtype: 'button',
                            id: 'ArchivePrintsBtn',
                            itemId: 'ArchivePrintsBtn',
                            iconCls: 'icon-printer',
                            text: 'Stampe',
                            menu: {
                                xtype: 'menu',
                                items: [
                                    {
                                        xtype: 'menuitem',
                                        handler: function (item, e) {
                                            var rec = {};

                                            rec.newSpool = 1;
                                            rec.print = 'ByDocumentClass';
                                            rec.namespace = 'Archive';
                                            rec.type = 'PDF';
                                            rec.mime = 'application/pdf';

                                            Ext.Ajax.request({
                                                url: '/mc2-api/core/print',
                                                params: rec,
                                                success: function (response, opts) {
                                                    var res = Ext.decode(response.responseText);
                                                    mc2ui.app.showNotifyPrint(res);
                                                }
                                            });
                                        },
                                        iconCls: 'icon-color_swatch',
                                        text: 'Riepilogo Documenti per tipo'
                                    },
                                    {
                                        xtype: 'menuitem',
                                        handler: function (item, e) {
                                            var rec = {};

                                            rec.newSpool = 1;
                                            rec.print = 'ByOrigin';
                                            rec.namespace = 'Archive';
                                            rec.type = 'PDF';
                                            rec.mime = 'application/pdf';

                                            Ext.Ajax.request({
                                                url: '/mc2-api/core/print',
                                                params: rec,
                                                success: function (response, opts) {
                                                    var res = Ext.decode(response.responseText);
                                                    mc2ui.app.showNotifyPrint(res);
                                                }
                                            });
                                        },
                                        iconCls: 'icon-arrow_in_longer',
                                        text: 'Riepilogo Documenti per origine'
                                    },
                                    {
                                        xtype: 'menuitem',
                                        handler: function (item, e) {
                                            var rec = {};

                                            rec.newSpool = 1;
                                            rec.print = 'Filtered';
                                            rec.namespace = 'Archive';
                                            rec.type = 'PDF';
                                            rec.mime = 'application/pdf';
                                            rec.filter = Ext.JSON.encode(Ext.getCmp('ArchiveFilterForm').getForm().getValues());

                                            Ext.Ajax.request({
                                                url: '/mc2-api/core/print',
                                                params: rec,
                                                success: function (response, opts) {
                                                    var res = Ext.decode(response.responseText);
                                                    mc2ui.app.showNotifyPrint(res);
                                                }
                                            });
                                        },
                                        iconCls: 'icon-find',
                                        text: 'Riepilogo Documenti filtrati'
                                    }
                                ]
                            }
                        },
                        {
                            xtype: 'tbseparator',
                            hidden: true
                        },
                        {
                            xtype: 'tbspacer',
                            flex: 1
                        },
                    ]
                }
            ]
        });

        me.callParent(arguments);
    },

    onArchiveFilterMetadataDateStartChange: function (field, newValue, oldValue, eOpts) {
        Ext.getStore('ArchiveDocumentsArchived').load();
    },

    onArchiveFilterMetadataDateEndChange: function (field, newValue, oldValue, eOpts) {
        Ext.getStore('ArchiveDocumentsArchived').load();
    },

    onArchiveFilterClassChange: function (field, newValue, oldValue, eOpts) {
        Ext.getStore('ArchiveDocumentsArchived').load();
    },

    onArchiveFilterOriginChange: function (field, newValue, oldValue, eOpts) {
        Ext.getCmp('ArchiveFilterForm').applyFilter();
    },

    onArchiveFilteCompleteChange: function (field, newValue, oldValue, eOpts) {
        Ext.getStore('ArchiveDocumentsArchived').load();
    },

    onArchiveFilteCheckedChange: function (field, newValue, oldValue, eOpts) {
        Ext.getStore('ArchiveDocumentsArchived').load();
    },

    onArchiveFilteAssignToUserChange: function (field, newValue, oldValue, eOpts) {
        Ext.getStore('ArchiveDocumentsArchived').load();
    },

    onArchiveFilteAssignToUserChange1: function (field, newValue, oldValue, eOpts) {
        Ext.getStore('ArchiveDocumentsArchived').load();
    },

    onArchiveFilterArchiveDateStartChange: function (field, newValue, oldValue, eOpts) {
        Ext.getCmp('ArchiveFilterForm').applyFilter();
    },

    onArchiveFilterArchiveDateEndChange: function (field, newValue, oldValue, eOpts) {
        Ext.getCmp('ArchiveFilterForm').applyFilter();
    },

    onArchiveFilterNameChange: function (field, newValue, oldValue, eOpts) {
        Ext.getCmp('ArchiveFilterForm').applyFilter();
    },

    onArchiveFilterDescriptionChange: function (field, newValue, oldValue, eOpts) {
        Ext.getCmp('ArchiveFilterForm').applyFilter();
    },

    onArchiveFilterActionProtocolChange: function (field, newValue, oldValue, eOpts) {
        Ext.getCmp('ArchiveFilterForm').applyFilter();
    },

    onArchiveFilterActionAlboChange: function (field, newValue, oldValue, eOpts) {
        Ext.getCmp('ArchiveFilterForm').applyFilter();
    },

    onArchiveFilterActionTrasparenzaChange: function (field, newValue, oldValue, eOpts) {
        Ext.getCmp('ArchiveFilterForm').applyFilter();
    },

    onArchiveFilterMetadataTextChange: function (field, newValue, oldValue, eOpts) {
        Ext.getCmp('ArchiveFilterForm').applyFilter();
    },

    onArchiveFilterMetadataNumericStartChange: function (field, newValue, oldValue, eOpts) {
        if (newValue && newValue > parseInt(Ext.getCmp('ArchiveFilterMetadataNumericEnd').getValue())) {
            field.setValue(oldValue);
        } else {
            Ext.getCmp('ArchiveFilterForm').applyFilter();
        }
    },

    onArchiveFilterMetadataNumericEndChange: function (field, newValue, oldValue, eOpts) {
        if (newValue && newValue < parseInt(Ext.getCmp('ArchiveFilterMetadataNumericStart').getValue())) {
            field.setValue(oldValue);
        } else {
            Ext.getCmp('ArchiveFilterForm').applyFilter();
        }
    },

    onArchiveLeftPnlBoxReady: function (component, width, height, eOpts) {
        Ext.getStore('ArchiveClasses').load();
        Ext.getStore('ArchiveOrigins').load();

        Ext.getStore('ArchiveClassesFilter').load({
            callback: function (records, operation, success) {
                if (success) {
                    Ext.getCmp('ArchiveFilterClass').select(0);
                }
            }
        });

        Ext.getStore('ArchiveOriginsFilter').load({
            callback: function (records, operation, success) {
                if (success) {
                    Ext.getCmp('ArchiveFilterOrigin').select(0);
                }
            }
        });

        Ext.getCmp('ArchiveFilterActionProtocol').select('all');
        Ext.getCmp('ArchiveFilterActionAlbo').select('all');
        Ext.getCmp('ArchiveFilterActionTrasparenza').select('all');
    },

    onArchiveMainPnlBoxReady: function (component, width, height, eOpts) {
        var sp = Ext.getStore('CoreParameter');

        sp.load({
            callback: function (records, options, success) {
                if (success) {
                    //Ext.getCmp('ArchiveMainPagesPurchased').setValue(parseInt(sp.findRecord('name', 'ARCHIVE_PAGES').get('value')));
                }
            }
        });

        Ext.getStore('ArchiveMails').load();
        Ext.getCmp('ArchiveMailPnl').countMail();
    },

    onArchiveDashboardPnlShow: function (component, eOpts) {
        component.updateDashboard();
    },

    onArchiveDashboardPnlRender: function (component, eOpts) {
        component.updateDashboard();
    },



    onViewItemContextMenu: function (dataview, record, item, index, e, eOpts) {
        Ext.getCmp('ArchivePnl').generateAssignMn(record, ['M', 'A']).showAt(e.xy[0], e.xy[1]);
    },



    onArchiveDocumentsQueueGridItemContextMenu: function (dataview, record, item, index, e, eOpts) {
        e.stopEvent();
        var newX = e.xy[0];
        var newY = e.xy[1];

        /*if (record.get('action_archive_date') ||
            record.get('action_protocol_date') !== null ||
            record.get('action_albo_date') !== null ||
            record.get('action_sign_date') !== null ||
            record.get('action_trasparenza_date') !== null) {
            Ext.getCmp('contextArchiveDocumentsQueueDelete').setDisabled(true);
        } else {
            Ext.getCmp('contextArchiveDocumentsQueueDelete').setDisabled(false);
        }*/

        // Ext.getCmp('ArchiveDocumentsQueueEditMn').showAt([newX,newY]);

        var m = Ext.getCmp('ArchivePnl').generateAssignMn(record, ['A']);


        var editItem = Ext.create('Ext.menu.Item', {
            xtype: 'menuitem',
            handler: function (item, e) {
                //var doc = Ext.getCmp('ArchiveDocumentsQueueGrid').getSelectionModel().getSelection()[0];
                var doc = record;

                Ext.getCmp('ArchivePnl').editDocument(doc);
                // Ext.getCmp('ArchiveDocumentEditActionProtocol').setDisabled(doc.get('action_protocol_date') !== null);
                // Ext.getCmp('ArchiveDocumentEditActionAlbo').setDisabled(doc.get('action_albo_date') !== null);
                // Ext.getCmp('ArchiveDocumentEditActionTrasparenza').setDisabled(doc.get('action_trasparenza_date') !== null);
            },
            iconCls: 'icon-pencil',
            text: 'Modifica'
        });

        var deleteItem = Ext.create('Ext.menu.Item', {
            xtype: 'menuitem',
            handler: function (item, e) {
                var record = Ext.getCmp('ArchiveDocumentsUserGrid').getSelectionModel().getSelection()[0];

                Ext.Msg.show({
                    title: record.get('filename'),
                    msg: 'Sei sicuro di voler eliminare questo Documento?',
                    buttons: Ext.Msg.YESNO,
                    fn: function (r) {
                        if (r == 'yes') {
                            storeA = Ext.getStore('ArchiveDocumentsArchived');
                            storeQ = Ext.getStore('ArchiveDocumentsUser');
                            storeQ.remove(record);
                            storeQ.sync({
                                callback: function () {
                                    storeA.load();
                                },
                                failure: function () {
                                    Ext.Msg.alert('Attenzione', 'Cancellazione NON avvenuta.');
                                }
                            });
                        }
                    }
                });
            },
            id: 'contextArchiveDocumentsQueueDelete',
            itemId: 'contextArchiveDocumentsQueueDelete',
            permissible: true,
            iconCls: 'icon-cancel',
            text: 'Elimina'
        });

        m.add(editItem);
        m.add(deleteItem);

        m.showAt(newX, newY);

    },

    onTextfieldChange1: function (field, newValue, oldValue, eOpts) {
        Ext.getStore('ArchiveDocumentsUser').load({
            params: {
                query: newValue
            }
        });
    },

    onArchiveDocumentsUserGridItemDblClick: function (dataview, record, item, index, e, eOpts) {
        Ext.getCmp('ArchivePnl').viewDocument(record);
    },

    onViewItemDblClick1: function (dataview, record, item, index, e, eOpts) {
        Ext.getCmp('ArchivePnl').viewMail(record);

    },

    onViewItemContextMenu1: function (dataview, record, item, index, e, eOpts) {
        Ext.getCmp('ArchivePnl').generateAssignMn(record, ['M', 'A']).showAt(e.xy[0], e.xy[1]);
    },

    onTextfieldChange2: function (field, newValue, oldValue, eOpts) {
        Ext.getStore('ArchiveMailsUser').load({
            params: {
                query: newValue
            }
        });
    },

    onArchiveDocumentsQueueGridItemContextMenu1: function (dataview, record, item, index, e, eOpts) {
        e.stopEvent();
        var newX = e.xy[0];
        var newY = e.xy[1];

        /*if (record.get('action_archive_date') ||
            record.get('action_protocol_date') !== null ||
            record.get('action_albo_date') !== null ||
            record.get('action_sign_date') !== null ||
            record.get('action_trasparenza_date') !== null) {
            Ext.getCmp('contextArchiveDocumentsQueueDelete').setDisabled(true);
        } else {
            Ext.getCmp('contextArchiveDocumentsQueueDelete').setDisabled(false);
        }*/

        // Ext.getCmp('ArchiveDocumentsQueueEditMn').showAt([newX,newY]);

        var m = Ext.getCmp('ArchivePnl').generateAssignMn(record, ['A']);


        var editItem = Ext.create('Ext.menu.Item', {
            xtype: 'menuitem',
            handler: function (item, e) {
                //var doc = Ext.getCmp('ArchiveDocumentsQueueGrid').getSelectionModel().getSelection()[0];
                var doc = record;

                Ext.getCmp('ArchivePnl').editDocument(doc);
                // Ext.getCmp('ArchiveDocumentEditActionProtocol').setDisabled(doc.get('action_protocol_date') !== null);
                // Ext.getCmp('ArchiveDocumentEditActionAlbo').setDisabled(doc.get('action_albo_date') !== null);
                // Ext.getCmp('ArchiveDocumentEditActionTrasparenza').setDisabled(doc.get('action_trasparenza_date') !== null);
            },
            iconCls: 'icon-pencil',
            text: 'Modifica'
        });

        var deleteItem = Ext.create('Ext.menu.Item', {
            xtype: 'menuitem',
            handler: function (item, e) {
                var record = Ext.getCmp('ArchiveDocumentsOfficeGrid').getSelectionModel().getSelection()[0];

                Ext.Msg.show({
                    title: record.get('filename'),
                    msg: 'Sei sicuro di voler eliminare questo Documento?',
                    buttons: Ext.Msg.YESNO,
                    fn: function (r) {
                        if (r == 'yes') {
                            storeA = Ext.getStore('ArchiveDocumentsArchived');
                            storeQ = Ext.getStore('ArchiveDocumentsOffice');
                            storeQ.remove(record);
                            storeQ.sync({
                                callback: function () {
                                    storeA.load();
                                },
                                failure: function () {
                                    Ext.Msg.alert('Attenzione', 'Cancellazione NON avvenuta.');
                                }
                            });
                        }
                    }
                });
            },
            id: 'contextArchiveDocumentsQueueDelete',
            itemId: 'contextArchiveDocumentsQueueDelete',
            permissible: true,
            iconCls: 'icon-cancel',
            text: 'Elimina'
        });

        m.add(editItem);
        m.add(deleteItem);

        m.showAt(newX, newY);

    },

    onTextfieldChange11: function (field, newValue, oldValue, eOpts) {
        Ext.getStore('ArchiveDocumentsUser').load({
            params: {
                query: newValue
            }
        });
    },

    onArchiveDocumentsOfficeGridItemDblClick: function (dataview, record, item, index, e, eOpts) {
        Ext.getCmp('ArchivePnl').viewDocument(record);
    },

    onViewItemDblClick11: function (dataview, record, item, index, e, eOpts) {
        Ext.getCmp('ArchivePnl').viewMail(record);

    },

    onViewItemContextMenu11: function (dataview, record, item, index, e, eOpts) {
        Ext.getCmp('ArchivePnl').generateAssignMn(record, ['M', 'A']).showAt(e.xy[0], e.xy[1]);
    },

    onTextfieldChange21: function (field, newValue, oldValue, eOpts) {
        Ext.getStore('ArchiveMailsUser').load({
            params: {
                query: newValue
            }
        });
    },

    onViewItemClick1: function (dataview, record, item, index, e, eOpts) {
        if (record.get('dossier') === true) {
            Ext.getCmp('ArchiveDossierPnl').paginazione.push({
                id: record.get('id'),
                text: record.get('name')
            });
            Ext.getCmp('paginazioneCnt').reloadPagination();

            var store = Ext.getStore('ArchiveDocumentDossier');
            store.currentPage = 1;
            store.load({
                url: store.getProxy().url + '/' + record.get('id')
            });
        }
    },

    onPaginazioneLblShow: function (component, width, height, eOpts) {
        component.reloadPagination();

    },

    onTextfieldChange3: function (field, newValue, oldValue, eOpts) {
        Ext.getStore('ArchiveDossiers').load({
            params: {
                query: newValue
            }
        });
    },

    onArchiveDossierPnlBoxReady: function (component, width, height, eOpts) {
        Ext.getCmp('ArchiveDossierPnl').paginazione = [];
    },

    onPaginazioneMcCntBoxReady: function (component, width, height, eOpts) {
        component.reloadPagination();
    },

    onViewItemClick2: function (dataview, record, item, index, e, eOpts) {
        if (record.get('dossier') === true) {
            Ext.getCmp('ArchiveDossierMcPnl').paginazione.push({
                id: record.get('id'),
                text: record.get('name')
            });
            Ext.getCmp('paginazioneMcCnt').reloadPagination();

            Ext.getStore('ArchiveDocumentDossierMc').load({
                url: Ext.getStore('ArchiveDocumentDossierMc').getProxy().url + '/' + record.get('id')
            });
        }
    },

    onPanelBoxReady: function (component, width, height, eOpts) {
        Ext.getStore('ArchiveDocumentDossierMc').load();
        Ext.getCmp('ArchiveDossierMcPnl').paginazione = [];
    },

    onArchiveDocumentsGridItemContextMenu: function (dataview, record, item, index, e, eOpts) {
        e.stopEvent();
        var newX = e.xy[0],
            newY = e.xy[1],
            disabled = false,
            storeA, storeU, storeO;

        if (
            record.get('action_protocol_date') !== null ||
            record.get('action_albo_date') !== null ||
            record.get('action_sign_date') !== null ||
            record.get('action_archive_date') !== null ||
            record.get('action_trasparenza_date') !== null) {
            Ext.getCmp('contextDocumentDelete').setDisabled(true);
            disabled = true;
        } else {
            Ext.getCmp('contextDocumentDelete').setDisabled(false);
            disabled = false;
        }

        /*if(record.get('completed')) {
            Ext.getCmp('ArchiveEditMnReopen').show();
        } else {
            Ext.getCmp('ArchiveEditMnReopen').hide();
        }*/

        // Ext.getCmp('ArchiveEditMn').showAt([newX,newY]);
        var m = Ext.getCmp('ArchivePnl').generateAssignMn(record, ['A']).showAt(e.xy[0], e.xy[1]);

        var editItem = Ext.create('Ext.menu.Item', {
            xtype: 'menuitem',
            handler: function (item, e) {
                //var doc = Ext.getCmp('ArchiveDocumentsQueueGrid').getSelectionModel().getSelection()[0];
                var doc = record;

                Ext.getCmp('ArchivePnl').editDocument(doc);

            },
            iconCls: 'icon-pencil',
            text: 'Modifica'
        });

        var mailItem = Ext.create('Ext.menu.Item', {
            xtype: 'menuitem',
            handler: function (item, e) {
                var sel = record,
                    data = {
                        subject: sel.get('short_description'),
                        document: sel.get('id')
                    };

                Ext.widget('ArchiveMailSendWin').show();

                Ext.getStore('ArchiveDocumentFiles').load({
                    params: {
                        document: sel.get('id')
                    }
                });

                Ext.getCmp('SendMailFrm').getForm().setValues(data);
            },
            iconCls: 'icon-email',
            text: 'Invia mail'
        });

        var deleteItem = Ext.create('Ext.menu.Item', {
            xtype: 'menuitem',
            handler: function (item, e) {

                Ext.Msg.show({
                    title: record.get('filename'),
                    msg: 'Sei sicuro di voler eliminare questo Documento?',
                    buttons: Ext.Msg.YESNO,
                    fn: function (r) {
                        if (r == 'yes') {
                            storeA = Ext.getStore('ArchiveDocumentsArchived');
                            storeU = Ext.getStore('ArchiveDocumentsUser');
                            storeO = Ext.getStore('ArchiveDocumentsOffice');
                            storeA.remove(record);
                            storeA.sync({
                                callback: function () {
                                    storeU.load();
                                    storeO.load();
                                },
                                failure: function () {
                                    Ext.Msg.alert('Attenzione', 'Cancellazione NON avvenuta.');
                                }
                            });
                        }
                    }
                });
            },
            //    id: 'contextArchiveDocumentsQueueDelete',
            //    itemId: 'contextArchiveDocumentsQueueDelete',
            permissible: true,
            iconCls: 'icon-cancel',
            disabled: disabled,
            text: 'Elimina'
        });

        if (!record.get('completed')) {
            m.add(editItem);
            m.add(deleteItem);
            m.add(mailItem);
        }

        m.showAt([newX, newY]);
    },

    onArchiveDocumentsGridItemDblClick: function (dataview, record, item, index, e, eOpts) {
        Ext.getCmp('ArchivePnl').viewDocument(record);
    },

    onArchiveArchivedSearchTxtChange: function (field, newValue, oldValue, eOpts) {
        Ext.getStore('ArchiveDocumentsArchived').load();
    },

    onViewItemClick: function (dataview, record, item, index, e, eOpts) {
        Ext.getStore('Raccoltes').clearFilter();
        Ext.getStore('Raccoltes').filter('parent', record.get('id'));
    },

    onGridpanelBoxReady: function (component, width, height, eOpts) {
        Ext.getStore('Raccoltes').filter('parent', '0');
    },

    onPanelShow: function (component, eOpts) {
        Ext.getStore('Raccoltes').clearFilter();
        Ext.getStore('Raccoltes').filter('parent', '0');
    },

    onArchiveMainPnlBeforeTabChange: function (tabPanel, newCard, oldCard, eOpts) {
        Ext.getCmp('ArchiveFilterForm').applyFilter();
        Ext.getStore('ArchiveMails').load();
        Ext.getStore('ArchiveMailsUser').load();
        Ext.getStore('ArchiveMailsOffice').load();
        Ext.getStore('ArchiveDocumentsUser').load();
        Ext.getStore('ArchiveDocumentsOffice').load();
        Ext.getCmp('ArchiveMailPnl').countMail();

        var panelId = newCard.getId();
        if (panelId === 'ArchiveIncomingPnl' || panelId === 'ArchiveQueuePnl' || panelId === 'ArchiveDossierPnl') {
            Ext.getCmp('ArchiveLeftPnl').hide();
        } else {
            Ext.getCmp('ArchiveLeftPnl').show();
        }
    },

    onArchivePnlBoxReady: function (component, width, height, eOpts) {
        // Fix bug on protocol store modified from archive pnl
        Ext.getCmp('ProtocolPnl').show();
        Ext.getCmp('ProtocolPnl').hide();

        var sc = Ext.getStore('ArchiveClassesFilter'),
            cc = Ext.getCmp('ArchiveFilterClass');

        sc.on('load', function (store, records, options) {
            cc.select(0);
        });

        sc.load();

        Ext.getStore('ArchiveMailSecurity').load();
        Ext.getStore('ArchiveMailProtocols').load();
        Ext.getStore('Assignees').load();

        // Ext.getStore('ArchiveMails').load();
        // Ext.getStore('ArchiveMailsUser').load();
        // Ext.getStore('ArchiveDocumentsUser').load();
        //Ext.getStore('ArchiveOffices').load();
        Ext.getStore('SettingsUsers').load();
        Ext.getStore('ArchiveTemplates').load();
        Ext.getStore('ArchiveDocumentDossier').load();
    },

    generateAssignMn: function (obj, sections) {
        var mm,
            mif, mia,
            items = [],
            ap = Ext.getCmp('ArchivePnl');

        // Protocollo
        items.push(
            {
                xtype: 'menuitem',
                text: 'Protocolla',
                iconCls: 'icon-page_portrait',
                handler: function () {
                    ap.assignTo('U', obj, mc2ui.app.settings.uid);
                    Ext.getCmp('ArchiveDocumentUploadWin').setTitle('Carica documento e protocolla');
                    Ext.getCmp('ArchiveProtocolBtn').show();
                    Ext.getCmp('ArchiveUploadDocumentBtn').hide();
                }
            },
            {
                xtype: 'menuitem',
                handler: function (item, e) {
                    Ext.getCmp('MailAccountPnl').openSendMailWin(obj.get('id'), 'reply');
                },
                iconCls: 'icon-arrow_turn_left',
                text: 'Rispondi'
            },
            {
                xtype: 'menuitem',
                handler: function (item, e) {
                    Ext.getCmp('MailAccountPnl').openSendMailWin(obj.get('id'));
                },
                iconCls: 'icon-arrow_turn_right',
                text: 'Inoltra'
            }
        );

        if (sections.indexOf('M') >= 0 && mc2ui.app.settings.areaArchive) { // Mail farward
            items.push(
                {
                    xtype: 'label',
                    html: '<i>Inoltra a </i>'
                },
                {
                    xtype: 'menuitem',
                    text: 'Persona',
                    iconCls: 'icon-user',
                    menu: {
                        xtype: 'menu',
                        id: 'ArchiveForwardUserMn',
                        itemId: 'ArchiveForwardUserMn',
                        //width: 120
                    }
                },
                {
                    xtype: 'menuitem',
                    text: 'Ufficio',
                    iconCls: 'icon-building',
                    menu: {
                        xtype: 'menu',
                        id: 'ArchiveFarwardOfficeMn',
                        itemId: 'ArchiveFarwardOfficeMn',
                        //width: 120
                    }
                }

            );

        }

        /*if (sections.indexOf('F') >= 0 && !obj.get('completed')) { // Flux
            items.push(
                {
                    xtype: 'menuseparator',
                },
                {
                    xtype: 'menuitem',
                    text: 'Abbina a flusso',
                    iconCls: 'icon-arrow_right',
                    menu: {
                        xtype: 'menu',
                        id: 'ArchiveAssignFluxMn',
                        itemId: 'ArchiveAssignFluxMn',
                        //width: 120
                    }
                },
                {
                    xtype: 'menuseparator',
                }
            );
        }*/
        if (sections.indexOf('A') >= 0 && !obj.get('completed') && mc2ui.app.settings.areaArchive) { // Assign to user/office
            items.push(
                {
                    xtype: 'label',
                    html: '<i>Assegna a </i>'
                },
                {
                    xtype: 'menuitem',
                    text: 'Persona',
                    iconCls: 'icon-user',
                    menu: {
                        xtype: 'menu',
                        id: 'ArchiveAssignUserMn',
                        itemId: 'ArchiveAssignUserMn',
                        //width: 120
                    }
                },
                {
                    xtype: 'menuitem',
                    text: 'Ufficio',
                    iconCls: 'icon-building',
                    menu: {
                        xtype: 'menu',
                        id: 'ArchiveAssignOfficeMn',
                        itemId: 'ArchiveAssignOfficeMn',
                        //width: 120
                    }
                }
            );
        }
        if (sections.indexOf('M') < 0) { // if not mail, you have selected a document, so show "presa visione"
            if (obj.get('to_check')) {
                items.push(
                    {
                        xtype: 'menuitem',
                        text: 'Prendi visione',
                        iconCls: 'icon-accept',
                        handler: function () {
                            Ext.Ajax.request({
                                url: '/mc2-api/archive/document/' + obj.get('id'),
                                method: 'PUT',
                                params: {
                                    checked: true,
                                    user: parseInt(mc2ui.app.settings.uid),
                                },
                                callback: function () {
                                    Ext.Msg.alert('SUCCESSO', 'Hai preso visione del file');
                                    Ext.getStore('ArchiveDocumentsUser').load();
                                    Ext.getStore('ArchiveDocumentsOffice').load();
                                    Ext.getStore('ArchiveDocumentsArchived').load();
                                }
                            });
                        }
                    }
                );
            } else {
                items.push(
                    {
                        xtype: 'menuitem',
                        text: 'Presa visione',
                        iconCls: 'icon-page_white_find',
                        handler: function () {
                            Ext.widget('ArchiveDocumentCheckWin').show();
                            Ext.getCmp('ArchiveDocumentCheckWin').record = obj;
                            Ext.getStore('Assignees').filter(function (rec) {
                                return rec.get('type') == 'U';
                            });
                        }
                    }
                );
            }

            if (obj.get('completed')) {
                items.push({
                    xtype: 'menuitem',
                    text: 'Riapri fascicolo',
                    iconCls: 'icon-lock_open',
                    handler: function () {

                        Ext.Ajax.request({
                            url: '/mc2-api/archive/document/' + obj.get('id'),
                            method: 'PUT',
                            params: {
                                completed: 0
                            },
                            success: function () {
                                obj.set('completed', null);
                                Ext.Msg.alert('SUCCESSO', 'Fascicolo riaperto correttamente');
                            }
                        });
                    }
                });
            } else {
                items.push({
                    xtype: 'menuitem',
                    text: 'Completato',
                    iconCls: 'icon-flag_checked',
                    handler: function () {
                        Ext.Ajax.request({
                            url: '/mc2-api/archive/document/' + obj.get('id'),
                            method: 'PUT',
                            params: {
                                completed: 1,
                            },
                            callback: function () {
                                Ext.Msg.alert('SUCCESSO', 'Il fascicolo è stato completato');
                                Ext.getStore('ArchiveDocumentsUser').load();
                                Ext.getStore('ArchiveDocumentsOffice').load();
                                Ext.getStore('ArchiveDocumentsArchived').load();
                            }
                        });
                    }
                });
            }
            // Fascicolo
            items.push(
                {
                    xtype: 'menuitem',
                    text: 'Assegna a fascicolo',
                    iconCls: 'icon-book_addresses',
                    handler: function () {
                        Ext.widget('ArchiveDossierWin').show();
                        Ext.getStore('ArchiveDossiers').load();
                        Ext.getStore('ArchiveDocumentDossierLinked').removeAll();
                        Ext.Ajax.request({
                            url: '/mc2-api/archive/document/' + obj.get('id') + '/dossier', success: function (r) {
                                Ext.getStore('ArchiveDocumentDossierLinked').add(Ext.decode(r.responseText).results);
                            }
                        });
                        Ext.getCmp('ArchiveDossierWin').archiveDocument = obj;
                    }
                }
            );
        }



        mm = Ext.getCmp('ArchiveAssignMn');
        if (typeof mm !== 'undefined') {
            mm.destroy();
        }

        mm = Ext.create('Ext.menu.Menu', {
            id: 'ArchiveAssignMn',
            items: items
        });

        if (!obj.get('completed')) {
            Ext.each(Ext.getStore('Assignees').getRange(), function (v) {
                if (v.get('type') == 'U') {
                    mif = Ext.create('Ext.menu.Item', {
                        text: v.get('name'),
                        iconCls: 'icon-user',
                        handler: function () {
                            ap.farwardTo(v.get('type'), obj, v.get('id'));
                        }
                    });

                    if (sections.indexOf('M') >= 0 && mc2ui.app.settings.areaArchive) {
                        Ext.getCmp('ArchiveForwardUserMn').add(mif);
                    }

                    mia = Ext.create('Ext.menu.Item', {
                        text: v.get('name'),
                        iconCls: 'icon-user',
                        handler: function () {
                            ap.assignTo(v.get('type'), obj, v.get('id'));
                        }
                    });
                    if (sections.indexOf('A') >= 0 && mc2ui.app.settings.areaArchive) {
                        Ext.getCmp('ArchiveAssignUserMn').add(mia);
                    }
                } else if (v.get('type') == 'O') {
                    mif = Ext.create('Ext.menu.Item', {
                        text: v.get('name'),
                        iconCls: 'icon-building',
                        handler: function () {
                            ap.farwardTo(v.get('type'), obj, v.get('id'));
                        }
                    });
                    if (sections.indexOf('M') >= 0 && mc2ui.app.settings.areaArchive) {
                        Ext.getCmp('ArchiveFarwardOfficeMn').add(mif);
                    }

                    mia = Ext.create('Ext.menu.Item', {
                        text: v.get('name'),
                        iconCls: 'icon-building',
                        handler: function () {
                            ap.assignTo(v.get('type'), obj, v.get('id'));
                        }
                    });
                    if (sections.indexOf('A') >= 0 && mc2ui.app.settings.areaArchive) {
                        Ext.getCmp('ArchiveAssignOfficeMn').add(mia);
                    }
                } else if (v.get('type') == 'F') {
                    mia = Ext.create('Ext.menu.Item', {
                        text: v.get('name'),
                        iconCls: 'icon-arrow_right',
                        handler: function () {
                            ap.assignTo(v.get('type'), obj, v.get('id'));
                        }
                    });

                    if (sections.indexOf('F') >= 0) {
                        Ext.getCmp('ArchiveAssignFluxMn').add(mia);
                    }
                }

            });
        }





        return mm;
    },

    editDocument: function (record) {
        var win = Ext.widget('ArchiveDocumentEditWin').show();
        win.record = record;
        Ext.getCmp('ArchiveDocumentEditForm').loadRecord(record);
    },

    viewDocument: function (record) {
        Ext.widget('ArchiveDocumentViewWin').show();

        Ext.Ajax.request({
            url: '/mc2-api/archive/document/' + record.get('id'),
            success: function (r) {
                var res = Ext.decode(r.responseText);

                if (res.success === true) {
                    Ext.getCmp('ArchiveDocumentUploadDate').setText(res.results.upload_date);
                    Ext.getCmp('ArchiveDocumentShortDescription').setText(res.results.short_description);
                    Ext.getCmp('ArchiveDocumentNote').setText(res.results.description);
                    Ext.getCmp('ArchiveDocumentCompleteDate').setText(res.results.completed);

                    // Files
                    var labelFiles;
                    Ext.each(res.results.files, function (f) {
                        labelFiles = Ext.create('Ext.form.Label', {
                            html: '<a href="/mc2-api/archive/document_file/' + f.id + '?header=1">' + f.filename + '</a>',
                            padding: 5
                        });
                        Ext.getCmp('ArchiveDocumentFiles').add(labelFiles);
                    });

                    // Checks
                    var labelChecks,
                        html;
                    Ext.each(res.results.checks, function (c) {
                        html = "L'utente " + c.user;
                        html += c.checked !== null ? " ha preso visione in data " + c.checked : " non ha preso visione";
                        labelChecks = Ext.create('Ext.form.Label', {
                            html: html,
                            padding: 5
                        });
                        Ext.getCmp('ArchiveDocumentChecks').add(labelChecks);
                    });

                    // Logs
                    var labelLogs;
                    Ext.each(res.results.logs, function (l) {
                        labelLogs = Ext.create('Ext.form.Label', {
                            html: l.date + ' - ' + l.description,
                            padding: 5
                        });
                        Ext.getCmp('ArchiveDocumentHistory').add(labelLogs);
                    });
                }
            }
        });
    },



    assignTo: function (type, obj, toId) {
        var win,
            params = {
                'assign_from_user': parseInt(mc2ui.app.settings.uid)
            };

        win = Ext.getCmp('ArchiveMailViewWin');

        //if(typeof win != 'undefined') {
        if (obj.store.model.$className === 'mc2ui.model.ArchiveMail') {

            //win.close();

            Ext.widget('ArchiveDocumentUploadWin').show();

            Ext.getStore('Assignees').filter('id', new RegExp("^" + toId + "$"));
            Ext.getStore('Assignees').filter('type', type);
            Ext.getCmp('ArchiveUploadClass').record = Ext.getStore('Assignees').getRange()[0];
            if (Ext.getCmp('ArchiveUploadClass').record) {
                Ext.getCmp('ArchiveUploadClass').setValue(Ext.getCmp('ArchiveUploadClass').record.get('id'));
            }

            Ext.getStore('Assignees').clearFilter();

            Ext.getCmp('ArchiveAttachmentCnt').show();
            Ext.getCmp('ArchiveUploadFile').hide();
            Ext.getCmp('ArchiveUploadClass').hide();

            Ext.getCmp('ArchiveUploadAssignFromUser').setValue(mc2ui.app.settings.uid);
            /*if(type == 'U') {
                Ext.getCmp('ArchiveUploadAssignToUser').setValue(toId);
            }
            if(type == 'O') {
                Ext.getCmp('ArchiveUploadAssignToOffice').setValue(toId);
            }
            if(type == 'F') {
                Ext.getCmp('ArchiveUploadClass').setValue(toId);
            }*/



            Ext.getCmp('ArchiveUploadShortDescription').setValue(obj.get('from') + ' - ' + obj.get('subject'));
            Ext.getCmp('ArchiveUploadOriginId').setValue(2); // Mail origin
            Ext.getCmp('ArchiveUploadMailIdAccount').setValue(obj.get('id') + '_' + obj.get('account')); // Mail id_account

            var cg = Ext.getCmp('ArhiveCheckBoxGroupAttachment'),
                ch;

            Ext.getStore('ArchiveMailAttachments').load({
                params: {
                    account: obj.get('account'),
                    mail: obj.get('id')
                },
                callback: function (a, r) {
                    var res = Ext.decode(r.response.responseText);
                    console.log(res.results);
                    Ext.each(res.results, function (attachment) {
                        cg.add(Ext.form.field.Checkbox({
                            boxLabel: attachment.name,
                            name: 'attachment_' + attachment.id,
                            inputValue: 'on',
                            uncheckedValue: 'off',
                            id: 'checkbox_' + attachment.id
                        }));
                    });
                }
            });



        } else {
            if (type == 'O') {
                params.assign_to_office = toId;
            } else if (type == 'U') {
                params.assign_to_user = toId;
            } else if (type == 'F') {
                params.class_id = toId;
            }

            Ext.Ajax.request({
                method: 'PUT',
                url: '/mc2-api/archive/document/' + obj.get('id'),
                params: params,
                success: function () {
                    Ext.getStore('ArchiveDocumentsUser').load();
                    Ext.getStore('ArchiveDocumentsOffice').load();
                    Ext.getStore('ArchiveMailsOffice').load();
                    Ext.getStore('ArchiveMailsUser').load();
                    Ext.getStore('ArchiveDocumentsArchived').load();
                }
            });
        }

    },

    farwardTo: function (type, mail, toId) {
        var win, params = {
            'assign_from_user': parseInt(mc2ui.app.settings.uid)
        };

        if (type == 'O') {
            params.assign_to_office = toId;
        } else if (type == 'U') {
            params.assign_to_user = toId;
        }

        Ext.Ajax.request({
            method: 'PUT',
            url: '/mc2-api/archive/mail/mail/' + mail.get('id'),
            params: params,
            success: function () {
                Ext.getStore('ArchiveMailsUser').load();
                Ext.getStore('ArchiveMailsOffice').load();
                Ext.getStore('ArchiveMails').load();

                win = Ext.getCmp('ArchiveMailViewWin');
                if (typeof win != 'undefined') {
                    win.close();
                }
            }
        });
    },

    populateProtocol: function (model) {
        if (model > 0) {
            Ext.Ajax.request({
                url: '/mc2-api/archive/model/' + model,
                success: function (res) {
                    var r = Ext.decode(res.responseText);
                    var model = r.results,
                        template = Ext.decode(model.template);
                    Ext.getCmp('ProtocolNewType').setValue(template.protocol.type);
                    Ext.getCmp('ProtocolNewDescription').setValue(template.protocol.object);
                }
            });
        }
    },

    populateAlbo: function (model) {
        Date.prototype.addDays = function (days) {
            var dat = new Date(this.valueOf());
            dat.setDate(dat.getDate() + days);
            return dat;
        };


        Ext.Ajax.request({
            url: '/mc2-api/archive/model/' + model,
            success: function (res) {
                var r = Ext.decode(res.responseText);
                var model = r.results,
                    template = Ext.decode(model.template);
                Ext.getCmp('AlboPublicationEditTitle').setValue(template.albo.title);

                var dd = new Date();
                Ext.getCmp('AlboPublicationEditStartDate').setValue(dd.addDays(template.albo.start));
                Ext.getCmp('AlboPublicationEditExpirationDate').setValue(dd.addDays(template.albo.start + template.albo.total_days));
                Ext.getCmp('AlboPublicationEditCategory').setValue(template.albo.category);
                Ext.getCmp('AlboPublicationEditArea').setValue(template.albo.area);
                Ext.getCmp('AlboPublicationEditEntity').setValue(template.albo.ente);
            }
        });
    },

    populateTrasparenza: function (model) {
        Ext.Ajax.request({
            url: '/mc2-api/archive/model/' + model,
            success: function (res) {
                var r = Ext.decode(res.responseText);
                var model = r.results,
                    template = Ext.decode(model.template);

                Ext.each(template.trasparenza.voices, function (v) {
                    Ext.getCmp('TrasparenzaVoicePickerGrid').getSelectionModel().select(v, true);
                });
            }
        });
    }

});