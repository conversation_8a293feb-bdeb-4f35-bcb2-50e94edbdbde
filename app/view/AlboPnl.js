/*
 * File: app/view/AlboPnl.js
 *
 * This file was generated by Sencha Architect version 3.2.0.
 * http://www.sencha.com/products/architect/
 *
 * This file requires use of the Ext JS 4.2.x library, under independent license.
 * License of Sencha Architect does not include license for Ext JS 4.2.x. For more
 * details see http://www.sencha.com/license <NAME_EMAIL>.
 *
 * This file will be auto-generated each and everytime you save your project.
 *
 * Do NOT hand edit this file.
 */

Ext.define('mc2ui.view.AlboPnl', {
    extend: 'Ext.panel.Panel',
    alias: 'widget.AlboPnl',

    requires: [
        'Ext.form.Panel',
        'Ext.button.Button',
        'Ext.form.FieldSet',
        'Ext.form.field.ComboBox',
        'Ext.form.field.Date',
        'Ext.form.field.Checkbox',
        'Ext.grid.Panel',
        'Ext.grid.column.Action',
        'Ext.grid.column.Date',
        'Ext.grid.View',
        'Ext.toolbar.Paging',
        'Ext.menu.Menu',
        'Ext.menu.Separator',
        'Ext.toolbar.Separator',
        'Ext.toolbar.Spacer'
    ],

    border: false,
    hidden: true,
    id: 'AlboPnl',
    itemId: 'AlboPnl',
    layout: 'border',
    header: false,
    title: 'Albo Pretorio',
    titleAlign: 'center',

    initComponent: function() {
        var me = this;

        Ext.applyIf(me, {
            items: [
                {
                    xtype: 'panel',
                    region: 'west',
                    split: true,
                    id: 'AlboLeftPnl',
                    itemId: 'AlboLeftPnl',
                    width: 225,
                    autoScroll: true,
                    layout: 'fit',
                    collapseDirection: 'left',
                    collapsed: true,
                    collapsible: true,
                    iconCls: 'icon-find',
                    title: 'Filtri',
                    titleCollapse: true,
                    items: [
                        {
                            xtype: 'form',
                            applyFilter: function() {
                                if (mc2ui.app.alboFilterEventTimeoutId > 0) {
                                    clearTimeout(mc2ui.app.alboFilterEventTimeoutId);
                                }

                                mc2ui.app.alboFilterEventTimeoutId = setTimeout(function() {
                                    Ext.getStore('AlboPublications').loadPage(1);
                                }, 1000);
                            },
                            border: false,
                            id: 'AlboFilterForm',
                            itemId: 'AlboFilterForm',
                            autoScroll: true,
                            bodyCls: 'bck-content',
                            bodyPadding: 10,
                            header: false,
                            layout: {
                                type: 'vbox',
                                align: 'stretch'
                            },
                            items: [
                                {
                                    xtype: 'button',
                                    handler: function(button, e) {
                                        var form = Ext.getCmp('AlboFilterForm').getForm(),
                                            cp = Ext.getCmp('AlboFilterPublication'),
                                            dps = Ext.getCmp('AlboFilterPublishedStart'),
                                            dpe = Ext.getCmp('AlboFilterPublishedEnd'),
                                            dss = Ext.getCmp('AlboFilterStartStart'),
                                            dse = Ext.getCmp('AlboFilterStartEnd'),
                                            des = Ext.getCmp('AlboFilterEndStart'),
                                            dee = Ext.getCmp('AlboFilterEndEnd'),
                                            cs = Ext.getCmp('AlboFilterStatus'),
                                            cx = Ext.getCmp('AlboFilterExpiration'),
                                            ca = Ext.getCmp('AlboFilterArea'),
                                            cc = Ext.getCmp('AlboFilterCategory'),
                                            ce = Ext.getCmp('AlboFilterEntity'),
                                            tt = Ext.getCmp('AlboFilterTitle'),
                                            td = Ext.getCmp('AlboFilterDescription'),
                                            rd = Ext.getCmp('AlboFilterDocument');

                                        form.reset();

                                        cp.select('A');
                                        cs.select('A');
                                        cx.select('A');
                                        ca.select(0);
                                        cc.select(0);
                                        ce.select(0);
                                    },
                                    margin: '0 0 10 0',
                                    iconCls: 'icon-arrow_undo',
                                    text: 'Reimposta'
                                },
                                {
                                    xtype: 'fieldset',
                                    title: 'Pubblicazione',
                                    layout: {
                                        type: 'vbox',
                                        align: 'stretch'
                                    },
                                    items: [
                                        {
                                            xtype: 'combobox',
                                            id: 'AlboFilterPublication',
                                            itemId: 'AlboFilterPublication',
                                            name: 'publication',
                                            allowBlank: false,
                                            allowOnlyWhitespace: false,
                                            editable: false,
                                            matchFieldWidth: false,
                                            forceSelection: true,
                                            queryMode: 'local',
                                            store: 'AlboPublishedFilter',
                                            valueField: 'id',
                                            listeners: {
                                                change: {
                                                    fn: me.onAlboFilterPublicationChange,
                                                    buffer: 1000,
                                                    scope: me
                                                }
                                            }
                                        }
                                    ]
                                },
                                {
                                    xtype: 'fieldset',
                                    title: 'Periodo pubblicazione',
                                    layout: {
                                        type: 'vbox',
                                        align: 'stretch'
                                    },
                                    items: [
                                        {
                                            xtype: 'datefield',
                                            endDateField: 'AlboFilterPublishedEnd',
                                            id: 'AlboFilterPublishedStart',
                                            itemId: 'AlboFilterPublishedStart',
                                            fieldLabel: 'Dal',
                                            labelAlign: 'right',
                                            labelWidth: 50,
                                            name: 'published_start',
                                            vtype: 'daterange',
                                            editable: false,
                                            format: 'd/m/Y',
                                            startDay: 1,
                                            submitFormat: 'd-m-Y',
                                            listeners: {
                                                change: {
                                                    fn: me.onAlboFilterPublishedStartChange,
                                                    buffer: 1000,
                                                    scope: me
                                                }
                                            }
                                        },
                                        {
                                            xtype: 'datefield',
                                            startDateField: 'AlboFilterPublishedStart',
                                            id: 'AlboFilterPublishedEnd',
                                            itemId: 'AlboFilterPublishedEnd',
                                            fieldLabel: 'al',
                                            labelAlign: 'right',
                                            labelWidth: 50,
                                            name: 'published_end',
                                            vtype: 'daterange',
                                            editable: false,
                                            format: 'd/m/Y',
                                            startDay: 1,
                                            submitFormat: 'd-m-Y',
                                            listeners: {
                                                change: {
                                                    fn: me.onAlboFilterPublishedEndChange,
                                                    buffer: 1000,
                                                    scope: me
                                                }
                                            }
                                        }
                                    ]
                                },
                                {
                                    xtype: 'fieldset',
                                    title: 'Inizio validità',
                                    layout: {
                                        type: 'vbox',
                                        align: 'stretch'
                                    },
                                    items: [
                                        {
                                            xtype: 'datefield',
                                            endDateField: 'AlboFilterStartEnd',
                                            id: 'AlboFilterStartStart',
                                            itemId: 'AlboFilterStartStart',
                                            fieldLabel: 'Dal',
                                            labelAlign: 'right',
                                            labelWidth: 50,
                                            name: 'start_start',
                                            vtype: 'daterange',
                                            editable: false,
                                            format: 'd/m/Y',
                                            startDay: 1,
                                            submitFormat: 'd-m-Y',
                                            listeners: {
                                                change: {
                                                    fn: me.onAlboFilterStartStartChange,
                                                    buffer: 1000,
                                                    scope: me
                                                }
                                            }
                                        },
                                        {
                                            xtype: 'datefield',
                                            startDateField: 'AlboFilterStartStart',
                                            id: 'AlboFilterStartEnd',
                                            itemId: 'AlboFilterStartEnd',
                                            fieldLabel: 'al',
                                            labelAlign: 'right',
                                            labelWidth: 50,
                                            name: 'start_end',
                                            vtype: 'daterange',
                                            editable: false,
                                            format: 'd/m/Y',
                                            startDay: 1,
                                            submitFormat: 'd-m-Y',
                                            listeners: {
                                                change: {
                                                    fn: me.onAlboFilterStartEndChange,
                                                    buffer: 1000,
                                                    scope: me
                                                }
                                            }
                                        }
                                    ]
                                },
                                {
                                    xtype: 'fieldset',
                                    title: 'Fine validità',
                                    layout: {
                                        type: 'vbox',
                                        align: 'stretch'
                                    },
                                    items: [
                                        {
                                            xtype: 'datefield',
                                            endDateField: 'AlboFilterEndEnd',
                                            id: 'AlboFilterEndStart',
                                            itemId: 'AlboFilterEndStart',
                                            fieldLabel: 'Dal',
                                            labelAlign: 'right',
                                            labelWidth: 50,
                                            name: 'end_start',
                                            vtype: 'daterange',
                                            editable: false,
                                            format: 'd/m/Y',
                                            startDay: 1,
                                            submitFormat: 'd-m-Y',
                                            listeners: {
                                                change: {
                                                    fn: me.onAlboFilterEndStartChange,
                                                    buffer: 1000,
                                                    scope: me
                                                }
                                            }
                                        },
                                        {
                                            xtype: 'datefield',
                                            startDateField: 'AlboFilterEndStart',
                                            id: 'AlboFilterEndEnd',
                                            itemId: 'AlboFilterEndEnd',
                                            fieldLabel: 'al',
                                            labelAlign: 'right',
                                            labelWidth: 50,
                                            name: 'end_end',
                                            vtype: 'daterange',
                                            editable: false,
                                            format: 'd/m/Y',
                                            startDay: 1,
                                            submitFormat: 'd-m-Y',
                                            listeners: {
                                                change: {
                                                    fn: me.onAlboFilterEndEndChange,
                                                    buffer: 1000,
                                                    scope: me
                                                }
                                            }
                                        }
                                    ]
                                },
                                {
                                    xtype: 'fieldset',
                                    title: 'Stato',
                                    layout: {
                                        type: 'vbox',
                                        align: 'stretch'
                                    },
                                    items: [
                                        {
                                            xtype: 'combobox',
                                            id: 'AlboFilterStatus',
                                            itemId: 'AlboFilterStatus',
                                            name: 'status',
                                            allowBlank: false,
                                            allowOnlyWhitespace: false,
                                            editable: false,
                                            matchFieldWidth: false,
                                            forceSelection: true,
                                            queryMode: 'local',
                                            store: 'AlboStatusesFilter',
                                            valueField: 'id',
                                            listeners: {
                                                change: {
                                                    fn: me.onAlboFilterStatusChange,
                                                    buffer: 1000,
                                                    scope: me
                                                }
                                            }
                                        }
                                    ]
                                },
                                {
                                    xtype: 'fieldset',
                                    title: 'Scadenza',
                                    layout: {
                                        type: 'vbox',
                                        align: 'stretch'
                                    },
                                    items: [
                                        {
                                            xtype: 'combobox',
                                            id: 'AlboFilterExpiration',
                                            itemId: 'AlboFilterExpiration',
                                            name: 'expiration',
                                            allowBlank: false,
                                            allowOnlyWhitespace: false,
                                            editable: false,
                                            matchFieldWidth: false,
                                            forceSelection: true,
                                            queryMode: 'local',
                                            store: 'AlboExpirationsFilter',
                                            valueField: 'id',
                                            listeners: {
                                                change: {
                                                    fn: me.onAlboFilterExpirationChange,
                                                    buffer: 1000,
                                                    scope: me
                                                }
                                            }
                                        }
                                    ]
                                },
                                {
                                    xtype: 'fieldset',
                                    title: 'Area',
                                    layout: {
                                        type: 'vbox',
                                        align: 'stretch'
                                    },
                                    items: [
                                        {
                                            xtype: 'combobox',
                                            id: 'AlboFilterArea',
                                            itemId: 'AlboFilterArea',
                                            name: 'area',
                                            allowBlank: false,
                                            allowOnlyWhitespace: false,
                                            editable: false,
                                            matchFieldWidth: false,
                                            displayField: 'name',
                                            forceSelection: true,
                                            queryMode: 'local',
                                            store: 'AlboAreasFilter',
                                            valueField: 'id',
                                            listeners: {
                                                change: {
                                                    fn: me.onAlboFilterAreaChange,
                                                    buffer: 1000,
                                                    scope: me
                                                }
                                            }
                                        }
                                    ]
                                },
                                {
                                    xtype: 'fieldset',
                                    title: 'Categoria',
                                    layout: {
                                        type: 'vbox',
                                        align: 'stretch'
                                    },
                                    items: [
                                        {
                                            xtype: 'combobox',
                                            id: 'AlboFilterCategory',
                                            itemId: 'AlboFilterCategory',
                                            name: 'category',
                                            allowBlank: false,
                                            allowOnlyWhitespace: false,
                                            editable: false,
                                            matchFieldWidth: false,
                                            displayField: 'name',
                                            forceSelection: true,
                                            queryMode: 'local',
                                            store: 'AlboCategoriesFilter',
                                            valueField: 'id',
                                            listeners: {
                                                change: {
                                                    fn: me.onAlboFilterCategoryChange,
                                                    buffer: 1000,
                                                    scope: me
                                                }
                                            }
                                        }
                                    ]
                                },
                                {
                                    xtype: 'fieldset',
                                    title: 'Ente',
                                    layout: {
                                        type: 'vbox',
                                        align: 'stretch'
                                    },
                                    items: [
                                        {
                                            xtype: 'combobox',
                                            id: 'AlboFilterEntity',
                                            itemId: 'AlboFilterEntity',
                                            name: 'entity',
                                            allowBlank: false,
                                            allowOnlyWhitespace: false,
                                            editable: false,
                                            matchFieldWidth: false,
                                            displayField: 'name',
                                            forceSelection: true,
                                            queryMode: 'local',
                                            store: 'AlboEntitiesFilter',
                                            valueField: 'id',
                                            listeners: {
                                                change: {
                                                    fn: me.onAlboFilterEntityChange,
                                                    buffer: 1000,
                                                    scope: me
                                                }
                                            }
                                        }
                                    ]
                                },
                                {
                                    xtype: 'fieldset',
                                    title: 'Titolo o Descrizione',
                                    layout: {
                                        type: 'vbox',
                                        align: 'stretch'
                                    },
                                    items: [
                                        {
                                            xtype: 'textfield',
                                            id: 'AlboFilterText',
                                            itemId: 'AlboFilterText',
                                            name: 'text',
                                            listeners: {
                                                change: {
                                                    fn: me.onAlboFilterTextChange,
                                                    buffer: 1000,
                                                    scope: me
                                                }
                                            }
                                        }
                                    ]
                                },
                                {
                                    xtype: 'fieldset',
                                    title: 'Contenuto',
                                    layout: {
                                        type: 'vbox',
                                        align: 'stretch'
                                    },
                                    items: [
                                        {
                                            xtype: 'textfield',
                                            id: 'AlboFilterDescription',
                                            itemId: 'AlboFilterDescription',
                                            name: 'description',
                                            listeners: {
                                                change: {
                                                    fn: me.onAlboFilterDescriptionChange,
                                                    buffer: 1000,
                                                    scope: me
                                                }
                                            }
                                        }
                                    ]
                                },
                                {
                                    xtype: 'fieldset',
                                    title: 'Dettagli',
                                    layout: {
                                        type: 'vbox',
                                        align: 'stretch'
                                    },
                                    items: [
                                        {
                                            xtype: 'checkboxfield',
                                            flex: 1,
                                            id: 'AlboFilterDocument',
                                            itemId: 'AlboFilterDocument',
                                            name: 'document',
                                            boxLabel: 'Con Allegati',
                                            uncheckedValue: 'off',
                                            listeners: {
                                                change: {
                                                    fn: me.onAlboFilterDocumentChange,
                                                    buffer: 1000,
                                                    scope: me
                                                }
                                            }
                                        }
                                    ]
                                }
                            ]
                        }
                    ]
                },
                {
                    xtype: 'panel',
                    region: 'center',
                    split: true,
                    id: 'AlboMainPnl',
                    layout: {
                        type: 'vbox',
                        align: 'stretch'
                    },
                    items: [
                        {
                            xtype: 'gridpanel',
                            flex: 1,
                            border: false,
                            id: 'AlboGrid',
                            itemId: 'AlboGrid',
                            emptyText: 'Nessuna pubblicazione trovata.',
                            enableColumnHide: false,
                            enableColumnMove: false,
                            sortableColumns: false,
                            store: 'AlboPublications',
                            columns: [
                                {
                                    xtype: 'actioncolumn',
                                    width: 80,
                                    align: 'center',
                                    stopSelection: false,
                                    items: [
                                        {
                                            getClass: function(v, metadata, r, rowIndex, colIndex, store) {
                                                var published = r.get('publication_date');

                                                if (published !== null) {
                                                    return 'icon-world';
                                                }
                                            },
                                            getTip: function(v, metadata, r, rowIndex, colIndex, store) {
                                                var published = r.get('publication_date');

                                                if (published !== null) {
                                                    return 'Pubblicata il: ' + Ext.util.Format.date(published, 'd/m/Y H:i');
                                                }
                                            }
                                        },
                                        {
                                            getClass: function(v, metadata, r, rowIndex, colIndex, store) {
                                                var canceled = r.get('cancelation_date');

                                                if (canceled !== null) {
                                                    return 'icon-delete';
                                                }
                                            },
                                            getTip: function(v, metadata, r, rowIndex, colIndex, store) {
                                                var canceled = r.get('cancelation_date');

                                                if (canceled !== null) {
                                                    return 'Annullata il: ' + Ext.util.Format.date(canceled, 'd/m/Y H:i');
                                                }
                                            }
                                        },
                                        {
                                            getClass: function(v, metadata, r, rowIndex, colIndex, store) {
                                                var extended = r.get('extension_date');

                                                if (extended !== null) {
                                                    return 'icon-arrow_ne';
                                                }
                                            },
                                            getTip: function(v, metadata, r, rowIndex, colIndex, store) {
                                                var extended = r.get('extension_date'),
                                                    extendedExpiration = r.get('extended_expiration_date');

                                                if (extended !== null) {
                                                    return 'Prorogata il: ' + Ext.util.Format.date(extended, 'd/m/Y H:i') + '  -  Nuova scadenza: ' + Ext.util.Format.date(extendedExpiration, 'd/m/Y H:i');
                                                }
                                            }
                                        },
                                        {
                                            getClass: function(v, metadata, r, rowIndex, colIndex, store) {
                                                var extended = r.get('expired');

                                                if (extended) {
                                                    return 'icon-clock_red';
                                                }
                                            },
                                            getTip: function(v, metadata, r, rowIndex, colIndex, store) {
                                                var extended = r.get('expired');

                                                if (extended) {
                                                    return 'Scaduta';
                                                }
                                            }
                                        }
                                    ]
                                },
                                {
                                    xtype: 'gridcolumn',
                                    width: 75,
                                    resizable: false,
                                    sortable: true,
                                    align: 'right',
                                    dataIndex: 'number',
                                    hideable: false,
                                    text: 'Numero'
                                },
                                {
                                    xtype: 'gridcolumn',
                                    resizable: false,
                                    dataIndex: 'title',
                                    hideable: false,
                                    text: 'Titolo',
                                    flex: 1
                                },
                                {
                                    xtype: 'datecolumn',
                                    width: 90,
                                    resizable: false,
                                    sortable: true,
                                    align: 'center',
                                    dataIndex: 'start_date',
                                    hideable: false,
                                    text: 'Inizio',
                                    format: 'd/m/Y'
                                },
                                {
                                    xtype: 'datecolumn',
                                    width: 90,
                                    resizable: false,
                                    sortable: true,
                                    align: 'center',
                                    dataIndex: 'expiration_date',
                                    hideable: false,
                                    text: 'Scadenza',
                                    format: 'd/m/Y'
                                },
                                {
                                    xtype: 'gridcolumn',
                                    width: 110,
                                    dataIndex: 'category_text',
                                    hideable: false,
                                    text: 'Categoria'
                                },
                                {
                                    xtype: 'gridcolumn',
                                    width: 110,
                                    dataIndex: 'area_text',
                                    hideable: false,
                                    text: 'Area'
                                },
                                {
                                    xtype: 'gridcolumn',
                                    width: 110,
                                    dataIndex: 'entity_text',
                                    hideable: false,
                                    text: 'Ente'
                                },
                                {
                                    xtype: 'actioncolumn',
                                    width: 40,
                                    align: 'center',
                                    stopSelection: false,
                                    items: [
                                        {
                                            handler: function(view, rowIndex, colIndex, item, e, record, row) {
                                                var msg = '';

                                                var omissis = record.get('omissis') ? 'Sì' : 'No',
                                                    internal = record.get('internal') ? 'Sì' : 'No',
                                                    number = record.get('number') > 0 ? record.get('number') : '-',
                                                    start = Ext.Date.format(record.get('start_date'), 'd/m/Y H:i'),
                                                    expiration = Ext.Date.format(record.get('expiration_date'), 'd/m/Y H:i'),
                                                    extended = Ext.Date.format(record.get('extended_expiration_date'), 'd/m/Y H:i'),
                                                    publication = Ext.Date.format(record.get('publication_date'), 'd/m/Y H:i');

                                                msg = '<div style="display: table">' +
                                                '<div style="display: table-row">' +
                                                '<div style="display: table-cell"><b>Numero:&nbsp;</b></div>' +
                                                '<div style="display: table-cell">' + number + '</div>' +
                                                '</div>' +
                                                '<div style="display: table-row">' +
                                                '<div style="display: table-cell"><b>Pubblicazione:&nbsp;</b></div>' +
                                                '<div style="display: table-cell">' + publication + '</div>' +
                                                '</div>' +
                                                '<div style="display: table-row">' +
                                                '<div style="display: table-cell"><b>Titolo:&nbsp;</b></div>' +
                                                '<div style="display: table-cell">' + record.get('title') + '</div>' +
                                                '</div>' +
                                                '<div style="display: table-row">' +
                                                '<div style="display: table-cell"><b>Descrizione:&nbsp;</b></div>' +
                                                '<div style="display: table-cell">' + record.get('description') + '</div>' +
                                                '</div>' +
                                                '<div style="display: table-row">' +
                                                '<div style="display: table-cell"><b>Inizio:&nbsp;</b></div>' +
                                                '<div style="display: table-cell">' + start + '</div>' +
                                                '</div>' +
                                                '<div style="display: table-row">' +
                                                '<div style="display: table-cell"><b>Scadenza:&nbsp;</b></div>' +
                                                '<div style="display: table-cell">' + expiration + '</div>' +
                                                '</div>' +
                                                '<div style="display: table-row">' +
                                                '<div style="display: table-cell"><b>Proroga scadenza:&nbsp;</b></div>' +
                                                '<div style="display: table-cell">' + extended + '</div>' +
                                                '</div>' +
                                                '<div style="display: table-row">' +
                                                '<div style="display: table-cell"><b>Area:&nbsp;</b></div>' +
                                                '<div style="display: table-cell">' + record.get('area_text') + '</div>' +
                                                '</div>' +
                                                '<div style="display: table-row">' +
                                                '<div style="display: table-cell"><b>Categoria:&nbsp;</b></div>' +
                                                '<div style="display: table-cell">' + record.get('category_text') + '</div>' +
                                                '</div>' +
                                                '<div style="display: table-row">' +
                                                '<div style="display: table-cell"><b>Ente:&nbsp;</b></div>' +
                                                '<div style="display: table-cell">' + record.get('entity_text') + '</div>' +
                                                '</div>' +
                                                '<div style="display: table-row">' +
                                                '<div style="display: table-cell"><b>Contiene omissis:&nbsp;</b></div>' +
                                                '<div style="display: table-cell">' + omissis + '</div>' +
                                                '</div>' +
                                                //    '<div style="display: table-row">' +
                                                //    '<div style="display: table-cell"><b>Interna:&nbsp;</b></div>' +
                                                //    '<div style="display: table-cell">' + internal + '</div>' +
                                                //    '</div>' +
                                                '</div>';

                                                Ext.Msg.alert('Dettagli Pubblicazione n.' + record.get('number'), msg);
                                            },
                                            iconCls: 'icon-information',
                                            tooltip: 'Dettagli'
                                        },
                                        {
                                            getClass: function(v, metadata, r, rowIndex, colIndex, store) {
                                                if (r.get('count_documents') > 0) {
                                                    return 'icon-attach';
                                                }
                                            },
                                            getTip: function(v, metadata, r, rowIndex, colIndex, store) {
                                                var documentsCnt = r.get('count_documents');

                                                if (documentsCnt > 0) {
                                                    var io = 'o';
                                                    if (documentsCnt > 1) {
                                                        io = 'i';
                                                    }
                                                    return documentsCnt + ' document' + io + ' allegat' + io;
                                                }
                                            },
                                            handler: function(view, rowIndex, colIndex, item, e, record, row) {
                                                var documentsCnt = record.get('count_documents');

                                                if (documentsCnt > 0) {
                                                    Ext.widget('AlboPublicationLinkedDocumentsWin').show();
                                                    Ext.getCmp('AlboPublicationLinkedDocumentsWin').setTitle('Documenti allegati alla pubblicazione n. ' + record.get('number') + ' - "' + record.get('title') + '"');
                                                    Ext.getStore('AlboLinkedDocuments').load({
                                                        params: {
                                                            publication: record.get('id')
                                                        }
                                                    });
                                                }
                                            }
                                        }
                                    ]
                                }
                            ],
                            viewConfig: {
                                getRowClass: function(record, rowIndex, rowParams, store) {
                                    if (record.get('cancelation_date') !== null) {
                                        return 'cancelled-albo-row';
                                    }
                                }
                            },
                            listeners: {
                                itemcontextmenu: {
                                    fn: me.onAlboGridItemContextMenu,
                                    scope: me
                                }
                            },
                            dockedItems: [
                                {
                                    xtype: 'toolbar',
                                    permissible: true,
                                    dock: 'top',
                                    id: 'AlboPublicationsToolbar',
                                    itemId: 'AlboPublicationsToolbar',
                                    items: [
                                        {
                                            xtype: 'button',
                                            handler: function(button, e) {
                                                var storeDocs = Ext.getStore('AlboLinkedDocumentsForm');

                                                Ext.widget('AlboPublicationEditWin').show();

                                                storeDocs.removeAll();
                                            },
                                            id: 'AlboPublicationNewBtn',
                                            itemId: 'AlboPublicationNewBtn',
                                            iconCls: 'icon-add',
                                            text: 'Nuova Pubblicazione'
                                        }
                                    ]
                                },
                                {
                                    xtype: 'pagingtoolbar',
                                    dock: 'bottom',
                                    displayInfo: true,
                                    displayMsg: 'Pubblicazioni {0} - {1} di {2}',
                                    emptyMsg: 'Nessuna pubblicazione',
                                    store: 'AlboPublications'
                                }
                            ]
                        },
                        {
                            xtype: 'menu',
                            permissible: true,
                            flex: 1,
                            hidden: true,
                            id: 'AlboPublicationEditMn',
                            itemId: 'AlboPublicationEditMn',
                            items: [
                                {
                                    xtype: 'menuitem',
                                    handler: function(item, e) {
                                        var record = Ext.getCmp('AlboGrid').getSelectionModel().getSelection()[0],
                                            storeDocs = Ext.getStore('AlboLinkedDocumentsForm');

                                        Ext.widget('AlboPublicationEditWin').show();
                                        Ext.getCmp('AlboPublicationEditForm').loadRecord(record);

                                        if(record.get('locked_docs') === true){
                                            Ext.getCmp('AlboPublicationEditLinkedDocumentsGrid').disable();
                                        }

                                        storeDocs.removeAll();

                                        storeDocs.load({
                                            params: {
                                                publication: record.get('id')
                                            }
                                        });
                                    },
                                    id: 'contextAlboPublicationEdit',
                                    itemId: 'contextAlboPublicationEdit',
                                    iconCls: 'icon-pencil',
                                    text: 'Modifica'
                                },
                                {
                                    xtype: 'menuitem',
                                    handler: function(item, e) {
                                        var record = Ext.getCmp('AlboGrid').getSelectionModel().getSelection()[0];

                                        Ext.Msg.show({
                                            title: 'Pubblicazione n. ' + record.get('number'),
                                            msg: 'Sei sicuro di voler eliminare questa Pubblicazione?',
                                            buttons: Ext.Msg.YESNO,
                                            fn: function(r){
                                                if (r == 'yes') {
                                                    store = Ext.getStore('AlboPublications');
                                                    store.remove(record);
                                                    store.sync({
                                                        callback: function () {
                                                            store.load();
                                                        },
                                                        success: function() {
                                                            Ext.Msg.alert('Successo', 'Pubblicazione eliminata');
                                                        },
                                                        failure: function() {
                                                            Ext.Msg.alert('Attenzione', 'Pubblicazione NON eliminata');
                                                        }
                                                    });
                                                }
                                            }
                                        });
                                    },
                                    id: 'contextAlboPublicationDelete',
                                    itemId: 'contextAlboPublicationDelete',
                                    iconCls: 'icon-cancel',
                                    text: 'Elimina'
                                },
                                {
                                    xtype: 'menuseparator'
                                },
                                {
                                    xtype: 'menuitem',
                                    handler: function(item, e) {
                                        var grid = Ext.getCmp('AlboGrid'),
                                            store = Ext.getStore('AlboPublications'),
                                            record = grid.getSelectionModel().getSelection()[0];

                                        Ext.Msg.show({
                                            title: 'Pubblicazione n. ' + record.get('number'),
                                            msg: 'Sei sicuro di voler pubblicare questa Pubblicazione?',
                                            buttons: Ext.Msg.YESNO,
                                            fn: function(r){
                                                if (r == 'yes') {
                                                    store = Ext.getStore('AlboPublications');
                                                    record.set({publication_date: new Date()});
                                                    store.sync({
                                                        callback: function () {
                                                            store.load();
                                                        },
                                                        success: function() {
                                                            grid.getSelectionModel().deselectAll();
                                                            Ext.Msg.alert('Successo', 'Pubblicazione pubblicata');
                                                        },
                                                        failure: function() {
                                                            Ext.Msg.alert('Attenzione', 'Pubblicazione NON pubblicata');
                                                        }
                                                    });
                                                }
                                            }
                                        });
                                    },
                                    id: 'contextAlboPublicationPublish',
                                    itemId: 'contextAlboPublicationPublish',
                                    iconCls: 'icon-world',
                                    text: 'Pubblica'
                                },
                                {
                                    xtype: 'menuitem',
                                    handler: function(item, e) {
                                        var grid = Ext.getCmp('AlboGrid'),
                                            store = Ext.getStore('AlboPublications'),
                                            record = grid.getSelectionModel().getSelection()[0];

                                        Ext.Msg.show({
                                            title: 'Pubblicazione n. ' + record.get('number'),
                                            msg: 'Sei sicuro di voler annullare questa Pubblicazione?',
                                            buttons: Ext.Msg.YESNO,
                                            fn: function(r){
                                                if (r == 'yes') {
                                                    store = Ext.getStore('AlboPublications');
                                                    record.set({cancelation_date: new Date()});
                                                    store.sync({
                                                        callback: function () {
                                                            store.load();
                                                        },
                                                        success: function() {
                                                            grid.getSelectionModel().deselectAll();
                                                            Ext.Msg.alert('Successo', 'Pubblicazione annullata');
                                                        },
                                                        failure: function() {
                                                            Ext.Msg.alert('Attenzione', 'Pubblicazione NON annullata');
                                                        }
                                                    });
                                                }
                                            }
                                        });
                                    },
                                    id: 'contextAlboPublicationCancel',
                                    itemId: 'contextAlboPublicationCancel',
                                    iconCls: 'icon-delete',
                                    text: 'Annulla'
                                },
                                {
                                    xtype: 'menuitem',
                                    handler: function(item, e) {
                                        var record = Ext.getCmp('AlboGrid').getSelectionModel().getSelection()[0];

                                        Ext.widget('AlboPublicationExtensionWin').show();
                                        Ext.getCmp('AlboPublicationExtensionWin').setTitle('Proroga pubblicazione n.' + record.get('number'));
                                        Ext.getCmp('AlboPublicationExtensionForm').loadRecord(record);
                                    },
                                    id: 'contextAlboPublicationExtend',
                                    itemId: 'contextAlboPublicationExtend',
                                    iconCls: 'icon-arrow_ne',
                                    text: 'Proroga'
                                },
                                {
                                    xtype: 'menuseparator'
                                },
                                {
                                    xtype: 'menuitem',
                                    handler: function(item, e) {
                                        var record = Ext.getCmp('AlboGrid').getSelectionModel().getSelection()[0];

                                        Ext.widget('AlboPublicationHistoryWin').show();
                                        Ext.getCmp('AlboPublicationHistoryWin').setTitle('Storico publicazione n. ' + record.get('number'));

                                        Ext.getStore('AlboHistories').load({
                                            params: {
                                                publication: record.get('id')
                                            }
                                        });
                                    },
                                    id: 'contextAlboPublicationHistory',
                                    itemId: 'contextAlboPublicationHistory',
                                    iconCls: 'icon-hourglass',
                                    text: 'Storico modifiche'
                                },
                                {
                                    xtype: 'menuseparator'
                                },
                                {
                                    xtype: 'menuitem',
                                    id: 'contextAlboPublicationPrints',
                                    itemId: 'contextAlboPublicationPrints',
                                    iconCls: 'icon-printer',
                                    text: 'Stampe singole',
                                    menu: {
                                        xtype: 'menu',
                                        items: [
                                            {
                                                xtype: 'menuitem',
                                                handler: function(item, e) {
                                                    var rec = {},
                                                        publication = Ext.getCmp('AlboGrid').getSelectionModel().getSelection()[0];

                                                    rec.newSpool = 1;
                                                    rec.print = 'Publication';
                                                    rec.namespace = 'Albo';
                                                    rec.type = 'PDF';
                                                    rec.mime = 'application/pdf';
                                                    rec.publication = publication.get('id');
                                                    rec.number = publication.get('number');

                                                    Ext.Ajax.request({
                                                        url: '/mc2-api/core/print',
                                                        params: rec,
                                                        success: function(response, opts) {
                                                            var res = Ext.decode(response.responseText);
                                                            mc2ui.app.showNotifyPrint(res);
                                                        }
                                                    });
                                                },
                                                iconCls: 'icon-page_white_acrobat',
                                                text: 'Pubblicazione'
                                            }
                                        ]
                                    }
                                }
                            ]
                        }
                    ]
                }
            ],
            dockedItems: [
                {
                    xtype: 'toolbar',
                    permissible: true,
                    dock: 'top',
                    id: 'AlboToolbar',
                    itemId: 'AlboToolbar',
                    items: [
                        {
                            xtype: 'button',
                            handler: function(button, e) {
                                Ext.widget('AlboAreasWin').show();
                            },
                            id: 'AlboAreasBtn',
                            itemId: 'AlboAreasBtn',
                            iconCls: 'icon-chart_org_inverted',
                            text: 'Aree'
                        },
                        {
                            xtype: 'button',
                            handler: function(button, e) {
                                Ext.widget('AlboCategoriesWin').show();
                            },
                            id: 'AlboCategoriesBtn',
                            itemId: 'AlboCategoriesBtn',
                            iconCls: 'icon-rgb',
                            text: 'Categorie'
                        },
                        {
                            xtype: 'button',
                            handler: function(button, e) {
                                Ext.widget('AlboEntitiesWin').show();
                            },
                            id: 'AlboEntitiesBtn',
                            itemId: 'AlboEntitiesBtn',
                            iconCls: 'icon-building',
                            text: 'Enti'
                        },
                        {
                            xtype: 'tbseparator'
                        },
                        {
                            xtype: 'button',
                            handler: function(button, e) {
                                Ext.Ajax.request({
                                    url: '/mc2-api/albo/albo-generate-html',
                                    success: function(response, opts) {
                                        Ext.Msg.alert('Successo', 'Aggiornamento pagina web pubblica di Albo Pretorio avvenuto.');
                                    },
                                    failure: function(response, opts) {
                                        Ext.Msg.alert('Attenzione', 'Aggiornamento pagina web pubblica di Albo Pretorio fallito.');
                                    }
                                });
                            },
                            id: 'AlboUpdateWebBtn',
                            itemId: 'AlboUpdateWebBtn',
                            iconCls: 'icon-world',
                            text: 'Aggiorna sito web'
                        },
                        {
                            xtype: 'tbseparator'
                        },
                        {
                            xtype: 'button',
                            id: 'AlboPrintsBtn',
                            itemId: 'AlboPrintsBtn',
                            text: 'Stampe',
                            menu: {
                                xtype: 'menu',
                                items: [
                                    {
                                        xtype: 'menuitem',
                                        handler: function(item, e) {
                                            var rec = {};

                                            rec.newSpool = 1;
                                            rec.print = 'Current';
                                            rec.namespace = 'Albo';
                                            rec.type = 'PDF';
                                            rec.mime = 'application/pdf';

                                            Ext.Ajax.request({
                                                url: '/mc2-api/core/print',
                                                params: rec,
                                                success: function(response, opts) {
                                                    var res = Ext.decode(response.responseText);
                                                    mc2ui.app.showNotifyPrint(res);
                                                }
                                            });
                                        },
                                        iconCls: 'icon-clock',
                                        text: 'Riepilogo Pubblicazioni correnti'
                                    },
                                    {
                                        xtype: 'menuitem',
                                        handler: function(item, e) {
                                            var rec = {};

                                            rec.newSpool = 1;
                                            rec.print = 'Filtered';
                                            rec.namespace = 'Albo';
                                            rec.type = 'PDF';
                                            rec.mime = 'application/pdf';
                                            rec.filter = Ext.JSON.encode(Ext.getCmp('AlboFilterForm').getForm().getValues());

                                            rec.sort = [];
                                            Ext.each(Ext.getCmp('AlboGrid').getStore().getSorters(), function(sorter){
                                                rec.sort = rec.sort.concat({
                                                    "property": sorter.property,
                                                    "direction": sorter.direction
                                                });
                                            });
                                            rec.sort = Ext.encode(rec.sort);

                                            Ext.Ajax.request({
                                                url: '/mc2-api/core/print',
                                                params: rec,
                                                success: function(response, opts) {
                                                    var res = Ext.decode(response.responseText);
                                                    mc2ui.app.showNotifyPrint(res);
                                                }
                                            });
                                        },
                                        iconCls: 'icon-find',
                                        text: 'Riepilogo Pubblicazioni filtrate'
                                    }
                                ]
                            }
                        },
                        {
                            xtype: 'tbspacer',
                            flex: 1
                        }
                    ]
                }
            ],
            listeners: {
                boxready: {
                    fn: me.onAlboPnlBoxReady,
                    scope: me
                }
            }
        });

        me.callParent(arguments);
    },

    onAlboFilterPublicationChange: function(field, newValue, oldValue, eOpts) {
        Ext.getCmp('AlboFilterForm').applyFilter();
    },

    onAlboFilterPublishedStartChange: function(field, newValue, oldValue, eOpts) {
        Ext.getCmp('AlboFilterForm').applyFilter();
    },

    onAlboFilterPublishedEndChange: function(field, newValue, oldValue, eOpts) {
        Ext.getCmp('AlboFilterForm').applyFilter();
    },

    onAlboFilterStartStartChange: function(field, newValue, oldValue, eOpts) {
        Ext.getCmp('AlboFilterForm').applyFilter();
    },

    onAlboFilterStartEndChange: function(field, newValue, oldValue, eOpts) {
        Ext.getCmp('AlboFilterForm').applyFilter();
    },

    onAlboFilterEndStartChange: function(field, newValue, oldValue, eOpts) {
        Ext.getCmp('AlboFilterForm').applyFilter();
    },

    onAlboFilterEndEndChange: function(field, newValue, oldValue, eOpts) {
        Ext.getCmp('AlboFilterForm').applyFilter();
    },

    onAlboFilterStatusChange: function(field, newValue, oldValue, eOpts) {
        Ext.getCmp('AlboFilterForm').applyFilter();
    },

    onAlboFilterExpirationChange: function(field, newValue, oldValue, eOpts) {
        Ext.getCmp('AlboFilterForm').applyFilter();
    },

    onAlboFilterAreaChange: function(field, newValue, oldValue, eOpts) {
        Ext.getCmp('AlboFilterForm').applyFilter();
    },

    onAlboFilterCategoryChange: function(field, newValue, oldValue, eOpts) {
        Ext.getCmp('AlboFilterForm').applyFilter();
    },

    onAlboFilterEntityChange: function(field, newValue, oldValue, eOpts) {
        Ext.getCmp('AlboFilterForm').applyFilter();
    },

    onAlboFilterTextChange: function(field, newValue, oldValue, eOpts) {
        Ext.getCmp('AlboFilterForm').applyFilter();
    },

    onAlboFilterDescriptionChange: function(field, newValue, oldValue, eOpts) {
        Ext.getCmp('AlboFilterForm').applyFilter();
    },

    onAlboFilterDocumentChange: function(field, newValue, oldValue, eOpts) {
        Ext.getCmp('AlboFilterForm').applyFilter();
    },

    onAlboGridItemContextMenu: function(dataview, record, item, index, e, eOpts) {
        e.stopEvent();
        var newX = e.xy[0];
        var newY = e.xy[1];
        Ext.getCmp('AlboPublicationEditMn').showAt([newX,newY]);

        var edit = Ext.getCmp('contextAlboPublicationEdit'),
            publish = Ext.getCmp('contextAlboPublicationPublish'),
            cancel = Ext.getCmp('contextAlboPublicationCancel'),
            extend = Ext.getCmp('contextAlboPublicationExtend');

        if (record.get('publication_date') === null) {
            edit.setDisabled(false);
            publish.setDisabled(false);
            cancel.setDisabled(true);
            extend.setDisabled(true);
        } else {
            edit.setDisabled(true);
            publish.setDisabled(true);
            if (record.get('cancelation_date') === null) {
                cancel.setDisabled(false);
                if (record.get('extension_date') === null) {
                    extend.setDisabled(false);
                } else {
                    extend.setDisabled(true);
                }
            } else {
                cancel.setDisabled(true);
                extend.setDisabled(true);
            }
        }
    },

    onAlboPnlBoxReady: function(component, width, height, eOpts) {
        Ext.getCmp('AlboFilterPublication').select('A');
        Ext.getCmp('AlboFilterStatus').select('A');
        Ext.getCmp('AlboFilterExpiration').select('A');

        Ext.getStore('AlboAreas').load();
        Ext.getStore('AlboCategories').load();
        Ext.getStore('AlboEntities').load();

        Ext.getStore('AlboAreasFilter').load({
            callback: function(records, operation, success) {
                if (success) {
                    Ext.getCmp('AlboFilterArea').select(0);
                }
            }
        });

        Ext.getStore('AlboCategoriesFilter').load({
            callback: function(records, operation, success) {
                if (success) {
                    Ext.getCmp('AlboFilterCategory').select(0);
                }
            }
        });

        Ext.getStore('AlboEntitiesFilter').load({
            callback: function(records, operation, success) {
                if (success) {
                    Ext.getCmp('AlboFilterEntity').select(0);
                }
            }
        });
    }

});