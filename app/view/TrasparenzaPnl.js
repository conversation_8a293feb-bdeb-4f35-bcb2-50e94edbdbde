/*
 * File: app/view/TrasparenzaPnl.js
 *
 * This file was generated by Sencha Architect version 3.2.0.
 * http://www.sencha.com/products/architect/
 *
 * This file requires use of the Ext JS 4.2.x library, under independent license.
 * License of Sencha Architect does not include license for Ext JS 4.2.x. For more
 * details see http://www.sencha.com/license <NAME_EMAIL>.
 *
 * This file will be auto-generated each and everytime you save your project.
 *
 * Do NOT hand edit this file.
 */

Ext.define('mc2ui.view.TrasparenzaPnl', {
    extend: 'Ext.panel.Panel',
    alias: 'widget.TrasparenzaPnl',

    requires: [
        'Ext.tree.Panel',
        'Ext.tree.View',
        'Ext.grid.column.Action',
        'Ext.tree.Column',
        'Ext.grid.column.Date',
        'Ext.menu.Menu',
        'Ext.menu.Separator',
        'Ext.toolbar.Toolbar',
        'Ext.button.Button',
        'Ext.toolbar.Separator',
        'Ext.toolbar.Spacer'
    ],

    border: false,
    hidden: true,
    id: 'TrasparenzaPnl',
    itemId: 'TrasparenzaPnl',
    layout: 'border',
    header: false,
    title: 'Amministrazione Trasparente',
    titleAlign: 'center',

    initComponent: function() {
        var me = this;

        Ext.applyIf(me, {
            items: [
                {
                    xtype: 'panel',
                    region: 'center',
                    split: true,
                    id: 'TrasparenzaMainPnl',
                    itemId: 'TrasparenzaMainPnl',
                    layout: {
                        type: 'vbox',
                        align: 'stretch'
                    },
                    items: [
                        {
                            xtype: 'treepanel',
                            flex: 1,
                            border: false,
                            id: 'TrasparenzaGrid',
                            itemId: 'TrasparenzaGrid',
                            enableColumnHide: false,
                            enableColumnMove: false,
                            enableColumnResize: false,
                            sortableColumns: false,
                            store: 'TrasparenzaVoicesTree',
                            rootVisible: false,
                            viewConfig: {

                            },
                            listeners: {
                                itemcontextmenu: {
                                    fn: me.onTrasparenzaGridItemContextMenu,
                                    scope: me
                                }
                            },
                            columns: [
                                {
                                    xtype: 'actioncolumn',
                                    width: 20,
                                    resizable: false,
                                    align: 'center',
                                    stopSelection: false,
                                    items: [
                                        {
                                            getClass: function(v, metadata, r, rowIndex, colIndex, store) {
                                                if (r.get('published') === true) {
                                                    return 'icon-world';
                                                }
                                            },
                                            getTip: function(v, metadata, r, rowIndex, colIndex, store) {
                                                if (r.get('published') === true) {
                                                    return 'Pubblicata';
                                                }
                                            }
                                        }
                                    ]
                                },
                                {
                                    xtype: 'treecolumn',
                                    resizable: false,
                                    dataIndex: 'title',
                                    text: 'Titolo',
                                    flex: 1
                                },
                                {
                                    xtype: 'datecolumn',
                                    width: 130,
                                    resizable: false,
                                    enableColumnHide: false,
                                    align: 'center',
                                    dataIndex: 'last_update',
                                    hideable: false,
                                    text: 'Ultimo Aggiornamento',
                                    format: 'd/m/Y'
                                },
                                {
                                    xtype: 'actioncolumn',
                                    width: 40,
                                    resizable: false,
                                    align: 'center',
                                    stopSelection: false,
                                    items: [
                                        {
                                            getClass: function(v, metadata, r, rowIndex, colIndex, store) {
                                                if (r.get('count_documents') > 0) {
                                                    return 'icon-attach';
                                                }
                                            },
                                            getTip: function(v, metadata, r, rowIndex, colIndex, store) {
                                                var documents = r.get('count_documents');

                                                if (documents > 0) {
                                                    var io = 'o';
                                                    if (documents > 1) {
                                                        io = 'i';
                                                    }
                                                    return documents + ' document' + io + ' allegat' + io;
                                                }
                                            },
                                            handler: function(view, rowIndex, colIndex, item, e, record, row) {
                                                var documentsCnt = record.get('count_documents');

                                                if (documentsCnt > 0) {
                                                    Ext.widget('TrasparenzaLinkedDocumentsWin').show();
                                                    Ext.getCmp('TrasparenzaLinkedDocumentsWin').setTitle('Documenti allegati alla voce "' + record.get('title') + '"');
                                                    Ext.getStore('TrasparenzaLinkedDocuments').load({
                                                        params: {
                                                            voice: record.get('id')
                                                        }
                                                    });
                                                }
                                            }
                                        },
                                        {
                                            getClass: function(v, metadata, r, rowIndex, colIndex, store) {
                                                if (r.get('reference') !== '' || r.get('content') !== '') {
                                                    return 'icon-information';
                                                }
                                            },
                                            getTip: function(v, metadata, r, rowIndex, colIndex, store) {
                                                if (r.get('reference') !== '' || r.get('content') !== '') {
                                                    return 'Dettagli';
                                                }
                                            },
                                            handler: function(view, rowIndex, colIndex, item, e, record, row) {
                                                if (record.get('reference') !== '' || record.get('content') !== '') {
                                                    Ext.Msg.alert('Dettagli voce "' + record.get('title') + '"', '<h5 style="margin-bottom: 3px">Contenuto:</h5><div>' + record.get('content') + '</div><h5 style="margin-bottom: 3px">Riferimenti normativi:</h5><div>' + record.get('reference') + '</div>');
                                                }
                                            }
                                        }
                                    ]
                                }
                            ]
                        },
                        {
                            xtype: 'menu',
                            permissible: true,
                            flex: 1,
                            hidden: true,
                            id: 'TrasparenzaVoiceEditMn',
                            itemId: 'TrasparenzaVoiceEditMn',
                            items: [
                                {
                                    xtype: 'menuitem',
                                    handler: function(item, e) {
                                        var record = Ext.getCmp('TrasparenzaGrid').getSelectionModel().getSelection()[0],
                                            storeDocs = Ext.getStore('TrasparenzaLinkedDocumentsForm');

                                        Ext.widget('TrasparenzaVoiceEditWin').show();
                                        Ext.getCmp('TrasparenzaVoiceEditForm').loadRecord(record);

                                        storeDocs.removeAll();

                                        storeDocs.load({
                                            params: {
                                                voice: record.get('id')
                                            }
                                        });
                                    },
                                    id: 'contextTrasparenzaVoiceEdit',
                                    itemId: 'contextTrasparenzaVoiceEdit',
                                    iconCls: 'icon-pencil',
                                    text: 'Modifica'
                                },
                                {
                                    xtype: 'menuseparator'
                                },
                                {
                                    xtype: 'menuitem',
                                    handler: function(item, e) {
                                        var store = Ext.getStore('TrasparenzaVoicesTree'),
                                            record = Ext.getCmp('TrasparenzaGrid').getSelectionModel().getSelection()[0];

                                        record.set('published', !record.get('published'));

                                        store.sync({
                                            callback: function() {
                                                store.load();
                                            },
                                            success: function(form, action) {
                                                Ext.getCmp('TrasparenzaGrid').getSelectionModel().deselectAll();
                                                Ext.Msg.alert('Successo', 'Voce Trasparenza salvata');
                                            },
                                            failure: function(form, action) {
                                                Ext.Msg.alert('Attenzione', 'Voce Trasparenza NON salvata');
                                            }
                                        });
                                    },
                                    id: 'contextTrasparenzaVoiceToggle',
                                    itemId: 'contextTrasparenzaVoiceToggle',
                                    iconCls: 'icon-world'
                                },
                                {
                                    xtype: 'menuseparator'
                                },
                                {
                                    xtype: 'menuitem',
                                    id: 'contextTrasparenzaVoicePrints',
                                    itemId: 'contextTrasparenzaVoicePrints',
                                    iconCls: 'icon-printer',
                                    text: 'Stampe singole',
                                    menu: {
                                        xtype: 'menu',
                                        width: 120,
                                        items: [
                                            {
                                                xtype: 'menuitem',
                                                handler: function(item, e) {
                                                    var rec = {},
                                                        voice = Ext.getCmp('TrasparenzaGrid').getSelectionModel().getSelection()[0];

                                                    rec.newSpool = 1;
                                                    rec.print = 'Voice';
                                                    rec.namespace = 'Trasparenza';
                                                    rec.type = 'PDF';
                                                    rec.mime = 'application/pdf';
                                                    rec.voice = voice.get('id');
                                                    rec.title = voice.get('title');

                                                    Ext.Ajax.request({
                                                        url: '/mc2-api/core/print',
                                                        params: rec,
                                                        success: function(response, opts) {
                                                            var res = Ext.decode(response.responseText);
                                                            mc2ui.app.showNotifyPrint(res);
                                                        }
                                                    });
                                                },
                                                iconCls: 'icon-page_white_acrobat',
                                                text: 'Voce'
                                            }
                                        ]
                                    }
                                }
                            ]
                        }
                    ]
                }
            ],
            dockedItems: [
                {
                    xtype: 'toolbar',
                    permissible: true,
                    dock: 'top',
                    id: 'TrasparenzaToolbar',
                    itemId: 'TrasparenzaToolbar',
                    items: [
                        {
                            xtype: 'button',
                            handler: function(button, e) {
                                Ext.Ajax.request({
                                    url: '/mc2-api/trasparenza/trasparenza-generate-html',
                                    success: function(response, opts) {
                                        Ext.Msg.alert('Successo', 'Aggiornamento pagina web pubblica per la Trasparenza Amministrativa avvenuto.');
                                    },
                                    failure: function(response, opts) {
                                        Ext.Msg.alert('Attenzione', 'Aggiornamento pagina web pubblica per la Trasparenza Amministrativa fallito.');
                                    }
                                });
                            },
                            id: 'TrasparenzaUpdateWebBtn',
                            itemId: 'TrasparenzaUpdateWebBtn',
                            iconCls: 'icon-world',
                            text: 'Aggiorna sito web'
                        },
                        {
                            xtype: 'tbseparator'
                        },
                        {
                            xtype: 'button',
                            id: 'TrasparenzaPrintsBtn',
                            itemId: 'TrasparenzaPrintsBtn',
                            iconCls: 'icon-printer',
                            text: 'Stampe',
                            menu: {
                                xtype: 'menu',
                                items: [
                                    {
                                        xtype: 'menuitem',
                                        handler: function(item, e) {
                                            var rec = {};

                                            rec.newSpool = 1;
                                            rec.print = 'Voices';
                                            rec.namespace = 'Trasparenza';
                                            rec.type = 'PDF';
                                            rec.mime = 'application/pdf';

                                            Ext.Ajax.request({
                                                url: '/mc2-api/core/print',
                                                params: rec,
                                                success: function(response, opts) {
                                                    var res = Ext.decode(response.responseText);
                                                    mc2ui.app.showNotifyPrint(res);
                                                }
                                            });
                                        },
                                        iconCls: 'icon-page_white_acrobat',
                                        text: 'Riepilogo voci pubblicate'
                                    }
                                ]
                            }
                        },
                        {
                            xtype: 'tbspacer',
                            flex: 1
                        },

                    ]
                }
            ]
        });

        me.callParent(arguments);
    },

    onTrasparenzaGridItemContextMenu: function(dataview, record, item, index, e, eOpts) {
        e.stopEvent();
        var newX = e.xy[0];
        var newY = e.xy[1];
        Ext.getCmp('TrasparenzaVoiceEditMn').showAt([newX,newY]);

        if (record.get('published')) {
            Ext.getCmp('contextTrasparenzaVoiceToggle').setText('Ritira');
        } else {
            Ext.getCmp('contextTrasparenzaVoiceToggle').setText('Pubblica');
        }
    }

});