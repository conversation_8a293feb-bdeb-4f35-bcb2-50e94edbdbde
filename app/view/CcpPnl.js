/*
 * File: app/view/CcpPnl.js
 *
 * This file was generated by Sencha Architect version 3.2.0.
 * http://www.sencha.com/products/architect/
 *
 * This file requires use of the Ext JS 4.2.x library, under independent license.
 * License of Sencha Architect does not include license for Ext JS 4.2.x. For more
 * details see http://www.sencha.com/license <NAME_EMAIL>.
 *
 * This file will be auto-generated each and everytime you save your project.
 *
 * Do NOT hand edit this file.
 */

Ext.define('mc2ui.view.CcpPnl', {
    extend: 'Ext.panel.Panel',
    alias: 'widget.CcpPnl',

    requires: [
        'mc2ui.view.MyPanel9',
        'mc2ui.view.MyPanel3',
        'mc2ui.view.MyPanel29',
        'mc2ui.view.MyPanel27',
        'Ext.tab.Panel',
        'Ext.tab.Tab',
        'Ext.form.Panel',
        'Ext.form.FieldSet',
        'Ext.form.field.Number',
        'Ext.form.field.Date',
        'Ext.form.field.Checkbox',
        'Ext.toolbar.Spacer',
        'Ext.grid.Panel',
        'Ext.grid.column.Action',
        'Ext.grid.column.Date',
        'Ext.grid.column.Number',
        'Ext.grid.View',
        'Ext.selection.CheckboxModel',
        'Ext.toolbar.Paging',
        'Ext.menu.Menu',
        'Ext.form.field.ComboBox',
        'Ext.menu.Separator',
        'Ext.tree.Panel',
        'Ext.tree.View',
        'Ext.tree.Column',
        'Ext.container.ButtonGroup',
        'Ext.form.Label',
        'Ext.toolbar.Separator'
    ],

    statics: {
        openEditMovementWin: function(id, parent_component) {
            Ext.widget('CcpMovementEditWin').show();

            if (parent_component) {
                Ext.getCmp('CcpMovementEditWin').section = parent_component;
            }

            if(!id) {
                Ext.getCmp('CcpMovementCreationDate').setValue(new Date());
            } else {
                var movement;
                Ext.Ajax.request({
                    async: false,
                    url: '/mc2-api/ccp/movement/' + id,
                    success: function(response) {
                        var res = Ext.decode(response.responseText);
                        if(res.success=== true) {
                            movement = Ext.create('mc2ui.model.CcpMovement', res.results);
                        } else {
                            Ext.msg.alert('ERRORE', res.message);
                        }
                    },
                    failure: function () {
                        Ext.msg.alert('ERRORE', 'Errore generico, contattare l\'assistenza');
                    }

                });


                var r = movement,
                    s = Ext.getStore('CcpLinkedAdditionalsForm'),
                    p = null;

                s.removeAll();
                s.load({
                    params: {
                        type: 'M',
                        item: r.get('id')
                    },
                    callback: function(records, operation, success) {
                        if (success) {


                            Ext.getStore('CcpTypes').load({
                                params: {school_year: r.get('subject_school_year')},
                                callback: function() {
                                    Ext.getCmp('CcpMovementTypeId').setValue(r.get('type_id'));

                                }
                            });


                            Ext.getCmp('CcpEditMovDiscountPnl').show();
                            //Ext.getCmp('CcpMovementEditWin').setHeight(550);
                            Ext.getCmp('CcpMovementEditForm').getForm().loadRecord(r);
                            Ext.getCmp('CcpEditMovementSubjectYearCmb').setValue(r.get('subject_school_year'));





                            Ext.getCmp('CcpMovementSubjectSchoolAddress').setValue(r.get('subject_school_address'));
                            Ext.getCmp('CcpMovementSubjectSchoolAddressCode').setValue(r.get('subject_school_address_code'));

                            Ext.getCmp('CcpSaveGoPayMovementIdBtn').hide();
                            Ext.getCmp('CcpSaveAndPayBtn').hide();


                            if (r.get('subject_type') === 'E') {
                                p = 'E_' + r.get('subject_id');
                            } else if (r.get('subject_type') === 'S') {
                                p = 'S_' + r.get('subject_id') + '_' + r.get('subject_seat');
                            } else if (r.get('subject_type') === 'O') {
                                p = r.get('subject_data');
                            }

                            if (p) {
                                if (r.get('subject_type') === 'O') {
                                    Ext.getCmp('CcpMovementSubjectOther').setValue(p);
                                } else {
                                    Ext.getCmp('CcpMovementSubject').select(p);
                                    Ext.getStore('CcpAddMovementStudents').removeAll();
                                    Ext.getStore('CcpAddMovementStudents').add(r.data);
                                }
                            }

                            if(r.get('subject_type') === 'O'){
                                Ext.getCmp('CcpMovementSubject').setDisabled(true);
                            } else {
                                Ext.getCmp('CcpMovementSubject').setDisabled(true);
                                Ext.getCmp('CcpMovementSubjectOther').setDisabled(true);
                                Ext.getCmp('CcpAddMovementStudentSelect').setDisabled(true);
                            }



                            Ext.getCmp('CcpMovementEditWin').validateFields();
                            if (r.get('school_year') !== null) {
                                Ext.getCmp('CcpMovementEditWin').filterTypes('school_year', r.get('school_year'), false);
                                setTimeout(function(){Ext.getStore('CcpTypes').clearFilter();}, 300);
                            }
                            Ext.getCmp('CcpRateTotalCnt').enable();
                            Ext.getCmp('CcpRateGrid').enable();
                            Ext.getStore('CcpTypeSteps').add({
                                expiration_date: r.get('expiration_date'),
                                description: r.get('description'),
                                da_ratei: r.get('da_ratei'),
                                a_ratei: r.get('a_ratei'),
                                vat: r.get('vat'),
                                gross: r.get('gross'),
                                value: r.get('amount'),
                                conto_risconti: r.get('conto_risconti'),
                                conto_crediti: r.get('conto_crediti'),
                                conto_ricavi: r.get('conto_ricavi')
                            });
                            Ext.getCmp('CcpRateGrid').getSelectionModel().selectAll();
                            Ext.getCmp('CcpMovementTotal').setValue(r.get('amount'));
                            //Ext.getCmp('CcpExpiratonDateField').show();
                            Ext.getCmp('CcpMovementEditWin').setTitle('Movimento - ' + r.get('subject_data'));
                            Ext.getCmp('CcpAddStepMovementBtn').hide();
                            Ext.getCmp('CcpMovementCopyStudents').hide();
                            //Ext.getCmp('CcpTabRateizzazione').setText('Movimento');
                        } else {


                            Ext.Msg.alert('Attenzione ', 'Caricamento addizionali abbinate al movimento fallita.');
                        }

                    }

                });




            }


        }
    },

    border: false,
    hidden: true,
    id: 'CcpPnl',
    itemId: 'CcpPnl',
    layout: 'border',
    header: false,
    title: 'Conto corrente postale',
    titleAlign: 'center',

    initComponent: function() {
        var me = this;

        Ext.applyIf(me, {
            items: [
                {
                    xtype: 'tabpanel',
                    region: 'center',
                    split: true,
                    splitterResize: false,
                    id: 'CcpMainPnl',
                    activeTab: 0,
                    items: [
                        {
                            xtype: 'mypanel9',
                            iconCls: 'icon-group'
                        },
                        {
                            xtype: 'mypanel3'
                        },
                        {
                            xtype: 'panel',
                            id: 'CcpInvoicePanel',
                            layout: 'fit',
                            title: 'Fatture',
                            tabConfig: {
                                xtype: 'tab',
                                iconCls: 'icon-folder_page',
                                listeners: {
                                    render: {
                                        fn: me.onTabRender,
                                        scope: me
                                    }
                                }
                            },
                            items: [
                                {
                                    xtype: 'tabpanel',
                                    activeTab: 0,
                                    items: [
                                        {
                                            xtype: 'panel',
                                            layout: 'border',
                                            title: 'Uscita',
                                            items: [
                                                {
                                                    xtype: 'panel',
                                                    region: 'west',
                                                    split: true,
                                                    width: 240,
                                                    layout: 'fit',
                                                    title: 'Filtri',
                                                    items: [
                                                        {
                                                            xtype: 'form',
                                                            getFilters: function() {
                                                                var data = Ext.getCmp('CcpInvoiceFilterFrm').getValues();

                                                                return data;
                                                            },
                                                            id: 'CcpInvoiceFilterFrm',
                                                            bodyPadding: 10,
                                                            title: '',
                                                            layout: {
                                                                type: 'vbox',
                                                                align: 'stretch'
                                                            },
                                                            items: [
                                                                {
                                                                    xtype: 'textfield',
                                                                    id: 'CcpInvoiceQuery',
                                                                    itemId: 'CcpInvoiceQuery',
                                                                    name: 'query',
                                                                    emptyText: 'Cerca ...'
                                                                },
                                                                {
                                                                    xtype: 'fieldset',
                                                                    title: 'Numero',
                                                                    items: [
                                                                        {
                                                                            xtype: 'numberfield',
                                                                            anchor: '100%',
                                                                            fieldLabel: 'Da',
                                                                            labelWidth: 50,
                                                                            name: 'number_from',
                                                                            hideTrigger: true
                                                                        },
                                                                        {
                                                                            xtype: 'numberfield',
                                                                            anchor: '100%',
                                                                            fieldLabel: 'A',
                                                                            labelWidth: 50,
                                                                            name: 'number_to',
                                                                            hideTrigger: true
                                                                        }
                                                                    ]
                                                                },
                                                                {
                                                                    xtype: 'fieldset',
                                                                    title: 'Data fattura',
                                                                    items: [
                                                                        {
                                                                            xtype: 'datefield',
                                                                            anchor: '100%',
                                                                            id: 'CcpInvoiceCreateFrom',
                                                                            fieldLabel: 'Da',
                                                                            labelWidth: 40,
                                                                            name: 'date_start',
                                                                            format: 'd/m/Y',
                                                                            submitFormat: 'Y-m-d'
                                                                        },
                                                                        {
                                                                            xtype: 'datefield',
                                                                            anchor: '100%',
                                                                            id: 'CcpInvoiceCreateTo',
                                                                            fieldLabel: 'A',
                                                                            labelWidth: 40,
                                                                            name: 'date_end',
                                                                            format: 'd/m/Y',
                                                                            submitFormat: 'Y-m-d'
                                                                        }
                                                                    ]
                                                                },
                                                                {
                                                                    xtype: 'fieldset',
                                                                    title: 'Situazione pagamento',
                                                                    layout: {
                                                                        type: 'hbox',
                                                                        align: 'stretch'
                                                                    },
                                                                    items: [
                                                                        {
                                                                            xtype: 'checkboxfield',
                                                                            fieldLabel: '',
                                                                            name: 'paid',
                                                                            boxLabel: 'Pagata',
                                                                            checked: true,
                                                                            inputValue: '1',
                                                                            uncheckedValue: '0'
                                                                        },
                                                                        {
                                                                            xtype: 'tbspacer',
                                                                            flex: 1
                                                                        },
                                                                        {
                                                                            xtype: 'checkboxfield',
                                                                            fieldLabel: '',
                                                                            name: 'not_paid',
                                                                            boxLabel: 'Non pagata',
                                                                            checked: true,
                                                                            inputValue: '1',
                                                                            uncheckedValue: '0'
                                                                        }
                                                                    ]
                                                                },
                                                                {
                                                                    xtype: 'fieldset',
                                                                    title: 'Situazione pubblicazione',
                                                                    layout: {
                                                                        type: 'hbox',
                                                                        align: 'stretch'
                                                                    },
                                                                    items: [
                                                                        {
                                                                            xtype: 'checkboxfield',
                                                                            fieldLabel: '',
                                                                            name: 'published',
                                                                            boxLabel: 'Pubblicato',
                                                                            checked: true,
                                                                            inputValue: '1',
                                                                            uncheckedValue: '0'
                                                                        },
                                                                        {
                                                                            xtype: 'tbspacer',
                                                                            flex: 1
                                                                        },
                                                                        {
                                                                            xtype: 'checkboxfield',
                                                                            fieldLabel: '',
                                                                            name: 'unpublished',
                                                                            boxLabel: 'Non pubblicato',
                                                                            checked: true,
                                                                            inputValue: '1',
                                                                            uncheckedValue: '0'
                                                                        }
                                                                    ]
                                                                },
                                                                {
                                                                    xtype: 'container',
                                                                    layout: 'fit',
                                                                    items: [
                                                                        {
                                                                            xtype: 'button',
                                                                            handler: function(button, e) {
                                                                                Ext.getStore('CcpInvoices').loadPage(1);
                                                                            },
                                                                            text: 'Cerca'
                                                                        }
                                                                    ]
                                                                }
                                                            ]
                                                        }
                                                    ]
                                                },
                                                {
                                                    xtype: 'gridpanel',
                                                    region: 'center',
                                                    id: 'CcpInvoicesGrid',
                                                    title: '',
                                                    store: 'CcpInvoices',
                                                    columns: [
                                                        {
                                                            xtype: 'actioncolumn',
                                                            width: 50,
                                                            align: 'center',
                                                            dataIndex: 'credit_note',
                                                            iconCls: '',
                                                            items: [
                                                                {
                                                                    getClass: function(v, metadata, r, rowIndex, colIndex, store) {
                                                                        if(v===true){
                                                                            return 'icon-arrow_undo';
                                                                        }
                                                                    },
                                                                    tooltip: 'Nota di credito'
                                                                },
                                                                {
                                                                    getClass: function(v, metadata, r, rowIndex, colIndex, store) {
                                                                        if(r.get('publication_path')){
                                                                            return 'icon-world';
                                                                        }
                                                                    },
                                                                    tooltip: 'Pubblicata'
                                                                }
                                                            ]
                                                        },
                                                        {
                                                            xtype: 'gridcolumn',
                                                            renderer: function(value, metaData, record, rowIndex, colIndex, store, view) {
                                                                var number = value;

                                                                if (record.get('suffix')) number += '_' + record.get('suffix');

                                                                return number + '/' + record.get('date').getFullYear();
                                                            },
                                                            width: 100,
                                                            align: 'center',
                                                            dataIndex: 'number',
                                                            text: 'Numero'
                                                        },
                                                        {
                                                            xtype: 'datecolumn',
                                                            align: 'center',
                                                            dataIndex: 'date',
                                                            text: 'Data fattura',
                                                            format: 'd/m/Y'
                                                        },
                                                        {
                                                            xtype: 'datecolumn',
                                                            hidden: true,
                                                            width: 139,
                                                            align: 'center',
                                                            dataIndex: 'expiration_date',
                                                            text: 'Data fattura',
                                                            format: 'd/m/Y'
                                                        },
                                                        {
                                                            xtype: 'gridcolumn',
                                                            dataIndex: 'accountholder',
                                                            text: 'Intestatario',
                                                            flex: 1
                                                        },
                                                        {
                                                            xtype: 'gridcolumn',
                                                            sortable: false,
                                                            dataIndex: 'debitors',
                                                            text: 'Debitori',
                                                            flex: 1
                                                        },
                                                        {
                                                            xtype: 'numbercolumn',
                                                            align: 'right',
                                                            dataIndex: 'total',
                                                            text: 'Totale'
                                                        },
                                                        {
                                                            xtype: 'actioncolumn',
                                                            hidden: true,
                                                            width: 70,
                                                            align: 'center',
                                                            dataIndex: 'fpa_status',
                                                            text: 'SDI',
                                                            items: [
                                                                {
                                                                    getClass: function(v, metadata, r, rowIndex, colIndex, store) {
                                                                        /*
                                                                        if (v === 'SN' || v === 'RC' || v === 'MC') {
                                                                        return 'icon-bullet_yellow';
                                                                        } else if(v === 'DT' || v === 'NEA') {
                                                                        return 'icon-bullet_green';
                                                                        } else
                                                                        */
                                                                        //if(v === 'NS' || v === 'AT' || v === 'NER') {
                                                                        if(v in ['NS', 'AT', 'NER', '2', '6', '8', '10', '14', '17', '23']) {
                                                                            return 'icon-bullet_red';
                                                                        } else if (!v) {
                                                                            return '';
                                                                        }
                                                                        return 'icon-bullet_green';
                                                                    },
                                                                    handler: function(view, rowIndex, colIndex, item, e, record, row) {
                                                                        Ext.widget('CcpInvoiceTransmissionWin').show();

                                                                        Ext.getStore('CcpInvoiceTransmissions').load({
                                                                            params: {invoice: record.get('id')}
                                                                        });
                                                                    }
                                                                }
                                                            ]
                                                        },
                                                        {
                                                            xtype: 'actioncolumn',
                                                            width: 70,
                                                            align: 'center',
                                                            dataIndex: 'ds_status',
                                                            text: 'BlueNext',
                                                            items: [
                                                                {
                                                                    getClass: function(v, metadata, r, rowIndex, colIndex, store) {
                                                                        if (v === 'wait') {
                                                                            return 'icon-bullet_yellow';
                                                                        } else if(v === 'success') {
                                                                            return 'icon-bullet_green';
                                                                        } else if(v === 'error') {
                                                                            return 'icon-bullet_red';
                                                                        }
                                                                    },
                                                                    handler: function(view, rowIndex, colIndex, item, e, record, row) {
                                                                        Ext.widget('CcpInvoiceTransmissionWin').show();

                                                                        Ext.getStore('CcpInvoiceTransmissions').load({
                                                                            params: {invoice: record.get('id')}
                                                                        });
                                                                    }
                                                                }
                                                            ]
                                                        }
                                                    ],
                                                    viewConfig: {
                                                        listeners: {
                                                            itemcontextmenu: {
                                                                fn: me.onViewItemContextMenu,
                                                                scope: me
                                                            }
                                                        }
                                                    },
                                                    selModel: Ext.create('Ext.selection.CheckboxModel', {

                                                    }),
                                                    dockedItems: [
                                                        {
                                                            xtype: 'pagingtoolbar',
                                                            dock: 'bottom',
                                                            width: 360,
                                                            displayInfo: true,
                                                            store: 'CcpInvoices'
                                                        },
                                                        {
                                                            xtype: 'toolbar',
                                                            dock: 'top',
                                                            items: [
                                                                {
                                                                    xtype: 'button',
                                                                    handler: function(button, e) {
                                                                        Ext.widget('CcpInvoiceNewWin').show();
                                                                    },
                                                                    iconCls: 'icon-add',
                                                                    text: 'Nuova'
                                                                },
                                                                {
                                                                    xtype: 'button',
                                                                    handler: function(button, e) {
                                                                        Ext.widget('EasyImportWin').show();
                                                                    },
                                                                    hidden: true,
                                                                    text: 'Importa fatture'
                                                                },
                                                                {
                                                                    xtype: 'button',
                                                                    text: 'Azioni su fatture filtrate/selezionate',
                                                                    menu: {
                                                                        xtype: 'menu',
                                                                        width: 200,
                                                                        items: [
                                                                            {
                                                                                xtype: 'menuitem',
                                                                                handler: function(item, e) {
                                                                                    var /*params = {
                                                                                        expiration_date_start: Ext.getCmp('CcpInvoiceFrom').getValue(),
                                                                                        expiration_date_end: Ext.getCmp('CcpInvoiceTo').getValue(),
                                                                                        query: Ext.getCmp('CcpInvoiceQuery').getValue(),
                                                                                        massive: 1
                                                                                    },*/
                                                                                    params = Ext.getCmp('CcpInvoiceFilterFrm').getFilters(),
                                                                                    sel = Ext.getCmp('CcpInvoicesGrid').getSelectionModel().getSelection(),
                                                                                    ids=[];

                                                                                    params.massive=1;
                                                                                    if(sel.length>0) {
                                                                                        Ext.each(sel, function(v){
                                                                                            ids.push(v.get('id'));
                                                                                        });

                                                                                        params.ids = Ext.encode(ids);
                                                                                    }


                                                                                    Ext.Ajax.request({
                                                                                        url: '/mc2-api/ccp/invoice/publish',
                                                                                        params: params,
                                                                                        success: function(r) {
                                                                                            var res = Ext.decode(r.responseText);
                                                                                            if(res.success === true) {
                                                                                                Ext.Msg.alert('INFORMAZIONI', 'Fatture in pubblicazione. La pubblicazione di molte fatture potrebbe richiedere qualche minuto');
                                                                                                //Ext.getStore('CcpInvoices').load();
                                                                                            } else {
                                                                                                Ext.Msg.alert('ERRORE', res.message);
                                                                                            }
                                                                                        }
                                                                                    });
                                                                                },
                                                                                iconCls: 'icon-world',
                                                                                text: 'Pubblica'
                                                                            },
                                                                            {
                                                                                xtype: 'menuitem',
                                                                                handler: function(item, e) {
                                                                                    var /*params = {
                                                                                        expiration_date_start: Ext.getCmp('CcpInvoiceFrom').getValue(),
                                                                                        expiration_date_end: Ext.getCmp('CcpInvoiceTo').getValue(),
                                                                                        query: Ext.getCmp('CcpInvoiceQuery').getValue(),
                                                                                        massive: 1
                                                                                    },*/
                                                                                    params = Ext.getCmp('CcpInvoiceFilterFrm').getFilters(),
                                                                                    sel = Ext.getCmp('CcpInvoicesGrid').getSelectionModel().getSelection(),
                                                                                    ids=[];

                                                                                    params.massive=1;
                                                                                    if(sel.length>0) {
                                                                                        Ext.each(sel, function(v){
                                                                                            ids.push(v.get('id'));
                                                                                        });

                                                                                        params.ids = Ext.encode(ids);
                                                                                    }


                                                                                    Ext.Ajax.request({
                                                                                        url: '/mc2-api/ccp/invoice/unpublish',
                                                                                        params: params,
                                                                                        success: function(r) {
                                                                                            var res = Ext.decode(r.responseText);
                                                                                            if(res.success === true) {
                                                                                                Ext.Msg.alert('INFORMAZIONI', 'Stiamo rimuovendo le fatture publicate. La rimozione di molte fatture potrebbe richiedere qualche minuto');
                                                                                                //Ext.getStore('CcpInvoices').load();
                                                                                            } else {
                                                                                                Ext.Msg.alert('ERRORE', res.message);
                                                                                            }
                                                                                        }
                                                                                    });
                                                                                },
                                                                                iconCls: 'icon-world_delete',
                                                                                text: 'Rimuovi pubblicazione'
                                                                            },
                                                                            {
                                                                                xtype: 'menuitem',
                                                                                handler: function(item, e) {
                                                                                    var ids = [],
                                                                                        filters=Ext.getCmp('CcpInvoiceFilterFrm').getFilters();


                                                                                    var ids = [];
                                                                                    Ext.each(Ext.getCmp('CcpInvoicesGrid').getSelectionModel().getSelection(), function(val){
                                                                                        ids.push(val.get('id'));
                                                                                    });


                                                                                    window.open('/mc2-api/ccp/send_fpa_invoice?step=xml&ids=' + Ext.encode(ids)+'&filters='+Ext.encode(filters));

                                                                                },
                                                                                iconCls: 'icon-page_green',
                                                                                text: 'Esporta xml fatture per SDI'
                                                                            },
                                                                            {
                                                                                xtype: 'menuitem',
                                                                                handler: function(item, e) {

                                                                                    Ext.widget('CcpSignLoginWin').show();
                                                                                },
                                                                                hidden: true,
                                                                                iconCls: 'icon-page_go',
                                                                                text: 'Invia fattura SDI'
                                                                            },
                                                                            {
                                                                                xtype: 'menuitem',
                                                                                handler: function(item, e) {
                                                                                    var ids = [],
                                                                                        filters=Ext.getCmp('CcpInvoiceFilterFrm').getFilters();

                                                                                    Ext.each(Ext.getCmp('CcpInvoicesGrid').getSelectionModel().getSelection(), function(val){
                                                                                        ids.push(val.get('id'));
                                                                                    });



                                                                                    Ext.Ajax.request({
                                                                                        url: '/mc2-api/ccp/send_fpa_invoice?step=ds',
                                                                                        method:'POST',
                                                                                        params: {
                                                                                            ids : Ext.encode(ids),
                                                                                            filters: Ext.encode(filters)
                                                                                        },
                                                                                        success: function(r,res){
                                                                                            Ext.Msg.alert('SUCCESSO', 'Invio delle fatture in corso. Controllare periodicamente lo stato per verificare che tutte siano state inviate correttamente');
                                                                                        }
                                                                                    });
                                                                                },
                                                                                iconCls: 'icon-page_go',
                                                                                text: 'Invia fattura a BlueNext'
                                                                            }
                                                                        ]
                                                                    }
                                                                },
                                                                {
                                                                    xtype: 'tbspacer',
                                                                    flex: 1
                                                                },
                                                                {
                                                                    xtype: 'button',
                                                                    handler: function(button, e) {
                                                                        var rec = {};

                                                                        rec.newSpool = 1;
                                                                        rec.print = 'ExportInvoicesList';
                                                                        rec.namespace = 'CCP';
                                                                        rec.type = 'XLS';
                                                                        rec.mime = 'application/vnd.ms-excel';
                                                                        //rec.expiration_date_start = Ext.getCmp('CcpInvoiceFrom').getValue();
                                                                        //rec.expiration_date_end = Ext.getCmp('CcpInvoiceTo').getValue();
                                                                        //rec.query = Ext.getCmp('CcpInvoiceQuery').getValue();

                                                                        rec=Object.assign({}, rec, Ext.getCmp('CcpInvoiceFilterFrm').getFilters());


                                                                        Ext.Ajax.request({
                                                                            url: '/mc2-api/core/print',
                                                                            params: rec,
                                                                            success: function(response, opts) {
                                                                                var res = Ext.decode(response.responseText);
                                                                                mc2ui.app.showNotifyPrint(res);
                                                                            }
                                                                        });
                                                                    },
                                                                    iconCls: 'icon-page_excel',
                                                                    text: 'Esporta elenco'
                                                                },
                                                                {
                                                                    xtype: 'button',
                                                                    handler: function(button, e) {
                                                                        Ext.widget('CcpDichiarazionePrintWin').show();
                                                                    },
                                                                    text: 'Stampa dichiarazione'
                                                                },
                                                                {
                                                                    xtype: 'button',
                                                                    handler: function(button, e) {
                                                                        var rec = {},
                                                                            filter;

                                                                        rec.newSpool = 1;
                                                                        rec.print = 'Invoice';
                                                                        rec.namespace = 'CCP';
                                                                        rec.type = 'PDF';
                                                                        rec.mime = 'application/pdf';

                                                                        filter = Ext.getCmp('CcpInvoiceFilterFrm').getFilters();
                                                                        filter.massive=1;
                                                                        /*rec.filter = Ext.encode({
                                                                        from: Ext.getCmp('CcpInvoiceFrom').getValue(),
                                                                        to: Ext.getCmp('CcpInvoiceTo').getValue(),
                                                                        create_from: Ext.getCmp('CcpInvoiceCreateFrom').getValue(),
                                                                        create_to: Ext.getCmp('CcpInvoiceCreateTo').getValue(),
                                                                        massive: 1,
                                                                        query: Ext.getCmp('CcpInvoiceQuery').getValue()
                                                                        });*/
                                                                        rec.filter = Ext.encode(filter);

                                                                        Ext.Ajax.request({
                                                                            url: '/mc2-api/core/print',
                                                                            params: rec,
                                                                            success: function(response, opts) {
                                                                                var res = Ext.decode(response.responseText);
                                                                                mc2ui.app.showNotifyPrint(res);
                                                                            }
                                                                        });
                                                                    },
                                                                    iconCls: 'icon-printer',
                                                                    text: 'Stampa fatture filtrate'
                                                                }
                                                            ]
                                                        }
                                                    ]
                                                },
                                                {
                                                    xtype: 'menu',
                                                    region: 'west',
                                                    hidden: true,
                                                    id: 'CcpInvoiceMovementsMn',
                                                    itemId: 'CcpInvoiceMovementsMn',
                                                    width: 150,
                                                    items: [
                                                        {
                                                            xtype: 'menuitem',
                                                            handler: function(item, e) {
                                                                var id = Ext.getCmp('CcpInvoicesGrid').getSelectionModel().getSelection()[0].get('id');

                                                                // Ext.getStore('CcpInvoiceAccountHolders').removeAll();

                                                                Ext.Ajax.request({
                                                                    url: '/mc2-api/ccp/invoice/' + id,
                                                                    success: function(response, opts) {
                                                                        Ext.widget('CcpInvoiceAccountHolderWin').show();
                                                                        Ext.getCmp('CcpInvoiceUpdateAccountHolderBtn').show();
                                                                        Ext.getCmp('CcpInvoiceGenerationBtn').hide();
                                                                        Ext.getCmp('CcpInvoiceAccountHolderWin').recordId = id;
                                                                        var subject_id = Ext.decode(Ext.decode(response.responseText).results.rows)[0].subject_id;
                                                                        var accountholder = Ext.decode(Ext.decode(response.responseText).results.accountholder);
                                                                        var dataHeader = Ext.decode(Ext.decode(response.responseText).results.header);
                                                                        var payment_method = Ext.decode(Ext.decode(response.responseText).results.payment_method);

                                                                        data = accountholder[0];
                                                                        data.payment_method = payment_method;

                                                                        var dd = new Date(1970,0,1);
                                                                        dd.setSeconds(parseInt(data.data_mandato_rid)+60*60*3);
                                                                        data.data_mandato_rid = Ext.Date.format(dd, 'Y-m-d');
                                                                        data.tipo_addebito = parseInt(data.tipo_addebito);
                                                                        data.subject_id = subject_id;
                                                                        Ext.getCmp('CcpInvoiceAccountholderFrm').getForm().setValues(data);
                                                                        Ext.getCmp('CcpInvoiceHeaderFrm').getForm().setValues(dataHeader);

                                                                    }
                                                                });
                                                            },
                                                            id: 'CcpInvoiceEditAccountHolderMn',
                                                            itemId: 'CcpInvoiceEditAccountHolderMn',
                                                            iconCls: 'icon-pencil',
                                                            text: 'Modifica intestatario'
                                                        },
                                                        {
                                                            xtype: 'menuitem',
                                                            handler: function(item, e) {
                                                                var rec = {};

                                                                rec.newSpool = 1;
                                                                rec.print = 'Invoice';
                                                                rec.namespace = 'CCP';
                                                                rec.type = 'PDF';
                                                                rec.mime = 'application/pdf';
                                                                rec.filter = Ext.encode(Ext.getCmp('CcpInvoicesGrid').getSelectionModel().getSelection()[0].data);

                                                                Ext.Ajax.request({
                                                                    url: '/mc2-api/core/print',
                                                                    params: rec,
                                                                    success: function(response, opts) {
                                                                        var res = Ext.decode(response.responseText);
                                                                        mc2ui.app.showNotifyPrint(res);
                                                                    }
                                                                });
                                                            },
                                                            iconCls: 'icon-printer',
                                                            text: 'Stampa'
                                                        },
                                                        {
                                                            xtype: 'menuitem',
                                                            handler: function(item, e) {
                                                                var id = Ext.getCmp('CcpInvoicesGrid').getSelectionModel().getSelection()[0].get('id');

                                                                Ext.Ajax.request({
                                                                    url: '/mc2-api/ccp/invoice/credit_note',
                                                                    params: {
                                                                        id: id
                                                                    },
                                                                    success: function(r){
                                                                        Ext.Msg.alert('SUCCESSO', 'Nota di credito inserita');
                                                                        Ext.getStore('CcpInvoices').load();
                                                                    }
                                                                });
                                                            },
                                                            hidden: true,
                                                            iconCls: 'icon-arrow_undo',
                                                            text: 'Emetti nota di credito',
                                                            listeners: {
                                                                render: {
                                                                    fn: me.onMenuitemRender,
                                                                    scope: me
                                                                }
                                                            }
                                                        },
                                                        {
                                                            xtype: 'menuitem',
                                                            handler: function(item, e) {
                                                                var id = Ext.getCmp('CcpInvoicesGrid').getSelectionModel().getSelection()[0].get('id');


                                                                Ext.Ajax.request({
                                                                    url: '/mc2-api/ccp/invoice/publish',
                                                                    params: {
                                                                        ids: Ext.encode([id])
                                                                    },
                                                                    success: function(r) {
                                                                        var res = Ext.decode(r.responseText);
                                                                        if(res.success === true) {
                                                                            Ext.Msg.alert('INFORMAZIONI', 'Fattura pubblicata correttamente');
                                                                            Ext.getStore('CcpInvoices').load();
                                                                        } else {
                                                                            Ext.Msg.alert('ERRORE', res.message);
                                                                        }
                                                                    }
                                                                });
                                                            },
                                                            id: 'CcpInvoicePublishMn',
                                                            iconCls: 'icon-world',
                                                            text: 'Pubblica fattura'
                                                        },
                                                        {
                                                            xtype: 'menuitem',
                                                            handler: function(item, e) {
                                                                var id = Ext.getCmp('CcpInvoicesGrid').getSelectionModel().getSelection()[0].get('id');


                                                                Ext.Ajax.request({
                                                                    url: '/mc2-api/ccp/invoice/unpublish',
                                                                    params: {
                                                                        ids: Ext.encode([id])
                                                                    },
                                                                    success: function(r) {
                                                                        var res = Ext.decode(r.responseText);
                                                                        if(res.success === true) {
                                                                            Ext.Msg.alert('INFORMAZIONI', 'Fattura non più pubblicata');
                                                                            Ext.getStore('CcpInvoices').load();
                                                                        } else {
                                                                            Ext.Msg.alert('ERRORE', res.message);
                                                                        }
                                                                    }
                                                                });
                                                            },
                                                            id: 'CcpInvoiceUnpublishMn',
                                                            iconCls: 'icon-world_delete',
                                                            text: 'Rimuovi pubblicazione'
                                                        },
                                                        {
                                                            xtype: 'menuitem',
                                                            handler: function(item, e) {
                                                                var id = Ext.getCmp('CcpInvoicesGrid').getSelectionModel().getSelection()[0].get('id');
                                                                Ext.Ajax.request({
                                                                    url: '/mc2-api/ccp/invoice/' + id,
                                                                    method: 'DELETE',
                                                                    success: function(response, opts) {
                                                                        var res = Ext.decode(response.responseText);
                                                                        if(res.success) {
                                                                            Ext.MessageBox.alert('INFORMAZIONE', 'Fattura eliminata correttamente');
                                                                            Ext.getStore('CcpInvoices').load();
                                                                        } else {
                                                                            Ext.Msg.alert('ERRORE', res.message);
                                                                        }
                                                                    }
                                                                });
                                                            },
                                                            iconCls: 'icon-delete',
                                                            text: 'Elimina'
                                                        }
                                                    ],
                                                    listeners: {
                                                        show: {
                                                            fn: me.onCcpInvoiceMovementsMnShow,
                                                            scope: me
                                                        }
                                                    }
                                                }
                                            ]
                                        },
                                        {
                                            xtype: 'panel',
                                            layout: 'fit',
                                            title: 'Entrata',
                                            items: [
                                                {
                                                    xtype: 'gridpanel',
                                                    title: '',
                                                    store: 'Invoices',
                                                    columns: [
                                                        {
                                                            xtype: 'gridcolumn',
                                                            align: 'center',
                                                            dataIndex: 'number',
                                                            text: 'Numero'
                                                        },
                                                        {
                                                            xtype: 'datecolumn',
                                                            align: 'center',
                                                            dataIndex: 'date',
                                                            text: 'Data',
                                                            format: 'd/m/Y'
                                                        },
                                                        {
                                                            xtype: 'gridcolumn',
                                                            dataIndex: 'name',
                                                            text: 'Nome',
                                                            flex: 1
                                                        },
                                                        {
                                                            xtype: 'gridcolumn',
                                                            dataIndex: 'fiscal_code',
                                                            text: 'Codice fiscale'
                                                        },
                                                        {
                                                            xtype: 'gridcolumn',
                                                            dataIndex: 'vat_number',
                                                            text: 'PIVA'
                                                        },
                                                        {
                                                            xtype: 'gridcolumn',
                                                            width: 200,
                                                            dataIndex: 'address',
                                                            text: 'Indirizzo'
                                                        },
                                                        {
                                                            xtype: 'gridcolumn',
                                                            width: 150,
                                                            dataIndex: 'city_name',
                                                            text: 'Città'
                                                        },
                                                        {
                                                            xtype: 'gridcolumn',
                                                            width: 65,
                                                            align: 'center',
                                                            dataIndex: 'city_province',
                                                            text: 'Provincia'
                                                        },
                                                        {
                                                            xtype: 'numbercolumn',
                                                            width: 80,
                                                            align: 'center',
                                                            dataIndex: 'city_postal_code',
                                                            text: 'CAP',
                                                            format: '0'
                                                        },
                                                        {
                                                            xtype: 'numbercolumn',
                                                            align: 'center',
                                                            dataIndex: 'total',
                                                            text: 'Totale'
                                                        },
                                                        {
                                                            xtype: 'actioncolumn',
                                                            width: 50,
                                                            align: 'center',
                                                            items: [
                                                                {
                                                                    handler: function(view, rowIndex, colIndex, item, e, record, row) {
                                                                        if(!record.get('xml')) return;
                                                                        var id = record.get('id');

                                                                        window.open('/mc2-api/ccp/pinvoice/'+id+'/print','_blank');
                                                                    },
                                                                    getClass: function(v, metadata, r, rowIndex, colIndex, store) {
                                                                        if(r.get('xml')) return 'icon-printer';
                                                                        else return '';
                                                                    }
                                                                },
                                                                {
                                                                    handler: function(view, rowIndex, colIndex, item, e, record, row) {
                                                                        Ext.Msg.confirm('ATTENZIONE', 'Eliminare la fattura selezionata?', function (btn){
                                                                            if(btn === 'yes') {
                                                                                Ext.Ajax.request({
                                                                                    method: 'DELETE',
                                                                                    url: '/mc2-api/ccp/pinvoice/' + record.get('id'),
                                                                                    success: function (){
                                                                                        Ext.getStore('Invoices').load();
                                                                                    }
                                                                                });
                                                                            }
                                                                        });
                                                                    },
                                                                    iconCls: 'icon-delete'
                                                                }
                                                            ]
                                                        }
                                                    ],
                                                    dockedItems: [
                                                        {
                                                            xtype: 'pagingtoolbar',
                                                            dock: 'bottom',
                                                            width: 360,
                                                            displayInfo: true,
                                                            store: 'Invoices'
                                                        }
                                                    ]
                                                }
                                            ],
                                            listeners: {
                                                show: {
                                                    fn: me.onTabShow,
                                                    scope: me
                                                }
                                            },
                                            dockedItems: [
                                                {
                                                    xtype: 'toolbar',
                                                    dock: 'top',
                                                    items: [
                                                        {
                                                            xtype: 'button',
                                                            text: 'Carica fattura',
                                                            menu: {
                                                                xtype: 'menu',
                                                                items: [
                                                                    {
                                                                        xtype: 'menuitem',
                                                                        handler: function(item, e) {
                                                                            Ext.widget('InvoiceEditWin').show();
                                                                            Ext.getCmp('InvoiceEditManualCnt').destroy();
                                                                        },
                                                                        text: 'Da file'
                                                                    },
                                                                    {
                                                                        xtype: 'menuitem',
                                                                        handler: function(item, e) {
                                                                            Ext.widget('InvoiceEditWin').show();
                                                                            Ext.getCmp('InvoiceEditFileUpload').destroy();
                                                                        },
                                                                        text: 'Manualmente'
                                                                    }
                                                                ]
                                                            }
                                                        },
                                                        {
                                                            xtype: 'button',
                                                            handler: function(button, e) {
                                                                Ext.Ajax.request({
                                                                    method: 'GET',
                                                                    url: '/mc2-api/ccp/pinvoice/download'
                                                                });
                                                                Ext.Msg.alert('INFO', 'Scaricamento fatture in corso. Ricaricare la lista periodicamente per verificare se ne sono state importate di nuove.');
                                                            },
                                                            text: 'Scarica fatture'
                                                        }
                                                    ]
                                                }
                                            ]
                                        }
                                    ]
                                }
                            ]
                        },
                        {
                            xtype: 'panel',
                            loadDepositSlipRows: function(id) {
                                /*Ext.getStore('CcpDepositSlipRows').removeAll();

                                Ext.Ajax.request({
                                url: '/mc2-api/ccp/deposit_slip/' + id,
                                success: function(r){
                                var res = Ext.decode(r.responseText);
                                Ext.getStore('CcpDepositSlipRows').add(res.results.ids);
                                }
                                });*/
                                Ext.getStore('CcpInvoiceDepositSlips').removeAll();

                                Ext.getStore('CcpInvoiceDepositSlips').load({
                                    params: {
                                        ds_id: id
                                    }
                                });
                            },
                            id: 'CcpDepositSlipPnl',
                            itemId: 'CcpDepositSlipPnl',
                            layout: 'fit',
                            title: 'Distinte SEPA',
                            tabConfig: {
                                xtype: 'tab',
                                iconCls: 'icon-newspaper_go'
                            },
                            dockedItems: [
                                {
                                    xtype: 'toolbar',
                                    dock: 'top',
                                    items: [
                                        {
                                            xtype: 'button',
                                            handler: function(button, e) {
                                                var wd = Ext.widget('CcpDepositSlipNewWin');
                                                if(!mc2ui.app.settings.invoiceEnabled)  {

                                                    var wi = Ext.widget('CcpInvoiceNewWin');
                                                    wi.closable=false;
                                                    wd.modal=false;
                                                    wi.modal=false;
                                                    wd.setHeight(wi.height);
                                                    wd.show();
                                                    wi.show();

                                                    var ww = Ext.getBody().getViewSize().width;
                                                    wi.setX(parseInt((ww-wi.width-wd.width)/2));

                                                    wd.setX(ww-wd.width-parseInt((ww-wi.width-wd.width)/2));

                                                    wd.setTitle();
                                                    Ext.getCmp('CcpDepositSlipAddFrm').hide();
                                                    Ext.getCmp('CcpDepositSlipTb').hide();

                                                } else wd.show();
                                            },
                                            iconCls: 'icon-add',
                                            text: 'Nuova'
                                        }
                                    ]
                                }
                            ],
                            items: [
                                {
                                    xtype: 'gridpanel',
                                    id: 'CcpDepositSlipGrid',
                                    itemId: 'CcpDepositSlipGrid',
                                    title: '',
                                    store: 'CcpDepositSlips',
                                    columns: [
                                        {
                                            xtype: 'numbercolumn',
                                            width: 67,
                                            align: 'center',
                                            dataIndex: 'number',
                                            text: 'Numero',
                                            format: '0'
                                        },
                                        {
                                            xtype: 'datecolumn',
                                            align: 'center',
                                            dataIndex: 'date',
                                            text: 'Data',
                                            format: 'd/m/Y'
                                        },
                                        {
                                            xtype: 'gridcolumn',
                                            dataIndex: 'bank_account_name',
                                            text: 'Banca',
                                            flex: 1
                                        },
                                        {
                                            xtype: 'gridcolumn',
                                            width: 75,
                                            align: 'center',
                                            dataIndex: 'row_number',
                                            text: 'N. Righe'
                                        },
                                        {
                                            xtype: 'gridcolumn',
                                            width: 75,
                                            align: 'center',
                                            dataIndex: 'invoices_count',
                                            text: 'N. Fatture'
                                        },
                                        {
                                            xtype: 'gridcolumn',
                                            text: 'Dettaglio importi',
                                            columns: [
                                                {
                                                    xtype: 'numbercolumn',
                                                    align: 'right',
                                                    dataIndex: 'movements_total',
                                                    text: 'Movimenti'
                                                },
                                                {
                                                    xtype: 'numbercolumn',
                                                    align: 'right',
                                                    dataIndex: 'bolli_total',
                                                    text: 'Bolli'
                                                },
                                                {
                                                    xtype: 'numbercolumn',
                                                    width: 108,
                                                    align: 'right',
                                                    dataIndex: 'collection_cost_total',
                                                    text: 'Spese di incasso'
                                                },
                                                {
                                                    xtype: 'numbercolumn',
                                                    align: 'right',
                                                    dataIndex: 'total',
                                                    text: 'Totale'
                                                }
                                            ]
                                        },
                                        {
                                            xtype: 'gridcolumn',
                                            text: 'Insoluto',
                                            columns: [
                                                {
                                                    xtype: 'numbercolumn',
                                                    align: 'center',
                                                    dataIndex: 'unpaid_rows',
                                                    text: 'Righe',
                                                    format: '0'
                                                },
                                                {
                                                    xtype: 'gridcolumn',
                                                    renderer: function(value, metaData, record, rowIndex, colIndex, store, view) {
                                                        var valueFormat = Ext.util.Format.number(value, '0,000.00');
                                                        if (value > 0) {
                                                            return '<font style="color:red">' + valueFormat + '</font>';
                                                        }
                                                        return valueFormat;
                                                    },
                                                    align: 'right',
                                                    dataIndex: 'unpaid',
                                                    text: 'Totale'
                                                }
                                            ]
                                        }
                                    ],
                                    viewConfig: {
                                        listeners: {
                                            itemcontextmenu: {
                                                fn: me.onViewItemContextMenu1,
                                                scope: me
                                            }
                                        }
                                    },
                                    dockedItems: [
                                        {
                                            xtype: 'pagingtoolbar',
                                            dock: 'bottom',
                                            width: 360,
                                            displayInfo: true,
                                            store: 'CcpDepositSlips'
                                        }
                                    ]
                                },
                                {
                                    xtype: 'menu',
                                    id: 'CcpDepositSlipMn',
                                    itemId: 'CcpDepositSlipMn',
                                    width: 150,
                                    items: [
                                        {
                                            xtype: 'menuitem',
                                            handler: function(item, e) {
                                                var id = Ext.getCmp('CcpDepositSlipGrid').getSelectionModel().getSelection()[0].get('id');

                                                Ext.widget('CcpDepositSlipRowWin').show();
                                                Ext.getCmp('CcpDepositSlipPnl').loadDepositSlipRows(id);
                                            },
                                            iconCls: 'icon-pencil',
                                            text: 'Gestisci righe distinta'
                                        },
                                        {
                                            xtype: 'menuitem',
                                            handler: function(item, e) {

                                                var id = Ext.getCmp('CcpDepositSlipGrid').getSelectionModel().getSelection()[0].get('id');

                                                window.open('/mc2-api/ccp/deposit_slip_sepa?id=' + id);

                                            },
                                            iconCls: 'icon-newspaper_go',
                                            text: 'Esportazione SEPA'
                                        },
                                        {
                                            xtype: 'menuitem',
                                            handler: function(item, e) {
                                                var id = Ext.getCmp('CcpDepositSlipGrid').getSelectionModel().getSelection()[0].get('id');

                                                Ext.Ajax.request({
                                                    url: '/mc2-api/ccp/deposit_slip/' + id,
                                                    method: 'DELETE',
                                                    success: function(r){
                                                        Ext.getStore('CcpDepositSlips').load();
                                                    }
                                                });
                                            },
                                            iconCls: 'icon-delete',
                                            text: 'Elimina'
                                        },
                                        {
                                            xtype: 'menuitem',
                                            handler: function(item, e) {
                                                var rec = {},
                                                    id = Ext.getCmp('CcpDepositSlipGrid').getSelectionModel().getSelection()[0].get('id');

                                                rec.newSpool = 1;
                                                rec.namespace = 'CCP';
                                                rec.type = 'XLS';
                                                rec.mime = 'application/pdf';
                                                rec.print = 'DepositSlip';
                                                rec.deposit_slip_id = id;


                                                Ext.Ajax.request({
                                                    url: '/mc2-api/core/print',
                                                    params: rec,
                                                    success: function(response, opts) {
                                                        var res = Ext.decode(response.responseText);
                                                        mc2ui.app.showNotifyPrint(res);
                                                    }
                                                });
                                            },
                                            iconCls: 'icon-printer',
                                            text: 'Stampa'
                                        }
                                    ]
                                }
                            ]
                        },
                        {
                            xtype: 'panel',
                            border: false,
                            layout: 'border',
                            iconCls: 'icon-creditcards',
                            title: 'Pagamenti',
                            items: [
                                {
                                    xtype: 'panel',
                                    region: 'west',
                                    split: true,
                                    id: 'CcpPLeftPnl',
                                    width: 320,
                                    autoScroll: true,
                                    bodyCls: 'bck-content',
                                    collapseDirection: 'left',
                                    collapsible: true,
                                    iconCls: 'icon-find',
                                    title: 'Filtri',
                                    titleCollapse: true,
                                    layout: {
                                        type: 'vbox',
                                        align: 'stretch'
                                    },
                                    items: [
                                        {
                                            xtype: 'form',
                                            getFilter: function() {
                                                var rec = Ext.getCmp('CcpPaymentsFilterForm').getValues(),
                                                    filter = {};

                                                for (var key in rec) {
                                                    if (rec[key] !== '') {
                                                        filter[key] = rec[key];
                                                    }
                                                }

                                                if (filter.account_id == '0') {
                                                    delete(filter.account_id);
                                                }

                                                if (filter['payment_method_id[]'] && filter['payment_method_id[]'].includes(0)) {
                                                    filter['payment_method_id[]'].splice(filter['payment_method_id[]'].indexOf(0),1);
                                                    if(filter['payment_method_id[]'].length===0) {
                                                        delete filter['payment_method_id[]'];
                                                    }
                                                }

                                                if(filter.subject_school_year=='all') {
                                                    delete(filter.subject_school_year);
                                                }


                                                return filter;
                                            },
                                            loadByFilter: function() {
                                                if (mc2ui.app.ccpPaymentsFilterEventTimeoutId > 0) {
                                                    clearTimeout(mc2ui.app.ccpPaymentsFilterEventTimeoutId);
                                                }

                                                mc2ui.app.ccpPaymentsFilterEventTimeoutId = setTimeout(function() {

                                                    Ext.getCmp('CcpPaymentsPositiveSum').setValue(Ext.util.Format.number(0, '0.000,00/i'));
                                                    Ext.getCmp('CcpPaymentsNegativeSum').setValue(Ext.util.Format.number(0, '0.000,00/i'));
                                                    Ext.getCmp('CcpPaymentsBalanceSum').setValue(Ext.util.Format.number(0, '0.000,00/i'));

                                                    Ext.getStore('CcpPayments').currentPage = 1;
                                                    Ext.getStore('CcpPayments').load({
                                                        callback: function(records, operation, success, a, b) {
                                                            var res = Ext.decode(operation.response.responseText);

                                                            Ext.getCmp('CcpPaymentsPositiveSum').setValue(Ext.util.Format.number(res.sum_positives, '0.000,00/i'));
                                                            Ext.getCmp('CcpPaymentsNegativeSum').setValue(Ext.util.Format.number(res.sum_negatives, '0.000,00/i'));
                                                            Ext.getCmp('CcpPaymentsBalanceSum').setValue(Ext.util.Format.number(res.sum_positives - res.sum_negatives, '0.000,00/i'));
                                                        }
                                                    });
                                                }, 500);
                                            },
                                            border: false,
                                            id: 'CcpPaymentsFilterForm',
                                            bodyCls: 'bck-content',
                                            bodyPadding: 10,
                                            layout: {
                                                type: 'vbox',
                                                align: 'stretch'
                                            },
                                            items: [
                                                {
                                                    xtype: 'button',
                                                    handler: function(button, e) {
                                                        var form = Ext.getCmp('CcpPaymentsFilterForm').getForm(),
                                                            pm = Ext.getCmp('CcpPaymentMethod'),
                                                            pd = Ext.getCmp('CcpPaymentDestination'),
                                                            io = Ext.getCmp('CcpIncoming1'),
                                                            os = Ext.getCmp('CcpOperationDateStart'),
                                                            oe = Ext.getCmp('CcpOperationDateEnd'),
                                                            as = Ext.getCmp('CcpAccountableDateStart'),
                                                            ae = Ext.getCmp('CcpAccountableDateEnd'),
                                                            st = Ext.getCmp('CcpPSubjectType'),
                                                            pt = Ext.getCmp('CcpPayerType');

                                                        form.reset();
                                                        //pm.select(0);
                                                        pd.select(0);
                                                        io.select('A');
                                                        os.setValue('01/01/' + new Date().getFullYear());
                                                        oe.setValue('31/12/' + new Date().getFullYear());
                                                        as.setValue();
                                                        ae.setValue();
                                                        st.select('A');
                                                        pt.select('A');

                                                        Ext.getCmp('CcpSchoolYear2').select('all');

                                                        Ext.getCmp('CcpCreditsSelectionCmb').select(Ext.getCmp('CcpCreditsSelectionCmb').getStore().getRange());


                                                    },
                                                    margin: '0 0 10 0',
                                                    iconCls: 'icon-arrow_undo',
                                                    text: 'Reimposta'
                                                },
                                                {
                                                    xtype: 'fieldset',
                                                    padding: 5,
                                                    title: '<b>Filtri per anno scolastico studente</b>',
                                                    layout: {
                                                        type: 'vbox',
                                                        align: 'stretch'
                                                    },
                                                    items: [
                                                        {
                                                            xtype: 'container',
                                                            flex: 1,
                                                            layout: {
                                                                type: 'hbox',
                                                                align: 'bottom'
                                                            },
                                                            items: [
                                                                {
                                                                    xtype: 'combobox',
                                                                    flex: 1,
                                                                    id: 'CcpSchoolYear2',
                                                                    fieldLabel: '<span style="color:#15428b">Anno riferimento studente</span>',
                                                                    labelAlign: 'top',
                                                                    name: 'subject_school_year',
                                                                    emptyText: 'Tutti',
                                                                    editable: false,
                                                                    displayField: 'name',
                                                                    forceSelection: true,
                                                                    queryMode: 'local',
                                                                    store: 'McDbs',
                                                                    valueField: 'name',
                                                                    listeners: {
                                                                        change: {
                                                                            fn: me.onComboboxSelect112,
                                                                            scope: me
                                                                        },
                                                                        afterrender: {
                                                                            fn: me.onCcpSchoolYearAfterRender2,
                                                                            scope: me
                                                                        }
                                                                    }
                                                                },
                                                                {
                                                                    xtype: 'button',
                                                                    handler: function(button, e) {
                                                                        Ext.getCmp('CcpSchoolYear2').setValue();
                                                                        Ext.getCmp('CcpPaymentsFilterForm').loadByFilter();

                                                                    },
                                                                    margin: '0 0 0 5',
                                                                    iconCls: 'icon-cancel'
                                                                }
                                                            ]
                                                        },
                                                        {
                                                            xtype: 'container',
                                                            flex: 1,
                                                            layout: {
                                                                type: 'vbox',
                                                                align: 'stretch'
                                                            },
                                                            items: [
                                                                {
                                                                    xtype: 'container',
                                                                    flex: 1,
                                                                    disabled: true,
                                                                    id: 'CcpPaymentFilterAddressCnt',
                                                                    layout: {
                                                                        type: 'hbox',
                                                                        align: 'bottom'
                                                                    },
                                                                    items: [
                                                                        {
                                                                            xtype: 'combobox',
                                                                            flex: 1,
                                                                            id: 'CcpAddressSectionCmb1',
                                                                            fieldLabel: '<span style="color:#15428b">Indirizzo</span>',
                                                                            labelAlign: 'top',
                                                                            name: 'address_id[]',
                                                                            emptyText: 'Tutti',
                                                                            editable: false,
                                                                            displayField: 'description',
                                                                            forceSelection: true,
                                                                            multiSelect: true,
                                                                            queryMode: 'local',
                                                                            store: 'Indirizzi1',
                                                                            valueField: 'id',
                                                                            listeners: {
                                                                                select: {
                                                                                    fn: me.onCcpAddressSectionCmbSelect1,
                                                                                    scope: me
                                                                                },
                                                                                change: {
                                                                                    fn: me.onCcpAddressSectionCmbChange1,
                                                                                    scope: me
                                                                                }
                                                                            }
                                                                        },
                                                                        {
                                                                            xtype: 'button',
                                                                            handler: function(button, e) {
                                                                                Ext.getCmp('CcpAddressSectionCmb1').setValue();
                                                                                Ext.getCmp('CcpClassSectionCmb1').setValue();
                                                                                Ext.getCmp('CcpPaymentsFilterForm').loadByFilter();

                                                                            },
                                                                            margin: '0 0 0 5',
                                                                            iconCls: 'icon-cancel'
                                                                        }
                                                                    ]
                                                                },
                                                                {
                                                                    xtype: 'container',
                                                                    flex: 1,
                                                                    disabled: true,
                                                                    id: 'CcpPaymentFilterClassCnt',
                                                                    margin: '5 0 0 0',
                                                                    layout: {
                                                                        type: 'hbox',
                                                                        align: 'bottom'
                                                                    },
                                                                    items: [
                                                                        {
                                                                            xtype: 'combobox',
                                                                            flex: 1,
                                                                            id: 'CcpClassSectionCmb1',
                                                                            itemId: 'CcpClassSectionCmb',
                                                                            fieldLabel: '<span style="color:#15428b">Classe</span>',
                                                                            labelAlign: 'top',
                                                                            name: 'class_id[]',
                                                                            emptyText: 'Tutte',
                                                                            editable: false,
                                                                            displayField: 'display',
                                                                            forceSelection: true,
                                                                            multiSelect: true,
                                                                            queryMode: 'local',
                                                                            store: 'Classi1',
                                                                            valueField: 'id',
                                                                            listeners: {
                                                                                select: {
                                                                                    fn: me.onComboboxSelect51,
                                                                                    scope: me
                                                                                }
                                                                            }
                                                                        },
                                                                        {
                                                                            xtype: 'button',
                                                                            handler: function(button, e) {
                                                                                Ext.getCmp('CcpClassSectionCmb').setValue();
                                                                                Ext.getCmp('CcpPaymentsFilterForm').loadByFilter();

                                                                            },
                                                                            margin: '0 0 0 5',
                                                                            iconCls: 'icon-cancel',
                                                                            text: ''
                                                                        }
                                                                    ]
                                                                }
                                                            ]
                                                        }
                                                    ]
                                                },
                                                {
                                                    xtype: 'fieldset',
                                                    flex: 1,
                                                    padding: 5,
                                                    title: '<b>Filtri per anno scolastico movimento</b>',
                                                    layout: {
                                                        type: 'vbox',
                                                        align: 'stretch'
                                                    },
                                                    items: [
                                                        {
                                                            xtype: 'container',
                                                            layout: {
                                                                type: 'hbox',
                                                                align: 'bottom'
                                                            },
                                                            items: [
                                                                {
                                                                    xtype: 'combobox',
                                                                    flex: 1,
                                                                    id: 'CcpSchoolYearMovement2',
                                                                    fieldLabel: '<span style="color:#15428b">Anno scolastico movimento</span>',
                                                                    labelAlign: 'top',
                                                                    name: 'movement_school_year',
                                                                    emptyText: 'Tutti',
                                                                    displayField: 'name',
                                                                    queryMode: 'local',
                                                                    store: 'McDbs',
                                                                    valueField: 'name',
                                                                    listeners: {
                                                                        select: {
                                                                            fn: me.onComboboxSelect,
                                                                            scope: me
                                                                        }
                                                                    }
                                                                },
                                                                {
                                                                    xtype: 'button',
                                                                    handler: function(button, e) {
                                                                        Ext.getCmp('CcpSchoolYearMovement2').setValue();
                                                                        Ext.getCmp('CcpPaymentsFilterForm').loadByFilter();

                                                                    },
                                                                    margin: '0 0 0 5',
                                                                    iconCls: 'icon-cancel',
                                                                    text: ''
                                                                }
                                                            ]
                                                        },
                                                        {
                                                            xtype: 'container',
                                                            flex: 1,
                                                            layout: {
                                                                type: 'hbox',
                                                                align: 'bottom'
                                                            },
                                                            items: [
                                                                {
                                                                    xtype: 'combobox',
                                                                    flex: 1,
                                                                    id: 'CcpPaymentCategoryCmb',
                                                                    fieldLabel: '<span style="color:#15428b">Categoria</span>',
                                                                    labelAlign: 'top',
                                                                    name: 'category_id',
                                                                    emptyText: 'Tutte',
                                                                    displayField: 'name',
                                                                    queryMode: 'local',
                                                                    store: 'CcpCategoriesFilter1',
                                                                    valueField: 'id',
                                                                    listeners: {
                                                                        select: {
                                                                            fn: me.onComboboxSelect7,
                                                                            scope: me
                                                                        }
                                                                    }
                                                                },
                                                                {
                                                                    xtype: 'button',
                                                                    handler: function(button, e) {
                                                                        Ext.getCmp('CcpPaymentCategoryCmb').setValue();
                                                                        Ext.getCmp('CcpPaymentsFilterForm').loadByFilter();

                                                                    },
                                                                    margin: '0 0 0 5',
                                                                    iconCls: 'icon-cancel'
                                                                }
                                                            ]
                                                        },
                                                        {
                                                            xtype: 'container',
                                                            flex: 1,
                                                            layout: {
                                                                type: 'hbox',
                                                                align: 'bottom'
                                                            },
                                                            items: [
                                                                {
                                                                    xtype: 'combobox',
                                                                    tpl: '<ul class="x-list-plain">\n    <tpl for=".">\n        <li role="option" style="font-size:10x;line-height:17px" class="x-boundlist-item">{name} \n        <div style="color:#333;margin:-2px 0px 0px 0px;font-style:italic;font-size:10px">{school_year}</div></li>\n    </tpl>\n</ul>',
                                                                    flex: 1,
                                                                    id: 'CcpPaymentTypeCmb',
                                                                    fieldLabel: '<span style="color:#15428b">Tipi movimento</span>',
                                                                    labelAlign: 'top',
                                                                    name: 'type_id[]',
                                                                    emptyText: 'Tutti',
                                                                    editable: false,
                                                                    displayField: 'name_school_year',
                                                                    forceSelection: true,
                                                                    multiSelect: true,
                                                                    queryMode: 'local',
                                                                    store: 'CcpTypesFilter1',
                                                                    valueField: 'id',
                                                                    listeners: {
                                                                        select: {
                                                                            fn: me.onComboboxSelect1,
                                                                            scope: me
                                                                        }
                                                                    }
                                                                },
                                                                {
                                                                    xtype: 'button',
                                                                    handler: function(button, e) {
                                                                        Ext.getCmp('CcpPaymentTypeCmb').setValue();
                                                                        Ext.getCmp('CcpPaymentsFilterForm').loadByFilter();

                                                                    },
                                                                    margin: '0 0 0 5',
                                                                    iconCls: 'icon-cancel'
                                                                }
                                                            ]
                                                        }
                                                    ]
                                                },
                                                {
                                                    xtype: 'fieldset',
                                                    title: 'Direzione',
                                                    layout: {
                                                        type: 'vbox',
                                                        align: 'stretch'
                                                    },
                                                    items: [
                                                        {
                                                            xtype: 'combobox',
                                                            id: 'CcpIncoming1',
                                                            name: 'incoming',
                                                            allowBlank: false,
                                                            allowOnlyWhitespace: false,
                                                            editable: false,
                                                            forceSelection: true,
                                                            queryMode: 'local',
                                                            store: 'CcpDirectionsFilter',
                                                            valueField: 'id',
                                                            listeners: {
                                                                select: {
                                                                    fn: me.onComboboxSelect31,
                                                                    scope: me
                                                                }
                                                            }
                                                        }
                                                    ]
                                                },
                                                {
                                                    xtype: 'fieldset',
                                                    title: 'Periodo Operazione',
                                                    layout: {
                                                        type: 'vbox',
                                                        align: 'stretch'
                                                    },
                                                    items: [
                                                        {
                                                            xtype: 'datefield',
                                                            endDateField: 'CcpOperationDateEnd',
                                                            id: 'CcpOperationDateStart',
                                                            fieldLabel: 'Dal',
                                                            labelAlign: 'right',
                                                            labelWidth: 50,
                                                            name: 'operation_date_start',
                                                            vtype: 'daterange',
                                                            format: 'd/m/Y',
                                                            startDay: 1,
                                                            submitFormat: 'Y-m-d',
                                                            listeners: {
                                                                change: {
                                                                    fn: me.onDatefieldChange3,
                                                                    delay: 500,
                                                                    buffer: 500,
                                                                    scope: me
                                                                }
                                                            }
                                                        },
                                                        {
                                                            xtype: 'datefield',
                                                            startDateField: 'CcpOperationDateStart',
                                                            id: 'CcpOperationDateEnd',
                                                            fieldLabel: 'al',
                                                            labelAlign: 'right',
                                                            labelWidth: 50,
                                                            name: 'operation_date_end',
                                                            vtype: 'daterange',
                                                            format: 'd/m/Y',
                                                            startDay: 1,
                                                            submitFormat: 'Y-m-d',
                                                            listeners: {
                                                                change: {
                                                                    fn: me.onDatefieldChange12,
                                                                    delay: 500,
                                                                    buffer: 500,
                                                                    scope: me
                                                                }
                                                            }
                                                        }
                                                    ]
                                                },
                                                {
                                                    xtype: 'fieldset',
                                                    title: 'Periodo pagamento',
                                                    layout: {
                                                        type: 'vbox',
                                                        align: 'stretch'
                                                    },
                                                    items: [
                                                        {
                                                            xtype: 'datefield',
                                                            endDateField: 'CcpAccountableDateEnd',
                                                            id: 'CcpAccountableDateStart',
                                                            fieldLabel: 'Dal',
                                                            labelAlign: 'right',
                                                            labelWidth: 50,
                                                            name: 'accountable_date_start',
                                                            vtype: 'daterange',
                                                            format: 'd/m/Y',
                                                            startDay: 1,
                                                            submitFormat: 'Y-m-d',
                                                            listeners: {
                                                                change: {
                                                                    fn: me.onDatefieldChange21,
                                                                    delay: 500,
                                                                    buffer: 500,
                                                                    scope: me
                                                                }
                                                            }
                                                        },
                                                        {
                                                            xtype: 'datefield',
                                                            startDateField: 'CcpAccountableDateStart',
                                                            id: 'CcpAccountableDateEnd',
                                                            fieldLabel: 'al',
                                                            labelAlign: 'right',
                                                            labelWidth: 50,
                                                            name: 'accountable_date_end',
                                                            vtype: 'daterange',
                                                            format: 'd/m/Y',
                                                            startDay: 1,
                                                            submitFormat: 'Y-m-d',
                                                            listeners: {
                                                                change: {
                                                                    fn: me.onDatefieldChange111,
                                                                    delay: 500,
                                                                    buffer: 500,
                                                                    scope: me
                                                                }
                                                            }
                                                        }
                                                    ]
                                                },
                                                {
                                                    xtype: 'fieldset',
                                                    padding: '0 5 5 5',
                                                    title: 'Modalità pagamento',
                                                    layout: {
                                                        type: 'hbox',
                                                        align: 'stretch'
                                                    },
                                                    items: [
                                                        {
                                                            xtype: 'combobox',
                                                            flex: 1,
                                                            id: 'CcpPaymentMethod',
                                                            name: 'payment_method_id[]',
                                                            editable: false,
                                                            displayField: 'name',
                                                            forceSelection: true,
                                                            multiSelect: true,
                                                            queryMode: 'local',
                                                            store: 'CcpPaymentMethodsFilter',
                                                            valueField: 'id',
                                                            listeners: {
                                                                change: {
                                                                    fn: me.onComboboxSelect12,
                                                                    scope: me
                                                                }
                                                            }
                                                        },
                                                        {
                                                            xtype: 'button',
                                                            handler: function(button, e) {
                                                                Ext.getCmp('CcpPaymentMethod').setValue();
                                                                Ext.getCmp('CcpPaymentsFilterForm').loadByFilter();
                                                            },
                                                            margin: '0 0 0 5',
                                                            iconCls: 'icon-cancel'
                                                        }
                                                    ]
                                                },
                                                {
                                                    xtype: 'fieldset',
                                                    title: 'Destinazione pagamento',
                                                    layout: {
                                                        type: 'vbox',
                                                        align: 'stretch'
                                                    },
                                                    items: [
                                                        {
                                                            xtype: 'combobox',
                                                            id: 'CcpPaymentDestination',
                                                            name: 'account_id',
                                                            allowBlank: false,
                                                            allowOnlyWhitespace: false,
                                                            editable: false,
                                                            displayField: 'denomination',
                                                            forceSelection: true,
                                                            queryMode: 'local',
                                                            store: 'CcpPaymentDestinationsFilter',
                                                            valueField: 'id',
                                                            listeners: {
                                                                select: {
                                                                    fn: me.onComboboxSelect21,
                                                                    scope: me
                                                                }
                                                            }
                                                        }
                                                    ]
                                                },
                                                {
                                                    xtype: 'fieldset',
                                                    title: 'Bollettino',
                                                    layout: {
                                                        type: 'vbox',
                                                        align: 'stretch'
                                                    },
                                                    items: [
                                                        {
                                                            xtype: 'textfield',
                                                            id: 'CcpBollettino',
                                                            name: 'bollettino',
                                                            listeners: {
                                                                change: {
                                                                    fn: me.onTextfieldChange13,
                                                                    delay: 500,
                                                                    buffer: 500,
                                                                    scope: me
                                                                }
                                                            }
                                                        }
                                                    ]
                                                },
                                                {
                                                    xtype: 'fieldset',
                                                    title: 'Dettagli',
                                                    layout: {
                                                        type: 'vbox',
                                                        align: 'stretch'
                                                    },
                                                    items: [
                                                        {
                                                            xtype: 'checkboxfield',
                                                            id: 'CcpHasAdditionals1',
                                                            name: 'additionals',
                                                            boxLabel: 'Con addizionali',
                                                            listeners: {
                                                                change: {
                                                                    fn: me.onCcpHasAdditionalsChange1,
                                                                    scope: me
                                                                }
                                                            }
                                                        },
                                                        {
                                                            xtype: 'checkboxfield',
                                                            id: 'CcpHasReceipt',
                                                            name: 'receipt',
                                                            boxLabel: 'Con ricevuta',
                                                            listeners: {
                                                                change: {
                                                                    fn: me.onCcpHasPaymentsChange1,
                                                                    scope: me
                                                                }
                                                            }
                                                        }
                                                    ]
                                                },
                                                {
                                                    xtype: 'fieldset',
                                                    flex: 1,
                                                    title: 'Incassi/Borsellino',
                                                    layout: {
                                                        type: 'vbox',
                                                        align: 'stretch'
                                                    },
                                                    items: [
                                                        {
                                                            xtype: 'checkboxfield',
                                                            fieldLabel: '',
                                                            name: 'only_credit',
                                                            boxLabel: 'Mostra solo crediti filtrati',
                                                            inputValue: '1',
                                                            uncheckedValue: '0',
                                                            listeners: {
                                                                change: {
                                                                    fn: me.onCheckboxfieldChange8,
                                                                    scope: me
                                                                }
                                                            }
                                                        },
                                                        {
                                                            xtype: 'container',
                                                            margin: '0 0 5 0',
                                                            layout: {
                                                                type: 'hbox',
                                                                align: 'stretch'
                                                            },
                                                            items: [
                                                                {
                                                                    xtype: 'combobox',
                                                                    flex: 1,
                                                                    id: 'CcpCreditsSelectionCmb',
                                                                    name: 'credit_id[]',
                                                                    value: 'pagamenti',
                                                                    displayField: 'description',
                                                                    forceSelection: true,
                                                                    multiSelect: true,
                                                                    queryMode: 'local',
                                                                    store: 'CreditsTypePaymentFilter',
                                                                    valueField: 'id',
                                                                    listeners: {
                                                                        change: {
                                                                            fn: me.onComboboxSelect14,
                                                                            scope: me
                                                                        },
                                                                        afterrender: {
                                                                            fn: me.onComboboxAfterRender2,
                                                                            scope: me
                                                                        }
                                                                    }
                                                                },
                                                                {
                                                                    xtype: 'button',
                                                                    handler: function(button, e) {
                                                                        Ext.getCmp('CcpCreditsSelectionCmb').setValue();
                                                                        Ext.getCmp('CcpPaymentsFilterForm').loadByFilter();

                                                                    },
                                                                    margin: '0 0 0 5',
                                                                    iconCls: 'icon-cancel',
                                                                    text: ''
                                                                }
                                                            ]
                                                        }
                                                    ]
                                                },
                                                {
                                                    xtype: 'fieldset',
                                                    title: 'Pagante',
                                                    layout: {
                                                        type: 'vbox',
                                                        align: 'stretch'
                                                    },
                                                    items: [
                                                        {
                                                            xtype: 'combobox',
                                                            id: 'CcpPayerType',
                                                            name: 'payer_type',
                                                            allowBlank: false,
                                                            allowOnlyWhitespace: false,
                                                            editable: false,
                                                            forceSelection: true,
                                                            queryMode: 'local',
                                                            store: 'CcpPayerTypesFilter',
                                                            valueField: 'id',
                                                            listeners: {
                                                                select: {
                                                                    fn: me.onComboboxSelect211,
                                                                    scope: me
                                                                }
                                                            }
                                                        },
                                                        {
                                                            xtype: 'textfield',
                                                            id: 'CcpPayer',
                                                            name: 'payer_data',
                                                            listeners: {
                                                                change: {
                                                                    fn: me.onTextfieldChange1111,
                                                                    delay: 500,
                                                                    buffer: 500,
                                                                    scope: me
                                                                }
                                                            }
                                                        }
                                                    ]
                                                },
                                                {
                                                    xtype: 'fieldset',
                                                    title: 'Debitore / Creditore',
                                                    layout: {
                                                        type: 'vbox',
                                                        align: 'stretch'
                                                    },
                                                    items: [
                                                        {
                                                            xtype: 'combobox',
                                                            id: 'CcpPSubjectType',
                                                            name: 'subject_type',
                                                            allowBlank: false,
                                                            allowOnlyWhitespace: false,
                                                            editable: false,
                                                            forceSelection: true,
                                                            queryMode: 'local',
                                                            store: 'CcpSubjectTypesFilter',
                                                            valueField: 'id',
                                                            listeners: {
                                                                select: {
                                                                    fn: me.onComboboxSelect2111,
                                                                    scope: me
                                                                }
                                                            }
                                                        },
                                                        {
                                                            xtype: 'textfield',
                                                            id: 'CcpSubject1',
                                                            name: 'subject_data',
                                                            listeners: {
                                                                change: {
                                                                    fn: me.onTextfieldChange11111,
                                                                    delay: 500,
                                                                    buffer: 500,
                                                                    scope: me
                                                                }
                                                            }
                                                        }
                                                    ]
                                                },
                                                {
                                                    xtype: 'fieldset',
                                                    flex: 1,
                                                    title: 'Periodo scadenza movimento',
                                                    items: [
                                                        {
                                                            xtype: 'datefield',
                                                            anchor: '100%',
                                                            fieldLabel: 'Da',
                                                            name: 'expiration_date_start',
                                                            format: 'd/m/Y',
                                                            submitFormat: 'Y-m-d',
                                                            listeners: {
                                                                change: {
                                                                    fn: me.onDatefieldChange2,
                                                                    scope: me
                                                                }
                                                            }
                                                        },
                                                        {
                                                            xtype: 'datefield',
                                                            anchor: '100%',
                                                            fieldLabel: 'A',
                                                            name: 'expiration_date_end',
                                                            format: 'd/m/Y',
                                                            submitFormat: 'Y-m-d',
                                                            listeners: {
                                                                change: {
                                                                    fn: me.onDatefieldChange4,
                                                                    scope: me
                                                                }
                                                            }
                                                        }
                                                    ]
                                                }
                                            ]
                                        }
                                    ]
                                },
                                {
                                    xtype: 'panel',
                                    permissible: true,
                                    region: 'center',
                                    id: 'CcpPaymentsPnl',
                                    layout: {
                                        type: 'vbox',
                                        align: 'stretch'
                                    },
                                    dockedItems: [
                                        {
                                            xtype: 'toolbar',
                                            flex: 1,
                                            dock: 'top',
                                            items: [
                                                {
                                                    xtype: 'button',
                                                    text: 'Raggruppamenti',
                                                    menu: {
                                                        xtype: 'menu',
                                                        width: 170,
                                                        items: [
                                                            {
                                                                xtype: 'menuitem',
                                                                handler: function(item, e) {
                                                                    var params = Ext.getCmp('CcpPaymentsFilterForm').getFilter(),
                                                                        sel = Ext.getCmp('CcpPaymentsGrid').getSelectionModel().getSelection(),
                                                                        ids=[],
                                                                        alreadyGrouped=false;


                                                                    Ext.each(sel, function(v){
                                                                        ids.push(v.get('id'));
                                                                    });
                                                                    if(ids.length>0) params.ids = Ext.encode(ids);

                                                                    var res = Ext.Ajax.request({
                                                                        url: '/mc2-api/ccp/payment_grouping',
                                                                        method: 'POST',
                                                                        params: params,
                                                                        async: false,

                                                                    });
                                                                    var r = Ext.decode(res.responseText);
                                                                    if(r.success===true) {
                                                                        var msg = 'Si stanno per raggruppare '+ r.total +' pagamenti.<br />';
                                                                        if(r.grouped>0) {
                                                                            msg += '<b>Di questi pagamenti ' + r.grouped + ' appartengono già ad un altro gruppo e verranno SOVRASCRITTI col nuovo. </b><br />';
                                                                        }
                                                                        msg += 'Confermi l\'operazione?';
                                                                        Ext.Msg.confirm('ATTENZIONE', msg, function (btn) {
                                                                            if(btn=='yes') {
                                                                                params.force=1;
                                                                                Ext.Ajax.request({
                                                                                    url: '/mc2-api/ccp/payment_grouping',
                                                                                    method: 'POST',
                                                                                    params: params,
                                                                                    success: function(r) {
                                                                                        var res = Ext.decode(r.responseText);
                                                                                        if(res.success === true) {
                                                                                            Ext.Msg.alert('INFORMAZIONI', 'Pagamenti raggruppati correttamente');
                                                                                            Ext.getStore('CcpPayments').load();
                                                                                        } else {
                                                                                            Ext.Msg.alert('ERRORE', res.message);
                                                                                        }
                                                                                    }
                                                                                });
                                                                            }
                                                                        });
                                                                    }
                                                                },
                                                                text: 'Raggruppa'
                                                            },
                                                            {
                                                                xtype: 'menuitem',
                                                                handler: function(item, e) {
                                                                    var params = Ext.getCmp('CcpPaymentsFilterForm').getFilter(),
                                                                        sel = Ext.getCmp('CcpPaymentsGrid').getSelectionModel().getSelection(),
                                                                        ids=[],
                                                                        alreadyGrouped=false;


                                                                    Ext.each(sel, function(v){
                                                                        ids.push(v.get('id'));
                                                                    });
                                                                    if(ids.length>0) params.ids = Ext.encode(ids);

                                                                    var res = Ext.Ajax.request({
                                                                        url: '/mc2-api/ccp/payment_grouping',
                                                                        method: 'POST',
                                                                        params: params,
                                                                        async: false,

                                                                    });
                                                                    var r = Ext.decode(res.responseText);
                                                                    if(r.success===true) {
                                                                        var msg = 'Si stanno per cancellare i raggruppamenti di '+ r.total +' pagamenti.<br />';
                                                                        msg += 'Confermi l\'operazione?';
                                                                        Ext.Msg.confirm('ATTENZIONE', msg, function (btn) {
                                                                            if(btn=='yes') {
                                                                                params.remove=1;
                                                                                Ext.Ajax.request({
                                                                                    url: '/mc2-api/ccp/payment_grouping',
                                                                                    method: 'POST',
                                                                                    params: params,
                                                                                    success: function(r) {
                                                                                        var res = Ext.decode(r.responseText);
                                                                                        if(res.success === true) {
                                                                                            Ext.Msg.alert('INFORMAZIONI', 'Raggruppamenti cancellati correttamente');
                                                                                            Ext.getStore('CcpPayments').load();
                                                                                        } else {
                                                                                            Ext.Msg.alert('ERRORE', res.message);
                                                                                        }
                                                                                    }
                                                                                });
                                                                            }
                                                                        });
                                                                    }
                                                                },
                                                                text: 'Elimina raggruppamento'
                                                            }
                                                        ]
                                                    }
                                                },
                                                {
                                                    xtype: 'button',
                                                    handler: function(button, e) {
                                                        Ext.widget('CcpPaymentMassiveEditWin').show();
                                                    },
                                                    text: 'Modifica massiva'
                                                },
                                                {
                                                    xtype: 'button',
                                                    handler: function(button, e) {
                                                        Ext.define('Ext.chart.theme.Residuals', {
                                                            extend: 'Ext.chart.theme.Base',
                                                            constructor: function(config) {
                                                                this.callParent([Ext.apply({
                                                                    colors: ['rgb(180,180,180)','rgb(0,153,51)', 'rgb(153,0,0)']
                                                                }, config)]);
                                                            }
                                                        });

                                                        Ext.widget('CcpResidualsWin').show();

                                                        var s = Ext.getStore('CcpResidualsYears'),
                                                            year = new Date().getFullYear();

                                                        s.add([{id: year - 3, text: '' + (year - 3)},
                                                        {id: year - 2, text: '' + (year - 2)},
                                                        {id: year - 1, text: '' + (year - 1)},
                                                        {id: year, text: 'Corrente'}]);

                                                        Ext.getCmp('CcpResidualsYear').select(year);
                                                    },
                                                    hidden: true,
                                                    id: 'CcpResidualBtn',
                                                    iconCls: 'icon-chart_line',
                                                    text: 'Saldi'
                                                },
                                                {
                                                    xtype: 'tbspacer',
                                                    flex: 1
                                                },
                                                {
                                                    xtype: 'button',
                                                    handler: function(button, e) {

                                                        Ext.widget('ExportEasyWin').show();
                                                        Ext.getCmp('ExportEasyFrm').hide();
                                                        Ext.getCmp('ExportEasyPaymentFrm').show();
                                                    },
                                                    text: 'Esportazione EASY'
                                                },
                                                {
                                                    xtype: 'button',
                                                    handler: function(button, e) {
                                                        var params =  Ext.getCmp('CcpPaymentsFilterForm').getFilter();
                                                        var queryString = Ext.Object.toQueryString(params);
                                                        window.open('/mc2-api/ccp/export_bpoint/payment?' + queryString, '_blank');
                                                    },
                                                    text: 'Esportazione BPoint'
                                                },
                                                {
                                                    xtype: 'button',
                                                    handler: function(button, e) {
                                                        Ext.widget('CcpPrintTypeWin').show();
                                                        Ext.getCmp('CcpPrintTypeType').setValue('P');
                                                        Ext.getCmp('CcpPrintLastPaymentDate').hide();
                                                    },
                                                    iconCls: 'icon-printer',
                                                    text: 'Stampa elenco'
                                                },
                                                {
                                                    xtype: 'button',
                                                    handler: function(button, e) {
                                                        var rec = {},
                                                            gridName;

                                                        rec = Ext.getCmp('CcpPaymentsFilterForm').getForm().getValues();


                                                        gridName = 'CcpPaymentsGrid';


                                                        rec.newSpool = 1;
                                                        rec.namespace = 'CCP';
                                                        rec.type = 'XLS';
                                                        rec.mime = 'application/vnd.ms-excel';
                                                        rec.print = 'ExportPayments';

                                                        if (rec.subject_type === 'O') {
                                                            rec.subject_data = rec.query;
                                                        }

                                                        rec.sort = [];
                                                        Ext.each(Ext.getCmp(gridName).getStore().getSorters(), function(sorter){
                                                            rec.sort = rec.sort.concat({
                                                                "property": sorter.property,
                                                                "direction": sorter.direction
                                                            });
                                                        });
                                                        rec.sort = Ext.encode(rec.sort);

                                                        if(rec.kind == 'R'){
                                                            rec.print = 'Residuals' + rec.print;
                                                        }

                                                        Ext.Ajax.request({
                                                            url: '/mc2-api/core/print',
                                                            params: rec,
                                                            success: function(response, opts) {
                                                                var res = Ext.decode(response.responseText);
                                                                mc2ui.app.showNotifyPrint(res);
                                                            }
                                                        });
                                                    },
                                                    iconCls: 'icon-page_excel',
                                                    text: ''
                                                }
                                            ]
                                        },
                                        {
                                            xtype: 'toolbar',
                                            flex: 1,
                                            dock: 'bottom',
                                            layout: {
                                                type: 'hbox',
                                                pack: 'center'
                                            },
                                            items: [
                                                {
                                                    xtype: 'tbspacer',
                                                    flex: 1
                                                },
                                                {
                                                    xtype: 'container',
                                                    layout: {
                                                        type: 'vbox',
                                                        align: 'stretch'
                                                    },
                                                    items: [
                                                        {
                                                            xtype: 'textfield',
                                                            id: 'CcpPaymentsPositiveSum',
                                                            width: 280,
                                                            fieldLabel: 'Entrate',
                                                            labelAlign: 'right',
                                                            labelStyle: 'font-weight: bold',
                                                            labelWidth: 180,
                                                            fieldStyle: 'text-align:right;color:green',
                                                            readOnly: true
                                                        },
                                                        {
                                                            xtype: 'textfield',
                                                            id: 'CcpPaymentsNegativeSum',
                                                            width: 280,
                                                            fieldLabel: 'Uscite',
                                                            labelAlign: 'right',
                                                            labelStyle: 'font-weight: bold',
                                                            labelWidth: 180,
                                                            fieldStyle: 'text-align:right;color:red',
                                                            readOnly: true
                                                        },
                                                        {
                                                            xtype: 'textfield',
                                                            id: 'CcpPaymentsBalanceSum',
                                                            style: 'text-align:right !important;',
                                                            width: 280,
                                                            fieldLabel: 'Saldo',
                                                            labelAlign: 'right',
                                                            labelStyle: 'font-weight: bold',
                                                            labelWidth: 180,
                                                            readOnly: true,
                                                            listeners: {
                                                                change: {
                                                                    fn: me.onCcpPaymentsBalanceSumChange,
                                                                    scope: me
                                                                }
                                                            }
                                                        }
                                                    ]
                                                }
                                            ]
                                        }
                                    ],
                                    items: [
                                        {
                                            xtype: 'gridpanel',
                                            flex: 1,
                                            border: false,
                                            id: 'CcpPaymentsGrid',
                                            emptyText: 'Nessun pagamento presente.',
                                            enableColumnHide: false,
                                            enableColumnMove: false,
                                            store: 'CcpPayments',
                                            columns: [
                                                {
                                                    xtype: 'actioncolumn',
                                                    width: 20,
                                                    items: [
                                                        {
                                                            getClass: function(v, metadata, r, rowIndex, colIndex, store) {
                                                                if (r.get('receipt_id')) {
                                                                    return 'icon-page';
                                                                }
                                                            },
                                                            getTip: function(v, metadata, r, rowIndex, colIndex, store) {
                                                                if (r.get('receipt_id')) {
                                                                    return 'Ricevuta n.' + r.get('receipt_number') + ' emessa il ' + Ext.Date.format(r.get('receipt_date'), 'd/m/Y H:i');
                                                                }
                                                            }
                                                        }
                                                    ]
                                                },
                                                {
                                                    xtype: 'numbercolumn',
                                                    width: 104,
                                                    align: 'center',
                                                    dataIndex: 'payment_group',
                                                    text: 'Raggruppamento',
                                                    format: '0'
                                                },
                                                {
                                                    xtype: 'datecolumn',
                                                    width: 110,
                                                    align: 'center',
                                                    dataIndex: 'operation_date',
                                                    text: 'Data Operazione',
                                                    format: 'd/m/Y'
                                                },
                                                {
                                                    xtype: 'datecolumn',
                                                    width: 110,
                                                    align: 'center',
                                                    dataIndex: 'accountable_date',
                                                    text: 'Data Pagamento',
                                                    format: 'd/m/Y'
                                                },
                                                {
                                                    xtype: 'gridcolumn',
                                                    width: 144,
                                                    dataIndex: 'type_text',
                                                    text: 'Tipo di movimento'
                                                },
                                                {
                                                    xtype: 'gridcolumn',
                                                    dataIndex: 'payment_method_text',
                                                    text: 'Metodo'
                                                },
                                                {
                                                    xtype: 'gridcolumn',
                                                    width: 120,
                                                    dataIndex: 'account_text',
                                                    text: 'Conto Corrente'
                                                },
                                                {
                                                    xtype: 'gridcolumn',
                                                    dataIndex: 'payer_data',
                                                    text: 'Pagante',
                                                    flex: 1
                                                },
                                                {
                                                    xtype: 'gridcolumn',
                                                    renderer: function(value, metaData, record, rowIndex, colIndex, store, view) {
                                                        if(record.get('subject_class')){
                                                            return value + ' - ' + record.get('subject_class');
                                                        }
                                                        return '';
                                                    },
                                                    dataIndex: 'subject_data',
                                                    text: 'Riferito a',
                                                    flex: 1
                                                },
                                                {
                                                    xtype: 'gridcolumn',
                                                    renderer: function(value, metaData, record, rowIndex, colIndex, store, view) {
                                                        if(record.get('incoming') === true && value>=0) {
                                                            return Ext.util.Format.number(value, '0,000.00');
                                                        }
                                                        return '';
                                                    },
                                                    width: 110,
                                                    align: 'right',
                                                    dataIndex: 'total',
                                                    text: 'Entrate'
                                                },
                                                {
                                                    xtype: 'gridcolumn',
                                                    renderer: function(value, metaData, record, rowIndex, colIndex, store, view) {
                                                        if(record.get('incoming') === false) {
                                                            return Ext.util.Format.number(value, '0,000.00');
                                                        } else {
                                                            if(value<0) {
                                                                value*=-1;
                                                                return Ext.util.Format.number(value, '0,000.00');
                                                            }
                                                        }
                                                        return '';
                                                    },
                                                    width: 110,
                                                    align: 'right',
                                                    dataIndex: 'total',
                                                    text: 'Uscite'
                                                },
                                                {
                                                    xtype: 'actioncolumn',
                                                    width: 40,
                                                    items: [
                                                        {
                                                            handler: function(view, rowIndex, colIndex, item, e, record, row) {
                                                                if (record.get('count_additionals') > 0) {
                                                                    var s = Ext.getStore('CcpLinkedAdditionals');
                                                                    s.removeAll();
                                                                    s.load({
                                                                        params: {
                                                                            type: 'P',
                                                                            item: record.get('id')
                                                                        },
                                                                        callback: function(records, operation, success) {
                                                                            if (success) {
                                                                                Ext.widget('CcpLinkedAdditionalsWin').show();
                                                                                Ext.getCmp('CcpLinkedAdditionalsWin').setTitle('Addizionali abbinate al pagamento');
                                                                            } else {
                                                                                Ext.Msg.alert('Attenzione', 'Caricamento addizionali abbinate al pagamento fallito.');
                                                                            }
                                                                        }
                                                                    });
                                                                }
                                                            },
                                                            getClass: function(v, metadata, r, rowIndex, colIndex, store) {
                                                                if (r.get('count_additionals') > 0) {
                                                                    return 'icon-money_add';
                                                                }
                                                            },
                                                            getTip: function(v, metadata, r, rowIndex, colIndex, store) {
                                                                var additionals = r.get('count_additionals'),
                                                                    a = 'e';

                                                                if (additionals > 0) {
                                                                    if (additionals > 1) {
                                                                        a = 'i';
                                                                    }

                                                                    return 'Comprende ' + additionals + ' addizional' + a;
                                                                }
                                                            }
                                                        },
                                                        {
                                                            handler: function(view, rowIndex, colIndex, item, e, record, row) {
                                                                var msg = '<div style="display: table">' +
                                                                '<div style="display: table-row">' +
                                                                '<div style="display: table-cell"><b>Conto Corrente:&nbsp;</b></div>' +
                                                                '<div style="display: table-cell">' + record.get('account_text') + '</div>' +
                                                                '</div>' +
                                                                '<div style="display: table-row">' +
                                                                '<div style="display: table-cell"><b>Bollettino:&nbsp;</b></div>' +
                                                                '<div style="display: table-cell">' + record.get('bollettino') + '</div>' +
                                                                '</div>' +
                                                                '<div style="display: table-row">' +
                                                                '<div style="display: table-cell"><b>Riferimento C/C:&nbsp;</b></div>' +
                                                                '<div style="display: table-cell">' + record.get('account_reference') + '</div>' +
                                                                '</div>' +
                                                                '<div style="display: table-row">' +
                                                                '<div style="display: table-cell"><b>Nominativo:&nbsp;</b></div>' +
                                                                '<div style="display: table-cell">' + '(' + record.get('payer_type') + ') ' + record.get('payer_name') + ' ' + record.get('payer_surname') + '</div>' +
                                                                '</div>' +
                                                                '<div style="display: table-row">' +
                                                                '<div style="display: table-cell"><b>Codice Fiscale:&nbsp;</b></div>' +
                                                                '<div style="display: table-cell">' + record.get('payer_fiscal_code') + '</div>' +
                                                                '</div>' +
                                                                '<div style="display: table-row">' +
                                                                '<div style="display: table-cell"><b>Indirizzo:&nbsp;</b></div>' +
                                                                '<div style="display: table-cell">' + record.get('payer_address') + '</div>' +
                                                                '</div>' +
                                                                '<div style="display: table-row">' +
                                                                '<div style="display: table-cell"><b>CAP:&nbsp;</b></div>' +
                                                                '<div style="display: table-cell">' + record.get('payer_zip_code') + '</div>' +
                                                                '</div>' +
                                                                '<div style="display: table-row">' +
                                                                '<div style="display: table-cell"><b>Città:&nbsp;</b></div>' +
                                                                '<div style="display: table-cell">' + record.get('payer_city') + ' (' + record.get('payer_province') + ')</div>' +
                                                                '</div>' +
                                                                '</div>';
                                                                Ext.Msg.alert('Dettagli pagamento', msg);
                                                            },
                                                            iconCls: 'icon-information',
                                                            tooltip: 'Dettagli'
                                                        }
                                                    ]
                                                }
                                            ],
                                            listeners: {
                                                itemcontextmenu: {
                                                    fn: me.onCcpPaymentsGridItemContextMenu1,
                                                    scope: me
                                                }
                                            },
                                            dockedItems: [
                                                {
                                                    xtype: 'pagingtoolbar',
                                                    dock: 'bottom',
                                                    displayInfo: true,
                                                    displayMsg: 'Pagamenti {0} - {1} di {2}',
                                                    emptyMsg: 'Nessun pagamento',
                                                    store: 'CcpPayments'
                                                }
                                            ],
                                            selModel: Ext.create('Ext.selection.CheckboxModel', {

                                            })
                                        },
                                        {
                                            xtype: 'menu',
                                            permissible: true,
                                            id: 'CcpPaymentsMn',
                                            items: [
                                                {
                                                    xtype: 'menuitem',
                                                    handler: function(item, e) {
                                                        var r = Ext.getCmp('CcpPaymentsGrid').getSelectionModel().getSelection()[0],
                                                            s = Ext.getStore('CcpLinkedAdditionalsForm'),
                                                            p = Ext.getStore('CcpPayers'),
                                                            a = 'Pagamento';

                                                        s.removeAll();
                                                        s.load({
                                                            params: {
                                                                type: 'P',
                                                                item: r.get('id')
                                                            },
                                                            callback: function(records, operation, success) {
                                                                if (success) {
                                                                    Ext.widget('CcpPaymentEditWin').show();

                                                                    if (!r.get('incoming')) {
                                                                        a = 'Prelievo';
                                                                    }
                                                                    Ext.getCmp('CcpPaymentEditWin').setTitle(a + ' abbinato al Movimento ' + r.get('movement_number'));



                                                                    p.removeAll();
                                                                    if (r.get('subject_type') === 'O') {
                                                                        Ext.getCmp('CcpPaymentForm').getForm().loadRecord(r);
                                                                        Ext.getCmp('CcpPaymentPayer').setDisabled(true);
                                                                    } else {
                                                                        Ext.getCmp('CcpPaymentPayer').setDisabled(false);
                                                                        p.load({
                                                                            params: {
                                                                                type: r.get('subject_type'),
                                                                                id: r.get('subject_id'),
                                                                                all_as_payer:true
                                                                            },
                                                                            callback: function(records, operation, success) {
                                                                                Ext.getCmp('CcpPaymentPayer').select(r.get('payer_type') + '_' + r.get('payer_id'));
                                                                                Ext.getCmp('CcpPaymentForm').getForm().loadRecord(r);
                                                                            }
                                                                        });
                                                                    }

                                                                    Ext.getCmp('CcpPaymentAmount').readOnly = true;
                                                                    //Ext.getCmp('CcpPaymentAmount').setMaxValue(Math.round((m.get('remaining') + r.get('amount'))*100, 2)/100);
                                                                } else {
                                                                    Ext.Msg.alert('Attenzione', 'Caricamento addizionali abbinate al pagamento fallito.');
                                                                }
                                                            }
                                                        });
                                                    },
                                                    id: 'contextCcpPaymentEdit',
                                                    iconCls: 'icon-pencil',
                                                    text: 'Modifica'
                                                },
                                                {
                                                    xtype: 'menuitem',
                                                    handler: function(item, e) {
                                                        var record = Ext.getCmp('CcpPaymentsGrid').getSelectionModel().getSelection()[0];

                                                        /*Ext.Msg.show({
                                                        title: 'Attenzione',
                                                        msg: 'Sei sicuro di voler eliminare questo Pagamento?<br />Sarà necessario emettere nuovamente l\'eventuale ricevuta associata, se legata ad altri pagamenti.',
                                                        buttons: Ext.Msg.YESNO,
                                                        fn: function(r){
                                                        if (r == 'yes') {
                                                        store = Ext.getStore('CcpPayments');
                                                        store.remove(record);
                                                        store.sync({
                                                        callback: function () {
                                                        store.load();
                                                        },
                                                        success: function() {
                                                        Ext.Msg.alert('Successo', 'Pagamento eliminato');
                                                        },
                                                        failure: function() {
                                                        Ext.Msg.alert('Attenzione', 'Pagamento NON eliminato');
                                                        }
                                                        });
                                                        }
                                                        }
                                                        });*/

                                                        Ext.Msg.confirm('ATTENZIONE', 'Sicuro di voler cancellare il pagamento?', function(btn) {
                                                            if (btn=='yes') {
                                                                store = Ext.getStore('CcpPayments');
                                                                store.remove(record);
                                                                store.sync({
                                                                    callback: function () {
                                                                        store.load();
                                                                    },
                                                                    success: function() {
                                                                        Ext.Msg.alert('Successo', 'Pagamento eliminato');
                                                                    },
                                                                    failure: function(batch,rr) {
                                                                        var msg = batch.proxy.getReader().jsonData.message;
                                                                        Ext.Msg.alert('Errore', msg);
                                                                    }
                                                                });
                                                            }
                                                        });

                                                    },
                                                    id: 'contextCcpPaymentDelete',
                                                    iconCls: 'icon-cancel',
                                                    text: 'Elimina'
                                                },
                                                {
                                                    xtype: 'menuseparator'
                                                },
                                                {
                                                    xtype: 'menuitem',
                                                    id: 'contextCcpPaymentPrints',
                                                    iconCls: 'icon-printer',
                                                    text: 'Stampe singole',
                                                    menu: {
                                                        xtype: 'menu',
                                                        id: 'CcpPaymentEditPrintsMn',
                                                        items: [
                                                            {
                                                                xtype: 'menuitem',
                                                                handler: function(item, e) {
                                                                    var r = Ext.getCmp('CcpPaymentsGrid').getSelectionModel().getSelection()[0],
                                                                        id = r.get('id');

                                                                    if (r.get('receipt_id')) {
                                                                        Ext.Ajax.request({
                                                                            url: '/mc2-api/core/print',
                                                                            params: {
                                                                                newSpool: 1,
                                                                                print: mc2ui.app.settings.prints.receipt,
                                                                                namespace: 'CCP',
                                                                                type: 'PDF',
                                                                                mime: 'application/pdf',
                                                                                receipt_id: r.get('receipt_id'),
                                                                                number: r.get('receipt_number'),
                                                                                date: r.get('receipt_date')
                                                                            },
                                                                            success: function(response, opts) {
                                                                                var res = Ext.decode(response.responseText);
                                                                                mc2ui.app.showNotifyPrint(res);
                                                                            }
                                                                        });
                                                                    } else {
                                                                        Ext.widget('CcpReceiptEditWin').show();
                                                                        Ext.getCmp('CcpReceiptEditLinkedPayments').setValue(Ext.encode([id]));
                                                                    }
                                                                },
                                                                id: 'contextCcpPaymentReceipt',
                                                                iconCls: 'icon-page',
                                                                text: 'Ricevuta'
                                                            },
                                                            {
                                                                xtype: 'menuitem',
                                                                handler: function(item, e) {
                                                                    var r = Ext.getCmp('CcpPaymentsGrid').getSelectionModel().getSelection()[0];

                                                                    Ext.Ajax.request({
                                                                        url: '/mc2-api/core/print',
                                                                        params: {
                                                                            newSpool: 1,
                                                                            print: 'Certificate',
                                                                            namespace: 'CCP',
                                                                            type: 'PDF',
                                                                            mime: 'application/pdf',
                                                                            payment_id: r.get('id'),
                                                                            operation_date: r.get('operation_date')
                                                                        },
                                                                        success: function(response, opts) {
                                                                            var res = Ext.decode(response.responseText);
                                                                            mc2ui.app.showNotifyPrint(res);
                                                                        }
                                                                    });
                                                                },
                                                                id: 'contextCcpPaymentCertificate',
                                                                iconCls: 'icon-script',
                                                                text: 'Attestato Contributo volontario'
                                                            }
                                                        ]
                                                    }
                                                }
                                            ]
                                        }
                                    ]
                                }
                            ]
                        },
                        {
                            xtype: 'panel',
                            border: false,
                            layout: 'border',
                            iconCls: 'icon-page',
                            title: 'Ricevute',
                            items: [
                                {
                                    xtype: 'panel',
                                    region: 'west',
                                    split: true,
                                    id: 'CcpRLeftPnl',
                                    width: 225,
                                    autoScroll: true,
                                    bodyCls: 'bck-content',
                                    collapseDirection: 'left',
                                    collapsed: true,
                                    collapsible: true,
                                    iconCls: 'icon-find',
                                    title: 'Filtri',
                                    titleCollapse: true,
                                    layout: {
                                        type: 'vbox',
                                        align: 'stretch'
                                    },
                                    items: [
                                        {
                                            xtype: 'form',
                                            getFilter: function() {
                                                var rec = Ext.getCmp('CcpReceiptsFilterForm').getValues(),
                                                    filter = {};

                                                for (var key in rec) {
                                                    if (rec[key] !== '') {
                                                        filter[key] = rec[key];
                                                    }
                                                }

                                                return filter;
                                            },
                                            loadByFilter: function() {
                                                if (mc2ui.app.ccpReceiptsFilterEventTimeoutId > 0) {
                                                    clearTimeout(mc2ui.app.ccpReceiptsFilterEventTimeoutId);
                                                }

                                                mc2ui.app.ccpReceiptsFilterEventTimeoutId = setTimeout(function() {
                                                    Ext.getStore('CcpReceipts').currentPage = 1;
                                                    Ext.getStore('CcpReceipts').load();
                                                }, 1000);
                                            },
                                            border: false,
                                            id: 'CcpReceiptsFilterForm',
                                            bodyCls: 'bck-content',
                                            bodyPadding: 10,
                                            layout: {
                                                type: 'vbox',
                                                align: 'stretch'
                                            },
                                            items: [
                                                {
                                                    xtype: 'button',
                                                    handler: function(button, e) {
                                                        var form = Ext.getCmp('CcpReceiptsFilterForm').getForm(),
                                                            es = Ext.getCmp('CcpEmissionDateStart'),
                                                            ee = Ext.getCmp('CcpEmissionDateEnd');

                                                        form.reset();
                                                        es.setValue('01/01/' + new Date().getFullYear());
                                                        ee.setValue('31/12/' + new Date().getFullYear());
                                                    },
                                                    margin: '0 0 10 0',
                                                    iconCls: 'icon-arrow_undo',
                                                    text: 'Reimposta'
                                                },
                                                {
                                                    xtype: 'combobox',
                                                    flex: 1,
                                                    name: 'receipt',
                                                    store: [
                                                        [
                                                            true,
                                                            'Ricevute'
                                                        ],
                                                        [
                                                            false,
                                                            'Attestazioni'
                                                        ]
                                                    ],
                                                    listeners: {
                                                        select: {
                                                            fn: me.onComboboxSelect10,
                                                            scope: me
                                                        }
                                                    }
                                                },
                                                {
                                                    xtype: 'fieldset',
                                                    title: 'Periodo Emissione',
                                                    layout: {
                                                        type: 'vbox',
                                                        align: 'stretch'
                                                    },
                                                    items: [
                                                        {
                                                            xtype: 'datefield',
                                                            endDateField: 'CcpEmissionDateEnd',
                                                            id: 'CcpEmissionDateStart',
                                                            fieldLabel: 'Dal',
                                                            labelAlign: 'right',
                                                            labelWidth: 50,
                                                            name: 'date_start',
                                                            vtype: 'daterange',
                                                            format: 'd/m/Y',
                                                            startDay: 1,
                                                            submitFormat: 'c',
                                                            listeners: {
                                                                change: {
                                                                    fn: me.onDatefieldChange31,
                                                                    delay: 500,
                                                                    buffer: 500,
                                                                    scope: me
                                                                }
                                                            }
                                                        },
                                                        {
                                                            xtype: 'datefield',
                                                            startDateField: 'CcpEmissionDateStart',
                                                            id: 'CcpEmissionDateEnd',
                                                            fieldLabel: 'al',
                                                            labelAlign: 'right',
                                                            labelWidth: 50,
                                                            name: 'date_end',
                                                            vtype: 'daterange',
                                                            format: 'd/m/Y',
                                                            startDay: 1,
                                                            submitFormat: 'c',
                                                            listeners: {
                                                                change: {
                                                                    fn: me.onDatefieldChange121,
                                                                    delay: 500,
                                                                    buffer: 500,
                                                                    scope: me
                                                                }
                                                            }
                                                        }
                                                    ]
                                                },
                                                {
                                                    xtype: 'fieldset',
                                                    title: 'Numero',
                                                    layout: {
                                                        type: 'vbox',
                                                        align: 'stretch'
                                                    },
                                                    items: [
                                                        {
                                                            xtype: 'textfield',
                                                            id: 'CcpNumberStart1',
                                                            fieldLabel: 'Dal',
                                                            labelAlign: 'right',
                                                            labelWidth: 50,
                                                            name: 'number_start',
                                                            listeners: {
                                                                change: {
                                                                    fn: me.onTextfieldChange131,
                                                                    delay: 500,
                                                                    buffer: 500,
                                                                    scope: me
                                                                }
                                                            }
                                                        },
                                                        {
                                                            xtype: 'textfield',
                                                            id: 'CcpNumberEnd1',
                                                            fieldLabel: 'al',
                                                            labelAlign: 'right',
                                                            labelWidth: 50,
                                                            name: 'number_end',
                                                            listeners: {
                                                                change: {
                                                                    fn: me.onTextfieldChange1311,
                                                                    delay: 500,
                                                                    buffer: 500,
                                                                    scope: me
                                                                }
                                                            }
                                                        }
                                                    ]
                                                },
                                                {
                                                    xtype: 'fieldset',
                                                    flex: 1,
                                                    title: 'Situazione pubblicazione ricevute',
                                                    items: [
                                                        {
                                                            xtype: 'checkboxfield',
                                                            name: 'published',
                                                            boxLabel: 'Pubblicate',
                                                            checked: true,
                                                            inputValue: '1',
                                                            uncheckedValue: '0',
                                                            listeners: {
                                                                change: {
                                                                    fn: me.onCheckboxfieldChange9,
                                                                    scope: me
                                                                }
                                                            }
                                                        },
                                                        {
                                                            xtype: 'checkboxfield',
                                                            name: 'not_published',
                                                            boxLabel: 'Non pubblicate',
                                                            checked: true,
                                                            inputValue: '1',
                                                            uncheckedValue: '0',
                                                            listeners: {
                                                                change: {
                                                                    fn: me.onCheckboxfieldChange10,
                                                                    scope: me
                                                                }
                                                            }
                                                        }
                                                    ]
                                                },
                                                {
                                                    xtype: 'fieldset',
                                                    flex: 1,
                                                    title: 'Cod. indirizzo',
                                                    layout: {
                                                        type: 'vbox',
                                                        align: 'stretch'
                                                    },
                                                    items: [
                                                        {
                                                            xtype: 'combobox',
                                                            flex: 1,
                                                            name: 'groupment',
                                                            editable: false,
                                                            displayField: 'groupment',
                                                            forceSelection: true,
                                                            queryMode: 'local',
                                                            store: 'CcpReceiptAddressCode',
                                                            valueField: 'groupment',
                                                            listeners: {
                                                                render: {
                                                                    fn: me.onComboboxRender1,
                                                                    scope: me
                                                                },
                                                                change: {
                                                                    fn: me.onComboboxChange3,
                                                                    scope: me
                                                                }
                                                            }
                                                        }
                                                    ]
                                                }
                                            ]
                                        }
                                    ]
                                },
                                {
                                    xtype: 'panel',
                                    permissible: true,
                                    region: 'center',
                                    id: 'CcpReceiptsPnl',
                                    layout: {
                                        type: 'vbox',
                                        align: 'stretch'
                                    },
                                    dockedItems: [
                                        {
                                            xtype: 'toolbar',
                                            flex: 1,
                                            dock: 'top',
                                            items: [
                                                {
                                                    xtype: 'button',
                                                    handler: function(button, e) {
                                                        Ext.widget('CcpReceiptEditMultiWin').show();
                                                        //Ext.getStore('CcpPaymentsUnreceipted').load();
                                                    },
                                                    id: 'CcpReceiptNewBtn',
                                                    iconCls: 'icon-add',
                                                    text: 'Emetti nuova'
                                                },
                                                {
                                                    xtype: 'button',
                                                    handler: function(button, e) {
                                                        var params = Ext.getCmp('CcpReceiptsFilterForm').getFilter(),
                                                            sel = Ext.getCmp('CcpReceiptsGrid').getSelectionModel().getSelection(),
                                                            ids=[];
                                                        params.massive = 1;

                                                        if(sel.length>0) {
                                                            Ext.each(sel, function(v){
                                                                ids.push(v.get('id'));
                                                            });

                                                            params.ids = Ext.encode(ids);
                                                        }

                                                        Ext.Ajax.request({
                                                            url: '/mc2-api/ccp/receipt/publish',
                                                            params: params,
                                                            success: function(r) {
                                                                var res = Ext.decode(r.responseText);
                                                                if(res.success === true) {
                                                                    Ext.Msg.alert('INFORMAZIONI', 'Ricevute in pubblicazione');
                                                                } else {
                                                                    Ext.Msg.alert('ERRORE', res.message);
                                                                }
                                                            }
                                                        });
                                                    },
                                                    iconCls: 'icon-world',
                                                    text: 'Pubblica ricevute filtrate/selezionate'
                                                },
                                                {
                                                    xtype: 'tbspacer',
                                                    flex: 1
                                                },
                                                {
                                                    xtype: 'button',
                                                    handler: function(button, e) {
                                                        Ext.widget('CcpPrintTypeWin').show();
                                                        Ext.getCmp('CcpPrintTypeType').setValue('R');

                                                    },
                                                    iconCls: 'icon-printer',
                                                    text: 'Stampa elenco'
                                                },
                                                {
                                                    xtype: 'button',
                                                    handler: function(button, e) {
                                                        var rec = Ext.getCmp('CcpReceiptsFilterForm').getFilter();

                                                        rec.newSpool = 1;
                                                        rec.namespace = 'CCP';
                                                        rec.type = 'ZIP';
                                                        rec.mime = 'application/zip';
                                                        rec.print = 'ReceiptsExport';

                                                        Ext.Ajax.request({
                                                            url: '/mc2-api/core/print',
                                                            params: rec,
                                                            success: function(response, opts) {
                                                                var res = Ext.decode(response.responseText);
                                                                mc2ui.app.showNotifyPrint(res);
                                                            }
                                                        });
                                                    },
                                                    text: 'Esporta ricevute filtrate'
                                                }
                                            ]
                                        }
                                    ],
                                    items: [
                                        {
                                            xtype: 'gridpanel',
                                            flex: 1,
                                            border: false,
                                            id: 'CcpReceiptsGrid',
                                            emptyText: 'Nessuna ricevuta presente.',
                                            enableColumnHide: false,
                                            enableColumnMove: false,
                                            enableColumnResize: false,
                                            store: 'CcpReceipts',
                                            columns: [
                                                {
                                                    xtype: 'actioncolumn',
                                                    width: 26,
                                                    items: [
                                                        {
                                                            getClass: function(v, metadata, r, rowIndex, colIndex, store) {
                                                                if(r.get('publication_path')){
                                                                    return 'icon-world';
                                                                }
                                                            }
                                                        }
                                                    ]
                                                },
                                                {
                                                    xtype: 'gridcolumn',
                                                    align: 'right',
                                                    dataIndex: 'number',
                                                    text: 'Numero',
                                                    renderer: function(value, metaData, record, rowIndex, colIndex, store, view) {
                                                        var result = value;
                                                        if(record.get('suffix')) result += '/' + record.get('suffix');
                                                        return result;
                                                    },
                                                },
                                                {
                                                    xtype: 'datecolumn',
                                                    align: 'center',
                                                    dataIndex: 'date',
                                                    text: 'Data Emissione',
                                                    format: 'd/m/Y'
                                                },
                                                {
                                                    xtype: 'gridcolumn',
                                                    dataIndex: 'students',
                                                    text: 'Studente',
                                                    flex: 1
                                                },
                                                {
                                                    xtype: 'gridcolumn',
                                                    dataIndex: 'payers',
                                                    text: 'Pagante',
                                                    flex: 1
                                                },
                                                {
                                                    xtype: 'numbercolumn',
                                                    align: 'right',
                                                    dataIndex: 'total',
                                                    text: 'Importo',
                                                    flex: 1
                                                },
                                                {
                                                    xtype: 'gridcolumn',
                                                    renderer: function(value, metaData, record, rowIndex, colIndex, store, view) {
                                                        return value === true ? 'Ricevuta' : 'Attestazione';
                                                    },
                                                    align: 'center',
                                                    dataIndex: 'receipt',
                                                    text: 'Tipo'
                                                },
                                                {
                                                    xtype: 'gridcolumn',
                                                    width: 100,
                                                    align: 'center',
                                                    dataIndex: 'groupment',
                                                    text: 'Cod. indirizzo'
                                                },
                                                {
                                                    xtype: 'actioncolumn',
                                                    width: 20,
                                                    items: [
                                                        {
                                                            handler: function(view, rowIndex, colIndex, item, e, record, row) {
                                                                var s = Ext.getStore('CcpLinkedPayments');

                                                                if (record.get('count_payments') > 0) {
                                                                    s.removeAll();
                                                                    s.load({
                                                                        params: {
                                                                            linked: 'R',
                                                                            item: record.get('id')
                                                                        },
                                                                        callback: function(records, operation, success) {
                                                                            if (success) {
                                                                                Ext.widget('CcpLinkedPaymentsWin').show();
                                                                                Ext.getCmp('CcpLinkedPaymentsWin').linked = 'R';
                                                                                Ext.getCmp('CcpLinkedPaymentsWin').readOnly = true;
                                                                                Ext.getCmp('CcpLinkedPaymentsWin').setTitle('Pagamenti contenuti nella ricevuta ' + record.get('number'));
                                                                            } else {
                                                                                Ext.Msg.alert('Attenzione', 'Caricamento pagamenti contenuti nella ricevuta fallito.');
                                                                            }
                                                                        }
                                                                    });
                                                                }
                                                            },
                                                            getClass: function(v, metadata, r, rowIndex, colIndex, store) {
                                                                if (r.get('count_payments') > 0) {
                                                                    return 'icon-creditcards';
                                                                }
                                                            },
                                                            getTip: function(v, metadata, r, rowIndex, colIndex, store) {
                                                                var payments = r.get('count_payments'),
                                                                    a = 'i';

                                                                if (payments > 0) {
                                                                    if (payments === 1) {
                                                                        a = 'o';
                                                                    }

                                                                    return 'Contiene ' + payments + ' pagament' + a;
                                                                }
                                                            }
                                                        }
                                                    ]
                                                }
                                            ],
                                            listeners: {
                                                itemcontextmenu: {
                                                    fn: me.onCcpReceiptsGridItemContextMenu,
                                                    scope: me
                                                }
                                            },
                                            dockedItems: [
                                                {
                                                    xtype: 'pagingtoolbar',
                                                    dock: 'bottom',
                                                    width: 360,
                                                    displayInfo: true,
                                                    displayMsg: 'Ricevute {0} - {1} di {2}',
                                                    emptyMsg: 'Nessuna ricevuta',
                                                    store: 'CcpReceipts'
                                                }
                                            ],
                                            selModel: Ext.create('Ext.selection.CheckboxModel', {

                                            })
                                        },
                                        {
                                            xtype: 'menu',
                                            permissible: true,
                                            id: 'CcpReceiptsMn',
                                            items: [
                                                {
                                                    xtype: 'menuitem',
                                                    handler: function(item, e) {
                                                        var r = Ext.getCmp('CcpReceiptsGrid').getSelectionModel().getSelection()[0],
                                                            print = r.get('receipt') === true ? mc2ui.app.settings.prints.receipt : mc2ui.app.settings.prints.attestation;

                                                        Ext.Ajax.request({
                                                            url: '/mc2-api/core/print',
                                                            params: {
                                                                newSpool: 1,
                                                                print: print,
                                                                namespace: 'CCP',
                                                                type: 'PDF',
                                                                mime: 'application/pdf',
                                                                receipt_id: r.get('id'),
                                                                number: r.get('number'),
                                                                date: r.get('date')
                                                            },
                                                            success: function(response, opts) {
                                                                var res = Ext.decode(response.responseText);
                                                                mc2ui.app.showNotifyPrint(res);
                                                            }
                                                        });
                                                    },
                                                    id: 'contextCcpReceiptPrint',
                                                    iconCls: 'icon-printer',
                                                    text: 'Stampa'
                                                },
                                                {
                                                    xtype: 'menuseparator'
                                                },
                                                {
                                                    xtype: 'menuitem',
                                                    handler: function(item, e) {
                                                        var record = Ext.getCmp('CcpReceiptsGrid').getSelectionModel().getSelection()[0];

                                                        Ext.Msg.show({
                                                            title: 'Attenzione',
                                                            msg: 'Sei sicuro di voler eliminare questa Ricevuta?<br />I pagamenti associati torneranno nuovamente disponibili per l\'emissione di una nuova ricevuta.',
                                                            buttons: Ext.Msg.YESNO,
                                                            fn: function(r){
                                                                if (r == 'yes') {
                                                                    store = Ext.getStore('CcpReceipts');
                                                                    store.remove(record);
                                                                    store.sync({
                                                                        callback: function () {
                                                                            store.load();
                                                                            Ext.getStore('CcpPayments').load();
                                                                        },
                                                                        success: function() {
                                                                            Ext.Msg.alert('Successo', 'Ricevuta eliminata');
                                                                        },
                                                                        failure: function() {
                                                                            Ext.Msg.alert('Attenzione', 'Ricevuta NON eliminata');
                                                                        }
                                                                    });
                                                                }
                                                            }
                                                        });
                                                    },
                                                    id: 'contextCcpReceiptDelete',
                                                    iconCls: 'icon-cancel',
                                                    text: 'Elimina'
                                                },
                                                {
                                                    xtype: 'menuitem',
                                                    handler: function(item, e) {
                                                        var id = Ext.getCmp('CcpReceiptsGrid').getSelectionModel().getSelection()[0].get('id');


                                                        Ext.Ajax.request({
                                                            url: '/mc2-api/ccp/receipt/publish',
                                                            params: {
                                                                receipt_id: id
                                                            },
                                                            success: function(r) {
                                                                var res = Ext.decode(r.responseText);
                                                                if(res.success === true) {
                                                                    Ext.Msg.alert('INFORMAZIONI', 'Ricevuta pubblicata correttamente');
                                                                    Ext.getStore('CcpReceipts').load();
                                                                } else {
                                                                    Ext.Msg.alert('ERRORE', res.message);
                                                                }
                                                            }
                                                        });
                                                    },
                                                    id: 'CcpReceiptPublishMn',
                                                    iconCls: 'icon-world',
                                                    text: 'Pubblica'
                                                },
                                                {
                                                    xtype: 'menuitem',
                                                    handler: function(item, e) {
                                                        var id = Ext.getCmp('CcpReceiptsGrid').getSelectionModel().getSelection()[0].get('id');


                                                        Ext.Ajax.request({
                                                            url: '/mc2-api/ccp/receipt/unpublish',
                                                            params: {
                                                                receipt_id: id
                                                            },
                                                            success: function(r) {
                                                                var res = Ext.decode(r.responseText);
                                                                if(res.success === true) {
                                                                    Ext.Msg.alert('INFORMAZIONI', 'Ricevuta non più pubblicata');
                                                                    Ext.getStore('CcpReceipts').load();
                                                                } else {
                                                                    Ext.Msg.alert('ERRORE', res.message);
                                                                }
                                                            }
                                                        });
                                                    },
                                                    id: 'CcpReceiptUnpublishMn',
                                                    iconCls: 'icon-world_delete',
                                                    text: 'Rimuovi pubblicazione'
                                                }
                                            ],
                                            listeners: {
                                                show: {
                                                    fn: me.onCcpReceiptsMnShow,
                                                    scope: me
                                                }
                                            }
                                        }
                                    ]
                                }
                            ]
                        },
                        {
                            xtype: 'mypanel29',
                            tabConfig: {
                                xtype: 'tab',
                                listeners: {
                                    afterrender: {
                                        fn: me.onPanelAfterRender,
                                        scope: me
                                    }
                                }
                            }
                        },
                        {
                            xtype: 'panel',
                            layout: 'fit',
                            title: 'Saldi',
                            hidden: true,
                            items: [
                                {
                                    xtype: 'gridpanel',
                                    title: '',
                                    store: 'StudentBalances',
                                    columns: [
                                        {
                                            xtype: 'gridcolumn',
                                            dataIndex: 'subject_data',
                                            text: 'Nome studente',
                                            flex: 1
                                        },
                                        {
                                            xtype: 'numbercolumn',
                                            align: 'right',
                                            dataIndex: 'credit',
                                            text: 'Crediti'
                                        },
                                        {
                                            xtype: 'numbercolumn',
                                            align: 'right',
                                            dataIndex: 'debit',
                                            text: 'Debiti'
                                        },
                                        {
                                            xtype: 'numbercolumn',
                                            align: 'right',
                                            dataIndex: 'credit_payments',
                                            text: 'Crediti pagati'
                                        },
                                        {
                                            xtype: 'numbercolumn',
                                            align: 'right',
                                            dataIndex: 'debit_payments',
                                            text: 'Debiti pagati'
                                        },
                                        {
                                            xtype: 'numbercolumn',
                                            align: 'right',
                                            dataIndex: 'credit_to_pay',
                                            text: 'Crediti da pagare'
                                        },
                                        {
                                            xtype: 'numbercolumn',
                                            align: 'right',
                                            dataIndex: 'debit_to_pay',
                                            text: 'Debiti da pagare'
                                        },
                                        {
                                            xtype: 'numbercolumn',
                                            dataIndex: 'credit_invoice',
                                            text: 'Crediti fatturati'
                                        },
                                        {
                                            xtype: 'numbercolumn',
                                            dataIndex: 'balance_invoice',
                                            text: 'Saldo fatturati'
                                        },
                                        {
                                            xtype: 'numbercolumn',
                                            align: 'right',
                                            dataIndex: 'balance',
                                            text: 'Saldo'
                                        }
                                    ],
                                    dockedItems: [
                                        {
                                            xtype: 'toolbar',
                                            dock: 'top',
                                            items: [
                                                {
                                                    xtype: 'textfield',
                                                    id: 'CcpSaldiSearchTxt',
                                                    fieldLabel: 'Nome studente',
                                                    emptyText: 'Cerca ...',
                                                    listeners: {
                                                        change: {
                                                            fn: me.onTextfieldChange4,
                                                            delay: 500,
                                                            buffer: 500,
                                                            scope: me
                                                        }
                                                    }
                                                },
                                                {
                                                    xtype: 'tbspacer',
                                                    flex: 1
                                                },
                                                {
                                                    xtype: 'button',
                                                    handler: function(button, e) {
                                                        Ext.widget('CcpExportResidualsWin').show();
                                                    },
                                                    iconCls: 'icon-page_excel',
                                                    text: ''
                                                }
                                            ]
                                        },
                                        {
                                            xtype: 'pagingtoolbar',
                                            dock: 'bottom',
                                            width: 360,
                                            displayInfo: true,
                                            store: 'StudentBalances'
                                        }
                                    ]
                                }
                            ],
                            listeners: {
                                render: {
                                    fn: me.onPanelRender,
                                    scope: me
                                }
                            }
                        },
                        {
                            xtype: 'panel',
                            layout: 'fit',
                            title: 'Solleciti',
                            items: [
                                {
                                    xtype: 'gridpanel',
                                    id: 'CcpReminderGrd',
                                    title: '',
                                    store: 'CcpReminders',
                                    columns: [
                                        {
                                            xtype: 'datecolumn',
                                            width: 120,
                                            dataIndex: 'creation',
                                            text: 'Creazione',
                                            format: 'd/m/Y H:i'
                                        },
                                        {
                                            xtype: 'gridcolumn',
                                            renderer: function(value, metaData, record, rowIndex, colIndex, store, view) {
                                                if(value === 'WARNING') return 'SOLLECITO';
                                                if(value === 'WARNING_2') return 'SECONDO SOLLECITO';
                                                if(value === 'WARNING_3') return 'TERZO SOLLECITO';
                                                if(value === 'WARNING_3') return 'TERZO SOLLECITO';
                                                if(value === 'WARNING_CUSTOMIZED') return 'SOLLECITO PERSONALIZZATO';
                                                if(value === 'INFO') return 'PRO MEMORIA';
                                                if(value === 'INFO_CUSTOMIZED') return 'PRO MEMORIA PERSONALIZZATO';
                                            },
                                            dataIndex: 'reminder_type',
                                            text: 'Tipologia',
                                            flex: 1
                                        },
                                        {
                                            xtype: 'numbercolumn',
                                            width: 112,
                                            dataIndex: 'total_count',
                                            text: 'Parenti selezionati',
                                            format: '0'
                                        },
                                        {
                                            xtype: 'numbercolumn',
                                            dataIndex: 'sent_count',
                                            text: 'Inviati',
                                            format: '0'
                                        },
                                        {
                                            xtype: 'numbercolumn',
                                            dataIndex: 'error_count',
                                            text: 'Errori',
                                            format: '0'
                                        },
                                        {
                                            xtype: 'actioncolumn',
                                            width: 67,
                                            align: 'center',
                                            dataIndex: 'confirmed',
                                            text: 'Conferma',
                                            items: [
                                                {
                                                    getClass: function(v, metadata, r, rowIndex, colIndex, store) {
                                                        if(v === null) {
                                                            return 'icon-email_go';
                                                        } else {
                                                            return 'icon-accept';
                                                        }
                                                    },
                                                    handler: function(view, rowIndex, colIndex, item, e, record, row) {
                                                        Ext.getCmp('CcpReminderGrd').setLoading(true);
                                                        Ext.Ajax.request({
                                                            url: '/mc2-api/ccp/reminder_confirmed',
                                                            method: 'POST',
                                                            params:  {
                                                                creation: record.get('creation').toISOString(),
                                                                reminder_type: record.get('reminder_type')
                                                            },
                                                            success: function(r) {
                                                                var res = Ext.decode(r.responseText);
                                                                if(res.success===false) {
                                                                    Ext.Msg.alert('ERRORE', 'Le seguenti mail risultano errate o mancanti: <br />' + (typeof(res.message) === 'object' ?  res.message.join('<br />') : res.message));
                                                                }
                                                                Ext.getCmp('CcpReminderGrd').setLoading(false);
                                                                Ext.getStore('CcpReminders').load();
                                                            },
                                                            failure: function() {
                                                                Ext.getCmp('CcpReminderGrd').setLoading(false);
                                                                Ext.Msg.alert('ERRORE', 'Errore durante la conferma dell\'invio');
                                                            }
                                                        });
                                                    }
                                                },
                                                {
                                                    handler: function(view, rowIndex, colIndex, item, e, record, row) {
                                                        Ext.Msg.confirm('ATTENZIONE', 'Verranno cancellati tutti i promemoria/solleciti non ancora inviati. Continuare?', function(btn) {
                                                            if(btn=='yes') {
                                                                Ext.Ajax.request({
                                                                    url: '/mc2-api/ccp/reminder/1?creation=' + record.get('creation').toISOString()+'&reminder_type=' + record.get('reminder_type'),
                                                                    method: 'DELETE',
                                                                    success: function() {
                                                                        Ext.getStore('CcpReminders').load();
                                                                    }
                                                                });
                                                            }
                                                        });
                                                    },
                                                    iconCls: 'icon-delete'
                                                }
                                            ]
                                        },
                                        {
                                            xtype: 'actioncolumn',
                                            width: 70,
                                            align: 'center',
                                            text: 'Stampa',
                                            items: [
                                                {
                                                    handler: function(view, rowIndex, colIndex, item, e, record, row) {

                                                        Ext.Ajax.request({
                                                            url: '/mc2-api/core/print',
                                                            params: {
                                                                newSpool: 1,
                                                                print: 'Reminder',
                                                                namespace: 'CCP',
                                                                type: 'PDF',
                                                                mime: 'application/pdf',
                                                                creation: record.get('creation'),
                                                                reminder_type: record.get('reminder_type')
                                                            },
                                                            success: function(response, opts) {
                                                                var res = Ext.decode(response.responseText);
                                                                mc2ui.app.showNotifyPrint(res);
                                                            }
                                                        });
                                                    },
                                                    iconCls: 'icon-printer'
                                                }
                                            ]
                                        }
                                    ],
                                    viewConfig: {
                                        listeners: {
                                            itemdblclick: {
                                                fn: me.onViewItemDblClick1,
                                                scope: me
                                            }
                                        }
                                    },
                                    dockedItems: [
                                        {
                                            xtype: 'pagingtoolbar',
                                            dock: 'bottom',
                                            width: 360,
                                            displayInfo: true,
                                            store: 'CcpReminders'
                                        }
                                    ]
                                }
                            ],
                            listeners: {
                                show: {
                                    fn: me.onPanelShow1,
                                    scope: me
                                }
                            }
                        },
                        {
                            xtype: 'panel',
                            getPrintWizard: function() {
                                return Ext.decode('{"credits":{"anno_scolastico":true,"date":true,"tipo_stampa":["PDF"],"raggruppamento":["group_by_student"],"parenti":false,"studenti":true,"colonne":["student_name","student_surname","student_class","student_residence","payer_name","payer_surname","payer_residence","payer_iban"],"stampa_elementi_vuoti":true},"family_balance":{"anno_scolastico":true,"date":true,"tipo_stampa":["PDF","XLS"],"raggruppamento":["group_by_payer"],"parenti":true,"studenti":false,"colonne":[]},"deposit_list":{"anno_scolastico":true,"date":true,"tipo_stampa":["PDF","XLS"],"raggruppamento":["group_by_none"],"parenti":true,"studenti":false,"colonne":[]},"debtor_list":{"anno_scolastico":true,"date":true,"tipo_stampa":["PDF","XLS"],"raggruppamento":["group_by_payer"],"parenti":true,"studenti":false,"colonne":[]},"estimate_list":{"anno_scolastico":true,"date":true,"tipo_stampa":["PDF","XLS"],"raggruppamento":["group_by_payer"],"parenti":true,"studenti":false,"colonne":[]},"school_year_registration":{"anno_scolastico":true,"date":true,"tipo_stampa":["PDF"],"raggruppamento":["group_by_payer"],"parenti":true,"studenti":false,"colonne":[]}}');
                            },
                            id: 'CcpPrintWizardPnl',
                            autoScroll: true,
                            title: 'Stampe',
                            layout: {
                                type: 'hbox',
                                align: 'stretch'
                            },
                            tabConfig: {
                                xtype: 'tab',
                                listeners: {
                                    afterrender: {
                                        fn: me.onTabAfterRender,
                                        scope: me
                                    }
                                }
                            },
                            items: [
                                {
                                    xtype: 'form',
                                    flex: 1,
                                    id: 'CcpReportFrm',
                                    autoScroll: true,
                                    bodyPadding: 10,
                                    title: '',
                                    layout: {
                                        type: 'hbox',
                                        align: 'stretch'
                                    },
                                    items: [
                                        {
                                            xtype: 'container',
                                            flex: 1,
                                            padding: '0 2',
                                            layout: {
                                                type: 'vbox',
                                                align: 'stretch'
                                            },
                                            items: [
                                                {
                                                    xtype: 'combobox',
                                                    id: 'CcpReportFormatCmb',
                                                    fieldLabel: 'Formato',
                                                    value: 'PDF',
                                                    queryMode: 'local',
                                                    store: [
                                                        'PDF',
                                                        'XLS',
                                                        'DOCX'
                                                    ]
                                                },
                                                {
                                                    xtype: 'gridpanel',
                                                    flex: 1,
                                                    id: 'CcpReportStructureGrd',
                                                    title: 'Tipo di stampa',
                                                    hideHeaders: true,
                                                    store: 'CcpPrintWhat',
                                                    columns: [
                                                        {
                                                            xtype: 'gridcolumn',
                                                            dataIndex: 'text',
                                                            text: 'Descrizione',
                                                            flex: 1
                                                        }
                                                    ],
                                                    selModel: Ext.create('Ext.selection.CheckboxModel', {
                                                        mode: 'SINGLE',
                                                        listeners: {
                                                            select: {
                                                                fn: me.onCheckboxModelSelect,
                                                                scope: me
                                                            }
                                                        }
                                                    })
                                                },
                                                {
                                                    xtype: 'combobox',
                                                    disabled: true,
                                                    hidden: true,
                                                    id: 'CcpReportCategoryCmb',
                                                    padding: 5,
                                                    fieldLabel: '',
                                                    name: 'print_category',
                                                    value: 'group_by_student',
                                                    allowBlank: false,
                                                    emptyText: 'Selezionare tipo di stampa',
                                                    store: 'CcpPrintCategories',
                                                    valueField: 'id'
                                                },
                                                {
                                                    xtype: 'gridpanel',
                                                    flex: 2,
                                                    disabled: true,
                                                    hidden: true,
                                                    id: 'CcpReportColumnsGrd',
                                                    padding: 5,
                                                    title: 'Colonne',
                                                    hideHeaders: true,
                                                    store: 'CcpPrintStudentCols',
                                                    columns: [
                                                        {
                                                            xtype: 'gridcolumn',
                                                            dataIndex: 'text',
                                                            text: 'String',
                                                            flex: 1
                                                        }
                                                    ],
                                                    selModel: Ext.create('Ext.selection.CheckboxModel', {

                                                    })
                                                },
                                                {
                                                    xtype: 'gridpanel',
                                                    flex: 1,
                                                    id: 'CcpPrintMcModelGrd',
                                                    title: 'Modello',
                                                    hideHeaders: true,
                                                    store: 'CcpMcPrintTemplate',
                                                    columns: [
                                                        {
                                                            xtype: 'gridcolumn',
                                                            dataIndex: 'nome_file',
                                                            text: 'String',
                                                            flex: 1
                                                        }
                                                    ],
                                                    selModel: Ext.create('Ext.selection.CheckboxModel', {
                                                        mode: 'SINGLE'
                                                    })
                                                }
                                            ]
                                        },
                                        {
                                            xtype: 'container',
                                            flex: 2,
                                            layout: {
                                                type: 'vbox',
                                                align: 'stretch'
                                            },
                                            items: [
                                                {
                                                    xtype: 'container',
                                                    layout: {
                                                        type: 'vbox',
                                                        align: 'stretch'
                                                    },
                                                    items: [
                                                        {
                                                            xtype: 'combobox',
                                                            id: 'CcpPrintWizardSchoolYear',
                                                            fieldLabel: 'Anno scolastico',
                                                            name: 'subject_school_year',
                                                            emptyText: 'Anno scolastico attuale ...',
                                                            displayField: 'name',
                                                            forceSelection: true,
                                                            queryMode: 'local',
                                                            store: 'McDbs',
                                                            valueField: 'name',
                                                            listeners: {
                                                                afterrender: {
                                                                    fn: me.onComboboxAfterRender,
                                                                    scope: me
                                                                },
                                                                select: {
                                                                    fn: me.onComboboxSelect13,
                                                                    scope: me
                                                                }
                                                            }
                                                        },
                                                        {
                                                            xtype: 'combobox',
                                                            id: 'CcpReportStudentStatusCmb',
                                                            fieldLabel: 'Stato studente',
                                                            name: 'student_status',
                                                            displayField: 'descrizione',
                                                            queryMode: 'local',
                                                            store: 'StudentStates',
                                                            valueField: 'id_stato_studente_personalizzato',
                                                            listeners: {
                                                                afterrender: {
                                                                    fn: me.onComboboxAfterRender1,
                                                                    scope: me
                                                                },
                                                                select: {
                                                                    fn: me.onComboboxSelect15,
                                                                    scope: me
                                                                }
                                                            }
                                                        }
                                                    ]
                                                },
                                                {
                                                    xtype: 'container',
                                                    flex: 1,
                                                    layout: {
                                                        type: 'hbox',
                                                        align: 'stretch'
                                                    },
                                                    items: [
                                                        {
                                                            xtype: 'container',
                                                            flex: 1,
                                                            padding: '0 2',
                                                            layout: {
                                                                type: 'vbox',
                                                                align: 'stretch'
                                                            },
                                                            items: [
                                                                {
                                                                    xtype: 'treepanel',
                                                                    flex: 1,
                                                                    id: 'CcpPrintStudentTree',
                                                                    title: 'Albero studenti',
                                                                    titleAlign: 'center',
                                                                    enableColumnHide: false,
                                                                    enableColumnMove: false,
                                                                    enableColumnResize: false,
                                                                    store: 'CcpStudentsTree',
                                                                    lines: false,
                                                                    useArrows: true,
                                                                    viewConfig: {

                                                                    },
                                                                    columns: [
                                                                        {
                                                                            xtype: 'treecolumn',
                                                                            dataIndex: 'text',
                                                                            text: 'Denominazione',
                                                                            flex: 1
                                                                        }
                                                                    ],
                                                                    listeners: {
                                                                        checkchange: {
                                                                            fn: me.onTreepanelCheckChange,
                                                                            scope: me
                                                                        }
                                                                    }
                                                                }
                                                            ]
                                                        },
                                                        {
                                                            xtype: 'container',
                                                            flex: 1,
                                                            id: 'CcpReportSelectParentsStudentsCnt',
                                                            padding: '0 2',
                                                            layout: {
                                                                type: 'vbox',
                                                                align: 'stretch'
                                                            },
                                                            items: [
                                                                {
                                                                    xtype: 'toolbar',
                                                                    items: [
                                                                        {
                                                                            xtype: 'tbspacer',
                                                                            flex: 1
                                                                        },
                                                                        {
                                                                            xtype: 'button',
                                                                            handler: function(button, e) {
                                                                                Ext.getCmp('CcpReporStudentGrd').show();
                                                                                Ext.getCmp('CcpReporParentGrd').hide();
                                                                                Ext.getStore('CcpReportStudentsParents').removeAll();
                                                                            },
                                                                            id: 'CcpReportFilterStudentsBtn',
                                                                            enableToggle: true,
                                                                            pressed: true,
                                                                            text: 'Studenti',
                                                                            toggleGroup: 'parenti_studenti_btn'
                                                                        },
                                                                        {
                                                                            xtype: 'button',
                                                                            handler: function(button, e) {
                                                                                Ext.getCmp('CcpReporStudentGrd').hide();
                                                                                Ext.getCmp('CcpReporParentGrd').show();
                                                                                Ext.getStore('CcpReportStudentsParents').removeAll();
                                                                            },
                                                                            enableToggle: true,
                                                                            text: 'Parenti',
                                                                            toggleGroup: 'parenti_studenti_btn'
                                                                        },
                                                                        {
                                                                            xtype: 'tbspacer',
                                                                            flex: 1
                                                                        }
                                                                    ]
                                                                },
                                                                {
                                                                    xtype: 'toolbar',
                                                                    items: [
                                                                        {
                                                                            xtype: 'textfield',
                                                                            flex: 1,
                                                                            fieldLabel: '',
                                                                            emptyText: 'Cerca ...',
                                                                            listeners: {
                                                                                change: {
                                                                                    fn: me.onTextfieldChange3,
                                                                                    scope: me
                                                                                }
                                                                            }
                                                                        }
                                                                    ]
                                                                },
                                                                {
                                                                    xtype: 'gridpanel',
                                                                    flex: 1,
                                                                    id: 'CcpReporStudentGrd',
                                                                    title: '',
                                                                    store: 'CcpSubjects',
                                                                    columns: [
                                                                        {
                                                                            xtype: 'gridcolumn',
                                                                            dataIndex: 'display',
                                                                            text: 'Nome',
                                                                            flex: 1
                                                                        },
                                                                        {
                                                                            xtype: 'actioncolumn',
                                                                            width: 30,
                                                                            align: 'center',
                                                                            items: [
                                                                                {
                                                                                    handler: function(view, rowIndex, colIndex, item, e, record, row) {

                                                                                        var s = Ext.getStore('CcpReportStudentsParents'),
                                                                                            found=false;
                                                                                        Ext.each(s.getRange(), function(v) {
                                                                                            if (v.get('id') == record.get('db_id')) {
                                                                                                found=true;
                                                                                                return;
                                                                                            }
                                                                                        });
                                                                                        if (found===false) s.add({id: record.get('db_id'), name: record.get('display')});
                                                                                    },
                                                                                    iconCls: 'icon-arrow_down'
                                                                                }
                                                                            ]
                                                                        }
                                                                    ]
                                                                },
                                                                {
                                                                    xtype: 'gridpanel',
                                                                    flex: 1,
                                                                    hidden: true,
                                                                    id: 'CcpReporParentGrd',
                                                                    title: '',
                                                                    store: 'CcpFamilies',
                                                                    columns: [
                                                                        {
                                                                            xtype: 'gridcolumn',
                                                                            renderer: function(value, metaData, record, rowIndex, colIndex, store, view) {
                                                                                html = record.get('cognome') + ' ' + record.get('nome');
                                                                                html += '<br /> <font style="font-style:italic;font-size:9px;color:#333"> ' + record.get('parentele') + '</font>';

                                                                                return html;
                                                                            },
                                                                            dataIndex: 'string',
                                                                            text: 'Nome',
                                                                            flex: 1
                                                                        },
                                                                        {
                                                                            xtype: 'actioncolumn',
                                                                            width: 30,
                                                                            align: 'center',
                                                                            items: [
                                                                                {
                                                                                    handler: function(view, rowIndex, colIndex, item, e, record, row) {

                                                                                        var s = Ext.getStore('CcpReportStudentsParents'),
                                                                                            found=false;
                                                                                        Ext.each(s.getRange(), function(v) {
                                                                                            if (v.get('id') == record.get('id_parente')) {
                                                                                                found=true;
                                                                                                return;
                                                                                            }
                                                                                        });
                                                                                        if (found===false) s.add({id: record.get('id_parente'), name: record.get('text')});
                                                                                    },
                                                                                    iconCls: 'icon-arrow_down'
                                                                                }
                                                                            ]
                                                                        }
                                                                    ]
                                                                },
                                                                {
                                                                    xtype: 'gridpanel',
                                                                    flex: 1,
                                                                    title: '',
                                                                    store: 'CcpReportStudentsParents',
                                                                    columns: [
                                                                        {
                                                                            xtype: 'gridcolumn',
                                                                            dataIndex: 'name',
                                                                            text: 'Nome',
                                                                            flex: 1
                                                                        },
                                                                        {
                                                                            xtype: 'actioncolumn',
                                                                            width: 30,
                                                                            align: 'center',
                                                                            items: [
                                                                                {
                                                                                    handler: function(view, rowIndex, colIndex, item, e, record, row) {
                                                                                        Ext.getStore('CcpReportStudentsParents').remove(record);
                                                                                    },
                                                                                    iconCls: 'icon-delete'
                                                                                }
                                                                            ]
                                                                        }
                                                                    ]
                                                                }
                                                            ]
                                                        }
                                                    ]
                                                }
                                            ]
                                        },
                                        {
                                            xtype: 'container',
                                            flex: 1,
                                            padding: '0 2',
                                            layout: {
                                                type: 'vbox',
                                                align: 'stretch'
                                            },
                                            items: [
                                                {
                                                    xtype: 'gridpanel',
                                                    flex: 2,
                                                    id: 'CcpReportMovementTypeGrd',
                                                    title: 'Tipi di movimento',
                                                    store: 'CcpTypes',
                                                    columns: [
                                                        {
                                                            xtype: 'gridcolumn',
                                                            dataIndex: 'name_school_year',
                                                            text: 'Nome',
                                                            flex: 1
                                                        }
                                                    ],
                                                    selModel: Ext.create('Ext.selection.CheckboxModel', {

                                                    })
                                                },
                                                {
                                                    xtype: 'gridpanel',
                                                    flex: 1,
                                                    id: 'CcpReportPaymentMethodGrd',
                                                    padding: '5 0 ',
                                                    title: 'Modalità di pagamento',
                                                    store: 'CcpPaymentMethods',
                                                    columns: [
                                                        {
                                                            xtype: 'gridcolumn',
                                                            dataIndex: 'name',
                                                            text: 'Nome',
                                                            flex: 1
                                                        }
                                                    ],
                                                    selModel: Ext.create('Ext.selection.CheckboxModel', {

                                                    })
                                                }
                                            ]
                                        },
                                        {
                                            xtype: 'container',
                                            flex: 1,
                                            padding: '0 2',
                                            layout: 'fit',
                                            items: [
                                                {
                                                    xtype: 'panel',
                                                    layout: 'fit',
                                                    title: 'Filtri',
                                                    items: [
                                                        {
                                                            xtype: 'container',
                                                            padding: '0 5',
                                                            layout: {
                                                                type: 'vbox',
                                                                align: 'stretch'
                                                            },
                                                            items: [
                                                                {
                                                                    xtype: 'fieldset',
                                                                    id: 'CcpPrintWizardDate',
                                                                    title: 'Date controllo',
                                                                    layout: {
                                                                        type: 'vbox',
                                                                        align: 'stretch'
                                                                    },
                                                                    items: [
                                                                        {
                                                                            xtype: 'datefield',
                                                                            fieldLabel: 'Da',
                                                                            name: 'start_date',
                                                                            format: 'd/m/Y',
                                                                            submitFormat: 'Y-m-d'
                                                                        },
                                                                        {
                                                                            xtype: 'datefield',
                                                                            fieldLabel: 'a',
                                                                            name: 'end_date',
                                                                            format: 'd/m/Y',
                                                                            submitFormat: 'Y-m-d',
                                                                            listeners: {
                                                                                render: {
                                                                                    fn: me.onDatefieldRender,
                                                                                    scope: me
                                                                                }
                                                                            }
                                                                        }
                                                                    ]
                                                                },
                                                                {
                                                                    xtype: 'combobox',
                                                                    id: 'CcpPrintWizardEmpty',
                                                                    fieldLabel: 'Stampare elementi vuoti',
                                                                    name: 'print_empty',
                                                                    value: 'NO',
                                                                    store: [
                                                                        'SI',
                                                                        'NO'
                                                                    ]
                                                                }
                                                            ]
                                                        }
                                                    ]
                                                }
                                            ]
                                        }
                                    ],
                                    listeners: {
                                        render: {
                                            fn: me.onCcpReportFrmRender,
                                            scope: me
                                        }
                                    }
                                }
                            ],
                            dockedItems: [
                                {
                                    xtype: 'toolbar',
                                    flex: 1,
                                    dock: 'top',
                                    items: [
                                        {
                                            xtype: 'button',
                                            handler: function(button, e) {

                                                var data = Ext.getCmp('CcpReportFrm').getValues(),
                                                    studentsChecked,// = Ext.getCmp('CcpPrintStudentTree').getChecked(),
                                                    sectionsChecked = Ext.getCmp('CcpReportStructureGrd').getSelectionModel().getSelection(),
                                                    columnsChecked = Ext.getCmp('CcpReportColumnsGrd').getSelectionModel().getSelection(),
                                                    studentsParentsChecked = Ext.getStore('CcpReportStudentsParents').getRange(),
                                                    printFormat = Ext.getCmp('CcpReportFormatCmb').getValue(),
                                                    mcModel = Ext.getCmp('CcpPrintMcModelGrd').getSelectionModel().getSelection(),
                                                    paymentMethods = Ext.getCmp('CcpReportPaymentMethodGrd').getSelectionModel().getSelection(),
                                                    movementTypes = Ext.getCmp('CcpReportMovementTypeGrd').getSelectionModel().getSelection();

                                                data.movement_type_ids = [];
                                                Ext.each(movementTypes, function(v) {
                                                    data.movement_type_ids.push(v.get('id'));
                                                });
                                                data.movement_type_ids = Ext.encode(data.movement_type_ids);

                                                data.payment_method_ids = [];
                                                Ext.each(paymentMethods, function(v) {
                                                    data.payment_method_ids.push(v.get('id'));
                                                });
                                                data.payment_method_ids = Ext.encode(data.payment_method_ids);

                                                data.students = [];
                                                studentsChecked = Ext.getCmp('CcpPrintStudentTree').getChecked();
                                                Ext.each(studentsChecked, function(v) {
                                                    if(v.get('db_id') > 0) {
                                                        data.students.push(v.get('db_id'));
                                                    }
                                                });
                                                if(Ext.getCmp('CcpReportFilterStudentsBtn').pressed === true) {
                                                    Ext.each(studentsParentsChecked, function(v) {
                                                        data.students.push(v.get('id'));
                                                    });
                                                } else {
                                                    if(Ext.getCmp('CcpPrintStudentTree').getChecked().length===0) {
                                                        data.parents = [];
                                                        Ext.each(studentsParentsChecked, function(v) {
                                                            data.parents.push(parseInt(v.get('id')));
                                                        });
                                                        data.parents = Ext.encode(data.parents);
                                                    }
                                                }
                                                data.students = Ext.encode(data.students);

                                                data.sections = [];
                                                Ext.each(sectionsChecked, function(v) {
                                                    data.sections.push(v.get('id'));
                                                });
                                                data.sections = Ext.encode(data.sections);

                                                data.columns = [];
                                                Ext.each(columnsChecked, function(v) {
                                                    data.columns.push(v.get('id'));
                                                });
                                                data.columns = Ext.encode(data.columns);



                                                data.print = sectionsChecked[0].get('mc2Class') ? sectionsChecked[0].get('mc2Class') : 'Report';
                                                if(mcModel.length > 0) {
                                                    data.mc_template = mcModel[0].get('id_template');
                                                    data.print = sectionsChecked[0].get('mc2Class');
                                                }



                                                data.newSpool = 1;
                                                data.namespace = 'CCP';
                                                data.type = printFormat;
                                                data.mime = printFormat === 'PDF' ? 'application/pdf' : 'application/vnd.ms-excel';


                                                data.sort = [];
                                                data.sort = Ext.encode(data.sort);

                                                Ext.Ajax.request({
                                                    url: '/mc2-api/core/print',
                                                    params: data,
                                                    success: function(response, opts) {
                                                        var res = Ext.decode(response.responseText);
                                                        mc2ui.app.showNotifyPrint(res);
                                                    }
                                                });

                                            },
                                            text: 'Stampa'
                                        }
                                    ]
                                }
                            ]
                        },
                        {
                            xtype: 'panel',
                            layout: 'fit',
                            title: 'Corrispettivi',
                            dockedItems: [
                                {
                                    xtype: 'toolbar',
                                    dock: 'top',
                                    items: [
                                        {
                                            xtype: 'buttongroup',
                                            title: '',
                                            columns: 2,
                                            items: [
                                                {
                                                    xtype: 'button',
                                                    handler: function(button, e) {
                                                        var ssy = Ext.getCmp('CcpCorrispettiviSchoolYearCmb').getValue();
                                                        Ext.getStore('Corrispettivi').load({params: {group: 'D', 'subject_school_year': ssy}});
                                                        Ext.getCmp('CcpCorrispettiviByMonthBtn').toggle(false);
                                                    },
                                                    id: 'CcpCorrispettiviByDayBtn',
                                                    enableToggle: true,
                                                    pressed: true,
                                                    text: 'Giornaliero'
                                                },
                                                {
                                                    xtype: 'button',
                                                    handler: function(button, e) {
                                                        var ssy = Ext.getCmp('CcpCorrispettiviSchoolYearCmb').getValue();
                                                        Ext.getStore('Corrispettivi').load({params: {group: 'M', 'subject_school_year': ssy}});
                                                        Ext.getCmp('CcpCorrispettiviByDayBtn').toggle(false);
                                                    },
                                                    id: 'CcpCorrispettiviByMonthBtn',
                                                    enableToggle: true,
                                                    text: 'Mensile'
                                                }
                                            ]
                                        },
                                        {
                                            xtype: 'combobox',
                                            id: 'CcpCorrispettiviSchoolYearCmb',
                                            margin: '0 0 0 5',
                                            fieldLabel: 'Anno scolastico',
                                            store: 'CcpSchoolYearsFilter',
                                            valueField: 'id',
                                            listeners: {
                                                render: {
                                                    fn: me.onComboboxRender,
                                                    scope: me
                                                },
                                                change: {
                                                    fn: me.onComboboxChange2,
                                                    scope: me
                                                }
                                            }
                                        },
                                        {
                                            xtype: 'tbspacer',
                                            flex: 1
                                        },
                                        {
                                            xtype: 'button',
                                            handler: function(button, e) {
                                                Ext.widget('CcpCorrispettiviPrintWin').show();
                                            },
                                            text: 'Stampe'
                                        },
                                        {
                                            xtype: 'button',
                                            handler: function(button, e) {
                                                Ext.widget('CcpCorrispettiviPrintWin').show();
                                                Ext.getCmp('CcpCorrispettiviPrintTypeCmb').store.removeAll();

                                                Ext.getCmp('CcpCorrispettiviPrintTypeCmb').store.add([
                                                //['CorrispettiviCausaleIndirizzo', 'Corrispettivi per causale e indirizzo'],
                                                ['CorrispettiviIndirizziTipoCausale', 'Corrispettivi indirizzo, tipo e causale']
                                                ]);

                                                Ext.getCmp('CcpCorrispettiviPrintWin').printType='xls';
                                            },
                                            iconCls: 'icon-page_excel',
                                            text: ''
                                        }
                                    ]
                                }
                            ],
                            items: [
                                {
                                    xtype: 'gridpanel',
                                    id: 'CcpCorrispettiviGrd',
                                    title: '',
                                    store: 'Corrispettivi',
                                    columns: [
                                        {
                                            xtype: 'datecolumn',
                                            id: 'CcpCorrispettiviDataCol',
                                            dataIndex: 'date',
                                            text: 'Data',
                                            flex: 1,
                                            format: 'd/m/Y'
                                        },
                                        {
                                            xtype: 'numbercolumn',
                                            align: 'right',
                                            dataIndex: 'gross',
                                            text: 'Imponibile'
                                        },
                                        {
                                            xtype: 'numbercolumn',
                                            align: 'right',
                                            dataIndex: 'vat',
                                            text: 'Imposte'
                                        },
                                        {
                                            xtype: 'numbercolumn',
                                            align: 'right',
                                            dataIndex: 'total',
                                            text: 'Totale'
                                        }
                                    ]
                                }
                            ],
                            listeners: {
                                show: {
                                    fn: me.onTabShow1,
                                    scope: me
                                }
                            }
                        },
                        {
                            xtype: 'panel',
                            layout: 'border',
                            title: 'Documenti',
                            items: [
                                {
                                    xtype: 'menu',
                                    region: 'east',
                                    id: 'DocumentReportMn',
                                    width: 200,
                                    items: [
                                        {
                                            xtype: 'menuitem',
                                            handler: function(item, e) {
                                                Ext.widget('UploadDocumentReportWin').show();
                                            },
                                            iconCls: 'icon-arrow_up',
                                            text: 'Aggiorna documento'
                                        }
                                    ]
                                },
                                {
                                    xtype: 'gridpanel',
                                    getSelectedData: function() {
                                        var sel = Ext.getCmp('CcpReportGrd').getSelectionModel().getSelection(),
                                            data = {};
                                        if(sel.length>0) {
                                            data.ids = [];
                                            Ext.each(sel, function(val){
                                                data.ids.push(val.get('id'));
                                            });
                                            data.ids = Ext.encode(data.ids);
                                        } else {
                                            data= Ext.getCmp('CcpReportFilterFrm').getFilter();
                                        }
                                        return data;
                                    },
                                    flex: 1,
                                    floatable: false,
                                    region: 'center',
                                    id: 'CcpReportGrd',
                                    title: '',
                                    store: 'CcpReports',
                                    columns: [
                                        {
                                            xtype: 'gridcolumn',
                                            dataIndex: 'description',
                                            text: 'Modello',
                                            flex: 1
                                        },
                                        {
                                            xtype: 'gridcolumn',
                                            width: 150,
                                            dataIndex: 'subject_data',
                                            text: 'Soggetto'
                                        },
                                        {
                                            xtype: 'gridcolumn',
                                            align: 'center',
                                            dataIndex: 'subject_class',
                                            text: 'Classe'
                                        },
                                        {
                                            xtype: 'gridcolumn',
                                            align: 'center',
                                            dataIndex: 'subject_school_address_code',
                                            text: 'Indirizzo'
                                        },
                                        {
                                            xtype: 'gridcolumn',
                                            align: 'center',
                                            dataIndex: 'school_year',
                                            text: 'Anno scolastico'
                                        },
                                        {
                                            xtype: 'datecolumn',
                                            width: 120,
                                            align: 'center',
                                            dataIndex: 'date_created',
                                            text: 'Creazione',
                                            format: 'd/m/Y H:i'
                                        },
                                        {
                                            xtype: 'gridcolumn',
                                            width: 70,
                                            dataIndex: 'print_key',
                                            text: 'Gruppo'
                                        },
                                        {
                                            xtype: 'gridcolumn',
                                            width: 60,
                                            align: 'center',
                                            dataIndex: 'print_type',
                                            text: 'Tipo'
                                        },
                                        {
                                            xtype: 'gridcolumn',
                                            renderer: function(value, metaData, record, rowIndex, colIndex, store, view) {
                                                var status=record.get('status').toUpperCase();
                                                if (status == 'PRONTA' && value===true) {
                                                    status = 'PUBBLICATA';
                                                }

                                                return status;
                                            },
                                            align: 'center',
                                            dataIndex: 'published',
                                            text: 'Stato'
                                        },
                                        {
                                            xtype: 'actioncolumn',
                                            width: 50,
                                            align: 'center',
                                            items: [
                                                {
                                                    handler: function(view, rowIndex, colIndex, item, e, record, row) {
                                                        window.open("/mc2-api/ccp/report/download?id=" + record.get('id'));
                                                    },
                                                    iconCls: 'icon-printer'
                                                }
                                            ]
                                        }
                                    ],
                                    viewConfig: {
                                        listeners: {
                                            itemcontextmenu: {
                                                fn: me.onViewItemContextMenu3,
                                                scope: me
                                            }
                                        }
                                    },
                                    dockedItems: [
                                        {
                                            xtype: 'pagingtoolbar',
                                            dock: 'bottom',
                                            width: 360,
                                            displayInfo: true,
                                            store: 'CcpReports'
                                        },
                                        {
                                            xtype: 'toolbar',
                                            dock: 'top',
                                            items: [
                                                {
                                                    xtype: 'button',
                                                    text: 'Pubblicazione',
                                                    menu: {
                                                        xtype: 'menu',
                                                        width: 150,
                                                        items: [
                                                            {
                                                                xtype: 'menuitem',
                                                                handler: function(item, e) {
                                                                    var data = Ext.getCmp('CcpReportGrd').getSelectedData(),
                                                                        total = !data.ids ? Ext.getStore('CcpReports').totalCount : Ext.decode(data.ids).length
                                                                        ;

                                                                    Ext.Msg.confirm('ATTENZIONE', 'Stanno per essere pubblicati ' +total + ' documenti. Continuare?', function(btn){
                                                                        if(btn=='yes') {

                                                                            data.publish = true;
                                                                            Ext.Ajax.request({
                                                                                mathod: 'POST',
                                                                                url: '/mc2-api/ccp/report/publication',
                                                                                params: data,
                                                                                success: function(res) {
                                                                                    var r = Ext.decode(res.responseText);
                                                                                    if(r.success===true) Ext.getStore('CcpReports').load();
                                                                                    else Ext.Msg.Alert('ERRORE', r.message);
                                                                                }
                                                                            });
                                                                        }
                                                                    });
                                                                },
                                                                iconCls: 'icon-world',
                                                                text: 'Pubblica'
                                                            },
                                                            {
                                                                xtype: 'menuitem',
                                                                handler: function(item, e) {
                                                                    var data = Ext.getCmp('CcpReportGrd').getSelectedData(),
                                                                        total = !data.ids ? Ext.getStore('CcpReports').totalCount : Ext.decode(data.ids).length;

                                                                    Ext.Msg.confirm('ATTENZIONE', 'Si sta rimuovendo la pubblicazione di ' +total + ' documenti. Continuare?', function(btn){
                                                                        if(btn=='yes') {

                                                                            data.publish = false;
                                                                            Ext.Ajax.request({
                                                                                mathod: 'POST',
                                                                                url: '/mc2-api/ccp/report/publication',
                                                                                params: data,
                                                                                success: function(res) {
                                                                                    var r = Ext.decode(res.responseText);
                                                                                    if(r.success===true) Ext.getStore('CcpReports').load();
                                                                                    else Ext.Msg.Alert('ERRORE', r.message);
                                                                                }
                                                                            });
                                                                        }
                                                                    });
                                                                },
                                                                iconCls: 'icon-world_delete',
                                                                text: 'Cancella pubblicazione'
                                                            }
                                                        ]
                                                    }
                                                },
                                                {
                                                    xtype: 'button',
                                                    handler: function(button, e) {
                                                        var data = Ext.getCmp('CcpReportGrd').getSelectedData(),
                                                            total = !data.ids ? Ext.getStore('CcpReports').totalCount : Ext.decode(data.ids).length;

                                                        Ext.Msg.confirm('ATTENZIONE', 'Stanno per essere cancellati ' + total + ' documenti. Continuare?', function(btn){
                                                            if(btn=='yes') {
                                                                Ext.Ajax.request({
                                                                    mathod: 'POST',
                                                                    url: '/mc2-api/ccp/report/delete_massive',
                                                                    params: data,
                                                                    success: function(res) {
                                                                        var r = Ext.decode(res.responseText);
                                                                        if(r.success===true) Ext.getStore('CcpReports').load();
                                                                        else Ext.Msg.Alert('ERRORE', r.message);
                                                                    }
                                                                });

                                                            }
                                                        });


                                                    },
                                                    text: 'Elimina elementi'
                                                },
                                                {
                                                    xtype: 'tbspacer',
                                                    flex: 1
                                                },
                                                {
                                                    xtype: 'button',
                                                    handler: function(button, e) {
                                                        var filter={},
                                                            ids = [],
                                                            url='/mc2-api/ccp/report/download';


                                                        Ext.each(Ext.getCmp('CcpReportGrd').getSelectionModel().getSelection(), function(v, i){
                                                            ids.push(v.get('id'));
                                                        });

                                                        if (ids.length > 0) {
                                                            filter.ids = Ext.encode(ids);
                                                        } else {
                                                            filter = Ext.getCmp('CcpReportFilterFrm').getFilter();
                                                        }
                                                        filter.massive=1;
                                                        url += '?' + Ext.urlEncode(filter);
                                                        window.open(url,'_blank');

                                                    },
                                                    text: 'Scarica dati filtrati'
                                                }
                                            ]
                                        }
                                    ],
                                    selModel: Ext.create('Ext.selection.CheckboxModel', {

                                    })
                                },
                                {
                                    xtype: 'panel',
                                    region: 'west',
                                    split: true,
                                    width: 300,
                                    layout: 'fit',
                                    title: 'Filtri',
                                    items: [
                                        {
                                            xtype: 'form',
                                            getFilter: function() {
                                                var data = Ext.getCmp('CcpReportFilterFrm').getValues();


                                                data.status = data.status_text.toLowerCase();
                                                if(data.status == 'pubblicata') {
                                                    delete data.status;
                                                    data.published = true;
                                                } else if (data.status == 'non pubblicata') {
                                                    delete data.status;
                                                    data.published = false;
                                                }

                                                delete data.status_text;
                                                return data;
                                            },
                                            loadFilterData: function() {
                                                Ext.Ajax.request({
                                                    method: 'GET',
                                                    url: '/mc2-api/ccp/report/filter/description',
                                                    success: function(res) {
                                                        var r = Ext.decode(res.responseText);
                                                        Ext.getCmp('CcpReportFilterDescriptionCmb').getStore().removeAll();

                                                        if(r.success === true) {
                                                            Ext.each(r.results, function(v){
                                                                Ext.getCmp('CcpReportFilterDescriptionCmb').getStore().add({field1: v});
                                                            });
                                                        }
                                                    }
                                                });

                                                Ext.Ajax.request({
                                                    method: 'GET',
                                                    url: '/mc2-api/ccp/report/filter/subject_class',
                                                    success: function(res) {
                                                        var r = Ext.decode(res.responseText);
                                                        Ext.getCmp('CcpReportFilterClassCmb').getStore().removeAll();

                                                        if(r.success === true) {
                                                            Ext.each(r.results, function(v){
                                                                Ext.getCmp('CcpReportFilterClassCmb').getStore().add({field1: v});
                                                            });
                                                        }
                                                    }
                                                });

                                                Ext.Ajax.request({
                                                    method: 'GET',
                                                    url: '/mc2-api/ccp/report/filter/subject_school_address_code',
                                                    success: function(res) {
                                                        var r = Ext.decode(res.responseText);
                                                        Ext.getCmp('CcpReportFilterIndirizzoCmb').getStore().removeAll();

                                                        if(r.success === true) {
                                                            Ext.each(r.results, function(v){
                                                                Ext.getCmp('CcpReportFilterIndirizzoCmb').getStore().add({field1: v});
                                                            });
                                                        }
                                                    }
                                                });

                                                Ext.Ajax.request({
                                                    method: 'GET',
                                                    url: '/mc2-api/ccp/report/filter/school_year',
                                                    success: function(res) {
                                                        var r = Ext.decode(res.responseText);
                                                        Ext.getCmp('CcpReportFilterAnnoCmb').getStore().removeAll();

                                                        if(r.success === true) {
                                                            Ext.each(r.results, function(v){
                                                                Ext.getCmp('CcpReportFilterAnnoCmb').getStore().add({field1: v});
                                                            });
                                                        }
                                                    }
                                                });
                                            },
                                            id: 'CcpReportFilterFrm',
                                            bodyPadding: 10,
                                            title: '',
                                            items: [
                                                {
                                                    xtype: 'textfield',
                                                    anchor: '100%',
                                                    fieldLabel: 'Soggetto',
                                                    labelWidth: 60,
                                                    name: 'subject_data',
                                                    emptyText: 'Cerca ...'
                                                },
                                                {
                                                    xtype: 'combobox',
                                                    anchor: '100%',
                                                    id: 'CcpReportFilterDescriptionCmb',
                                                    fieldLabel: 'Modello',
                                                    labelWidth: 60,
                                                    name: 'description',
                                                    store: [

                                                    ]
                                                },
                                                {
                                                    xtype: 'combobox',
                                                    anchor: '100%',
                                                    id: 'CcpReportFilterClassCmb',
                                                    fieldLabel: 'Classe',
                                                    labelWidth: 60,
                                                    name: 'subject_class',
                                                    store: [

                                                    ]
                                                },
                                                {
                                                    xtype: 'combobox',
                                                    anchor: '100%',
                                                    id: 'CcpReportFilterIndirizzoCmb',
                                                    fieldLabel: 'Indirizzi',
                                                    labelWidth: 60,
                                                    name: 'subject_school_address_code',
                                                    store: [

                                                    ]
                                                },
                                                {
                                                    xtype: 'combobox',
                                                    anchor: '100%',
                                                    id: 'CcpReportFilterAnnoCmb',
                                                    fieldLabel: 'Anno scolastico',
                                                    labelWidth: 60,
                                                    name: 'school_year',
                                                    store: [

                                                    ]
                                                },
                                                {
                                                    xtype: 'fieldset',
                                                    title: 'Data creazione',
                                                    items: [
                                                        {
                                                            xtype: 'datefield',
                                                            anchor: '100%',
                                                            fieldLabel: 'Da',
                                                            labelWidth: 50,
                                                            name: 'date_created_start',
                                                            format: 'd/m/Y',
                                                            submitFormat: 'Y-m-d'
                                                        },
                                                        {
                                                            xtype: 'datefield',
                                                            anchor: '100%',
                                                            fieldLabel: 'A',
                                                            labelWidth: 50,
                                                            name: 'date_created_end',
                                                            format: 'd/m/Y',
                                                            submitFormat: 'Y-m-d'
                                                        }
                                                    ]
                                                },
                                                {
                                                    xtype: 'numberfield',
                                                    anchor: '100%',
                                                    fieldLabel: 'Gruppo',
                                                    labelWidth: 60,
                                                    name: 'print_key',
                                                    hideTrigger: true
                                                },
                                                {
                                                    xtype: 'combobox',
                                                    anchor: '100%',
                                                    fieldLabel: 'Stato',
                                                    labelWidth: 60,
                                                    name: 'status_text',
                                                    store: [
                                                        'IN CORSO',
                                                        'PRONTA',
                                                        'ERRORE',
                                                        'PUBBLICATA',
                                                        'NON PUBBLICATA'
                                                    ]
                                                },
                                                {
                                                    xtype: 'container',
                                                    layout: 'fit',
                                                    items: [
                                                        {
                                                            xtype: 'button',
                                                            handler: function(button, e) {
                                                                /*var data= Ext.getCmp('CcpReportFilterFrm').getFilter();
                                                                Ext.getStore('CcpReports').load({
                                                                params: data
                                                                });*/

                                                                Ext.getStore('CcpReports').load();
                                                            },
                                                            text: 'Cerca'
                                                        }
                                                    ]
                                                }
                                            ],
                                            listeners: {
                                                afterrender: {
                                                    fn: me.onCcpReportFilterFrmAfterRender,
                                                    scope: me
                                                }
                                            }
                                        }
                                    ]
                                }
                            ],
                            tabConfig: {
                                xtype: 'tab',
                                listeners: {
                                    activate: {
                                        fn: me.onTabShow2,
                                        scope: me
                                    }
                                }
                            },
                            listeners: {
                                render: {
                                    fn: me.onPanelRender1,
                                    scope: me
                                }
                            }
                        },
                        {
                            xtype: 'mypanel27'
                        }
                    ]
                }
            ],
            dockedItems: [
                {
                    xtype: 'toolbar',
                    permissible: true,
                    dock: 'top',
                    id: 'CcpToolbar',
                    itemId: 'CcpToolbar',
                    autoScroll: true,
                    items: [
                        {
                            xtype: 'label',
                            hidden: true,
                            html: '<b style="padding:0px 5px">Movimenti:</b>',
                            padding: '0 5'
                        },
                        {
                            xtype: 'button',
                            id: 'CcpCategoryBtn',
                            iconCls: 'icon-build',
                            text: 'Categorie',
                            menu: {
                                xtype: 'menu',
                                width: 150,
                                items: [
                                    {
                                        xtype: 'menuitem',
                                        handler: function(item, e) {
                                            Ext.widget('CcpCategoriesWin').show();
                                        },
                                        text: 'Categorie tipi'
                                    },
                                    {
                                        xtype: 'menuitem',
                                        handler: function(item, e) {
                                            Ext.widget('CcpPrintCategoriesWin').show();
                                        },
                                        text: 'Categorie stampe'
                                    }
                                ]
                            }
                        },
                        {
                            xtype: 'button',
                            handler: function(button, e) {
                                Ext.widget('CcpTypesWin').show();
                            },
                            id: 'CcpTypeBtn',
                            iconCls: 'icon-bricks',
                            text: 'Tipi'
                        },
                        {
                            xtype: 'button',
                            handler: function(button, e) {
                                Ext.widget('CcpAdditionalsWin').show();
                            },
                            id: 'CcpAdditionalBtn',
                            iconCls: 'icon-money_add',
                            text: 'Tipi sconti'
                        },
                        {
                            xtype: 'button',
                            text: 'Gestione sconti',
                            listeners: {
                                render: {
                                    fn: me.onButtonRender,
                                    scope: me
                                }
                            },
                            menu: {
                                xtype: 'menu',
                                width: 165,
                                items: [
                                    {
                                        xtype: 'menuitem',
                                        handler: function(item, e) {
                                            Ext.widget('CcpDiscountWin').show();
                                        },
                                        text: 'Assegna sconto'
                                    },
                                    {
                                        xtype: 'menuitem',
                                        handler: function(item, e) {
                                            Ext.Ajax.request({
                                                url: '/mc2-api/ccp/delete_all_discount',
                                                params: {
                                                    confirm: 0
                                                },
                                                success: function(res) {
                                                    var r = Ext.decode(res.responseText);
                                                    if(r.success===true) {
                                                        Ext.Msg.confirm('ATTENZIONE', 'Verranno cancellati definitivamente <b>'  + r.total + ' sconti</b>. Questa operazione non potrà essere annullata. Procedere?', function(btn) {
                                                            if(btn == 'yes') {
                                                                Ext.Ajax.request({
                                                                    url: '/mc2-api/ccp/delete_all_discount',
                                                                    params: {
                                                                        confirm: 1
                                                                    },
                                                                    success: function(res) {
                                                                        if(r.success===true) {
                                                                            Ext.Msg.alert('SUCCESSO', 'Cancellazione avvenuta con successo');
                                                                        } else {
                                                                            Ext.Msg.alert('ERRORE', r.message);
                                                                        }
                                                                    },
                                                                    failure: function(res) {
                                                                        Ext.Msg.alert('ERRORE', 'Errore durante la chiamata. Riprovare più tardi');
                                                                    }
                                                                });
                                                            }
                                                        });
                                                    } else {
                                                        Ext.Msg.alert('ERRORE', r.message);
                                                    }
                                                },
                                                failure: function () {
                                                    Ext.Msg.alert('ERRORE', 'Errore durante la chiamata. Riprovare più tardi');
                                                }
                                            });
                                        },
                                        iconCls: 'icon-cancel',
                                        text: 'Cancella tutti gli sconti'
                                    }
                                ]
                            }
                        },
                        {
                            xtype: 'tbseparator'
                        },
                        {
                            xtype: 'button',
                            hidden: true,
                            id: 'CcpPrintsBtn',
                            iconCls: 'icon-printer',
                            text: 'Stampe',
                            menu: {
                                xtype: 'menu',
                                items: [
                                    {
                                        xtype: 'menuitem',
                                        handler: function(item, e) {
                                            Ext.widget('CcpCashJournalPrintWin').show();
                                        },
                                        iconCls: 'icon-book_open',
                                        text: 'Giornale di cassa'
                                    },
                                    {
                                        xtype: 'menuitem',
                                        handler: function(item, e) {
                                            Ext.widget('CcpByCCPrintWin').show();
                                            Ext.getStore('CoreBankAccounts').load({
                                                params: {
                                                    ise_id: 0,
                                                    ise_type: 'I'
                                                }
                                            });
                                            Ext.getCmp('CcpByCCPrintDateStart').setValue(new Date());
                                            Ext.getCmp('CcpByCCPrintDateEnd').setValue(new Date());
                                        },
                                        hidden: true,
                                        iconCls: 'icon-book_open_mark',
                                        text: 'Registro conto corrente'
                                    },
                                    {
                                        xtype: 'menuseparator'
                                    },
                                    {
                                        xtype: 'menuitem',
                                        handler: function(item, e) {
                                            Ext.widget('CcpPrintTypeWin').show();
                                        },
                                        iconCls: 'icon-report',
                                        text: 'Riepilogo dati filtrati'
                                    }
                                ]
                            }
                        },
                        {
                            xtype: 'button',
                            text: 'Servizi',
                            menu: {
                                xtype: 'menu',
                                items: [
                                    {
                                        xtype: 'menuitem',
                                        iconCls: 'icon-printer',
                                        handler: function(item, e) {
                                            Ext.widget('CcpServiziWin').show();
                                        },
                                        text: 'Stampa'
                                    }, {
                                        xtype: 'menuitem',
                                        handler: function(item, e) {
                                            //document.createModal('Servizi senza tipo di movimento', '<marketplace-missing-movement-type class="h-full"></marketplace-missing-movement-type>');
                                            document.checkMarketplaceMissMovementType(false);
                                        },
                                        text: 'Abbina tipi di movimento'
                                    }
                                ]
                            }
                        },
                        {
                            xtype: 'button',
                            handler: function(button, e) {
                                Ext.widget('CcpCreditTypeWin').show();
                            },
                            text: 'Tipi credito'
                        },
                        {
                            xtype: 'button',
                            text: 'Gestione crediti',
                            menu: {
                                xtype: 'menu',
                                items: [
                                    {
                                        xtype: 'menuitem',
                                        handler: function(item, e) {
                                            Ext.widget('CcpCreditDistributionWin').show();
                                        },
                                        text: 'Distribuzione credito'
                                    },
                                    {
                                        xtype: 'menuitem',
                                        handler: function(item, e) {
                                            Ext.widget('CcpCreditUploadWin').show();
                                        },
                                        text: 'Importa credito per tipo'
                                    },
                                    {
                                        xtype: 'menuitem',
                                        handler: function(item, e) {
                                            Ext.widget('CcpPvrUploadWin').show();
                                        },
                                        text: 'Importa crediti (PVR)'
                                    },
                                    {
                                        xtype: 'menuitem',
                                        handler: function(item, e) {
                                            Ext.widget('CcpPvrUploadWin').show();
                                            Ext.getCmp('CcpPvrUpload').name = 'camtUpload';
                                            Ext.getCmp('CcpUploadPvrFrm').getForm().url = '/mc2-api/ccp/import_camt';
                                        },
                                        text: 'Importa crediti (CAMT)'
                                    }
                                ]
                            }
                        },
                        {
                            xtype: 'button',
                            handler: function(button, e) {
                                Ext.widget('CcpExtraTimeWin').show();
                            },
                            text: 'Consolidamento'
                        },
                        {
                            xtype: 'button',
                            iconCls: 'icon-arrow_join',
                            text: 'Agenzia entrate',
                            menu: {
                                xtype: 'menu',
                                width: 120,
                                items: [
                                    {
                                        xtype: 'menuitem',
                                        handler: function(item, e) {
                                            Ext.widget('CcpAeExportWin').show();
                                        },
                                        text: 'Esportazione'
                                    },
                                    {
                                        xtype: 'menuitem',
                                        handler: function(item, e) {
                                            Ext.widget('CcpAdePrintFromFileWin').show();
                                        },
                                        text: 'Stampa da file'
                                    }
                                ]
                            }
                        },
                        {
                            xtype: 'button',
                            handler: function(button, e) {
                                Ext.widget('CcpBollettiniPrintWin').show();
                            },
                            id: 'CcpBollettiniBtn',
                            iconCls: 'icon-newspaper',
                            text: 'Bollettini'
                        },
                        {
                            xtype: 'button',
                            handler: function(button, e) {
                                Ext.widget('CcpStudentsMissing').show();
                            },
                            iconCls: 'icon-error',
                            text: 'Anomalie studenti/movimenti'
                        },
                        {
                            xtype: 'button',
                            text: 'Export gestionali',
                            menu: {
                                xtype: 'menu',
                                width: 120,
                                items: [
                                    {
                                        xtype: 'menuitem',
                                        handler: function(item, e) {
                                            window.open('/mc2-api/ccp/export_mexal','_blank');
                                        },
                                        text: 'Passepartout'
                                    }
                                ]
                            }
                        },
                        {
                            xtype: 'tbspacer',
                            flex: 1
                        }
                    ]
                },
                {
                    xtype: 'toolbar',
                    dock: 'top',
                    hidden: true,
                    items: [
                        {
                            xtype: 'label',
                            html: '<b style="padding:0px 5px">Stampe:</b>',
                            padding: '0 5'
                        },
                        {
                            xtype: 'button',
                            handler: function(button, e) {
                                Ext.widget('CcpCashJournalPrintWin').show();
                            },
                            iconCls: 'icon-book_open',
                            text: 'Giornale di cassa'
                        }
                    ]
                }
            ],
            listeners: {
                boxready: {
                    fn: me.onCcpPnlBoxReady,
                    scope: me
                }
            }
        });

        me.callParent(arguments);
    },

    onTabRender: function(component, eOpts) {

        var date = new Date();

        date.setDate(1);
        date.setMonth(0);
        Ext.getCmp('CcpInvoiceCreateFrom').setValue(date);

        date.setDate(31);
        date.setMonth(11);
        Ext.getCmp('CcpInvoiceCreateTo').setValue(date);

        Ext.getStore('CcpInvoices').load({params: {'limit': 50}});

    },

    onViewItemContextMenu: function(dataview, record, item, index, e, eOpts) {

        e.stopEvent();
        var newX = e.xy[0];
        var newY = e.xy[1];

        Ext.getCmp('CcpInvoiceMovementsMn').showAt([newX,newY]);

    },

    onMenuitemRender: function(component, eOpts) {
        if(mc2ui.app.settings.oldCreditNoteMethod===true) {
            component.show();
        }
    },

    onCcpInvoiceMovementsMnShow: function(component, eOpts) {
        setTimeout(function(){var record = Ext.getCmp('CcpInvoicesGrid').getSelectionModel().getSelection()[0];

                              Ext.getCmp('CcpInvoicePublishMn').show();
                              Ext.getCmp('CcpInvoiceUnpublishMn').show();



                              if (!record.get('publication_path')) Ext.getCmp('CcpInvoiceUnpublishMn').hide();

                              else Ext.getCmp('CcpInvoicePublishMn').hide();

                              if(record.get('credit_note') === true && mc2ui.app.settings.creditNotesPublish === false) {
                                  Ext.getCmp('CcpInvoicePublishMn').hide();
                                  Ext.getCmp('CcpInvoiceUnpublishMn').hide();
                              }

                             }, 200);

    },

    onTabShow: function(component, eOpts) {
        Ext.getStore('Invoices').load();
    },

    onViewItemContextMenu1: function(dataview, record, item, index, e, eOpts) {

        e.stopEvent();
        var newX = e.xy[0];
        var newY = e.xy[1];

        Ext.getCmp('CcpDepositSlipMn').showAt([newX,newY]);
    },

    onComboboxSelect112: function(field, newValue, oldValue, eOpts) {

        var subject_school_year = Ext.getCmp('CcpSchoolYear2').getValue();

        Ext.getCmp('CcpClassSectionCmb1').setValue();
        Ext.getCmp('CcpAddressSectionCmb1').setValue();

        Ext.getStore('Classi1').load({params: {subject_school_year: subject_school_year}});
        Ext.getStore('Indirizzi1').load({params: {subject_school_year: subject_school_year}});
        if(Ext.getCmp('CcpSchoolYear2').getValue() === null) {
            Ext.getCmp('CcpPaymentFilterClassCnt').disable();
            Ext.getCmp('CcpPaymentFilterAddressCnt').disable();

        } else {
            Ext.getCmp('CcpPaymentFilterClassCnt').enable();
            Ext.getCmp('CcpPaymentFilterAddressCnt').enable();
        }


        Ext.getCmp('CcpPaymentsFilterForm').loadByFilter();


    },

    onCcpSchoolYearAfterRender2: function(component, eOpts) {
        //if(mc2ui.app.settings.ccp_filter_movement_by_school_year === true) {
            Ext.each(Ext.getStore('McDbs').getRange(), function(v){
                if(v.get('current') === true) {
                    Ext.getCmp('CcpSchoolYear2').select(v);
                }
            });
        //}
    },

    onCcpAddressSectionCmbSelect1: function(combo, records, eOpts) {
        Ext.getCmp('CcpClassSectionCmb1').setValue();

        Ext.getCmp('CcpPaymentsFilterForm').loadByFilter();

        Ext.getStore('Classi1').clearFilter();
        Ext.getStore('Classi1').filter(function(v){
            var addressIds = [];
            Ext.each(records, function(v){
                addressIds.push(v.get('code'));
            });
            if (addressIds.includes(v.get('school_address_code'))) return true;
            else return false;
        });

    },

    onCcpAddressSectionCmbChange1: function(field, newValue, oldValue, eOpts) {
        if(!newValue) Ext.getStore('Classi').clearFilter();

    },

    onComboboxSelect51: function(combo, records, eOpts) {
        Ext.getCmp('CcpPaymentsFilterForm').loadByFilter();
    },

    onComboboxSelect: function(combo, records, eOpts) {
        var movement_school_year = Ext.getCmp('CcpSchoolYearMovement2').getValue();

        Ext.getCmp('CcpPaymentCategoryCmb').setValue();
        Ext.getStore('CcpCategoriesFilter1').load({
            params: {movement_school_year: movement_school_year}
        });


        Ext.getCmp('CcpPaymentTypeCmb').setValue();
        Ext.getStore('CcpTypesFilter1').load({
            params: {movement_school_year: movement_school_year}

        });


        Ext.getCmp('CcpPaymentsFilterForm').loadByFilter();

    },

    onComboboxSelect7: function(combo, records, eOpts) {
        var movement_school_year = Ext.getCmp('CcpSchoolYearMovement2').getValue();
        Ext.getStore('CcpTypesFilter1').load({
            params: {
                school_year: movement_school_year,
                movement_school_year: movement_school_year,
                category_id: records[0].get('id')
            }
        });
        Ext.getCmp('CcpPaymentsFilterForm').loadByFilter();
    },

    onComboboxSelect1: function(combo, records, eOpts) {
        Ext.getCmp('CcpPaymentsFilterForm').loadByFilter();
    },

    onComboboxSelect31: function(combo, records, eOpts) {
        Ext.getCmp('CcpPaymentsFilterForm').loadByFilter();
    },

    onDatefieldChange3: function(field, newValue, oldValue, eOpts) {
        Ext.getCmp('CcpPaymentsFilterForm').loadByFilter();
    },

    onDatefieldChange12: function(field, newValue, oldValue, eOpts) {
        Ext.getCmp('CcpPaymentsFilterForm').loadByFilter();
    },

    onDatefieldChange21: function(field, newValue, oldValue, eOpts) {
        Ext.getCmp('CcpPaymentsFilterForm').loadByFilter();
    },

    onDatefieldChange111: function(field, newValue, oldValue, eOpts) {
        Ext.getCmp('CcpPaymentsFilterForm').loadByFilter();
    },

    onComboboxSelect12: function(field, newValue, oldValue, eOpts) {
        Ext.getCmp('CcpPaymentsFilterForm').loadByFilter();
    },

    onComboboxSelect21: function(combo, records, eOpts) {
        Ext.getCmp('CcpPaymentsFilterForm').loadByFilter();
    },

    onTextfieldChange13: function(field, newValue, oldValue, eOpts) {
        Ext.getCmp('CcpPaymentsFilterForm').loadByFilter();
    },

    onCcpHasAdditionalsChange1: function(field, newValue, oldValue, eOpts) {
        Ext.getCmp('CcpPaymentsFilterForm').loadByFilter();
    },

    onCcpHasPaymentsChange1: function(field, newValue, oldValue, eOpts) {
        Ext.getCmp('CcpPaymentsFilterForm').loadByFilter();
    },

    onCheckboxfieldChange8: function(field, newValue, oldValue, eOpts) {
        Ext.getCmp('CcpPaymentsFilterForm').loadByFilter();
    },

    onComboboxSelect14: function(field, newValue, oldValue, eOpts) {
        Ext.getCmp('CcpPaymentsFilterForm').loadByFilter();
    },

    onComboboxAfterRender2: function(component, eOpts) {
        Ext.getStore('CreditsTypePaymentFilter').load({
            callback: function() {
                component.select(Ext.getStore('CreditsTypePaymentFilter').getRange());
            }
        });

    },

    onComboboxSelect211: function(combo, records, eOpts) {
        Ext.getCmp('CcpPaymentsFilterForm').loadByFilter();
    },

    onTextfieldChange1111: function(field, newValue, oldValue, eOpts) {
        Ext.getCmp('CcpPaymentsFilterForm').loadByFilter();
    },

    onComboboxSelect2111: function(combo, records, eOpts) {
        Ext.getCmp('CcpPaymentsFilterForm').loadByFilter();
    },

    onTextfieldChange11111: function(field, newValue, oldValue, eOpts) {
        Ext.getCmp('CcpPaymentsFilterForm').loadByFilter();
    },

    onDatefieldChange2: function(field, newValue, oldValue, eOpts) {
        Ext.getCmp('CcpPaymentsFilterForm').loadByFilter();
    },

    onDatefieldChange4: function(field, newValue, oldValue, eOpts) {
        Ext.getCmp('CcpPaymentsFilterForm').loadByFilter();
    },

    onCcpPaymentsGridItemContextMenu1: function(dataview, record, item, index, e, eOpts) {
        e.stopEvent();
        var newX = e.xy[0],
            newY = e.xy[1];
        Ext.getCmp('CcpPaymentsMn').showAt([newX,newY]);

        if (record.get('receipt_id')) {
            Ext.getCmp('contextCcpPaymentEdit').setDisabled(true);
            //Ext.getCmp('contextCcpPaymentDelete').setDisabled(true);
        } else {
            Ext.getCmp('contextCcpPaymentEdit').setDisabled(false);
            //Ext.getCmp('contextCcpPaymentDelete').setDisabled(false);
        }
    },

    onCcpPaymentsBalanceSumChange: function(field, newValue, oldValue, eOpts) {
        Ext.getCmp('CcpTotaleToolBar').setColors(field, newValue);
        field.setFieldStyle(field.getFieldStyle() + '; text-align: right');
    },

    onComboboxSelect10: function(combo, records, eOpts) {
        Ext.getCmp('CcpReceiptsFilterForm').loadByFilter();
    },

    onDatefieldChange31: function(field, newValue, oldValue, eOpts) {
        Ext.getCmp('CcpReceiptsFilterForm').loadByFilter();
    },

    onDatefieldChange121: function(field, newValue, oldValue, eOpts) {
        Ext.getCmp('CcpReceiptsFilterForm').loadByFilter();
    },

    onTextfieldChange131: function(field, newValue, oldValue, eOpts) {
        Ext.getCmp('CcpReceiptsFilterForm').loadByFilter();
    },

    onTextfieldChange1311: function(field, newValue, oldValue, eOpts) {
        Ext.getCmp('CcpReceiptsFilterForm').loadByFilter();
    },

    onCheckboxfieldChange9: function(field, newValue, oldValue, eOpts) {
        Ext.getCmp('CcpReceiptsFilterForm').loadByFilter();
    },

    onCheckboxfieldChange10: function(field, newValue, oldValue, eOpts) {
        Ext.getCmp('CcpReceiptsFilterForm').loadByFilter();
    },

    onComboboxRender1: function(component, eOpts) {
        Ext.getStore('CcpReceiptAddressCode').load();
    },

    onComboboxChange3: function(field, newValue, oldValue, eOpts) {
        Ext.getCmp('CcpReceiptsFilterForm').loadByFilter();
    },

    onCcpReceiptsGridItemContextMenu: function(dataview, record, item, index, e, eOpts) {
        e.stopEvent();
        var newX = e.xy[0],
            newY = e.xy[1];
        Ext.getCmp('CcpReceiptsMn').showAt([newX,newY]);
    },

    onCcpReceiptsMnShow: function(component, eOpts) {
        setTimeout(function(){
            var record = Ext.getCmp('CcpReceiptsGrid').getSelectionModel().getSelection()[0];

            Ext.getCmp('CcpReceiptPublishMn').show();
            Ext.getCmp('CcpReceiptUnpublishMn').show();



            if (!record.get('publication_path')) Ext.getCmp('CcpReceiptUnpublishMn').hide();

            else Ext.getCmp('CcpReceiptPublishMn').hide();



        }, 200);

    },

    onTextfieldChange4: function(field, newValue, oldValue, eOpts) {

        if(newValue !== oldValue && newValue !== '') {
            Ext.getStore('StudentBalances').load();
        }
    },

    onPanelRender: function(component, eOpts) {
        Ext.getStore('StudentBalances').load();

    },

    onViewItemDblClick1: function(dataview, record, item, index, e, eOpts) {
        var creation = Ext.Date.format(record.get('creation'), 'c');

        Ext.widget('ReminderDetailWin').show();

        Ext.getStore('CcpReminderDetails').load({
            params: {
                creation: creation
            }
        });
    },

    onPanelShow1: function(component, eOpts) {
        Ext.getStore('CcpReminders').load();
    },

    onTabAfterRender: function(component, eOpts) {
        var t = Ext.getCmp('CcpPrintStudentTree');
        t.getRootNode().expand();



    },

    onCheckboxModelSelect: function(rowmodel, record, index, eOpts) {
        /*var permissions = Ext.getCmp('CcpPrintWizardPnl').getPrintWizard()[record.get('id')];


        Ext.getCmp('CcpPrintWizardSchoolYear').disable();
        Ext.getCmp('CcpPrintWizardDate').disable();
        Ext.getCmp('CcpReporParentGrd').disable();
        Ext.getCmp('CcpPrintStudentTree').disable();
        Ext.getCmp('CcpPrintWizardEmpty').disable();
        Ext.getCmp('CcpReportFormatCmb').disable();
        Ext.getCmp('CcpReportCategoryCmb').disable();
        Ext.getCmp('CcpReportColumnsGrd').disable();

        if(permissions.anno_scolastico === true) Ext.getCmp('CcpPrintWizardSchoolYear').enable();
        if(permissions.date === true) Ext.getCmp('CcpPrintWizardDate').enable();
        if(permissions.parenti === true) Ext.getCmp('CcpReporParentGrd').enable();
        if(permissions.studenti === true) Ext.getCmp('CcpPrintStudentTree').enable();
        if(permissions.stampa_elementi_vuoti === true) Ext.getCmp('CcpPrintWizardEmpty').enable();

        if(permissions.tipo_stampa.length > 0) {
            Ext.getCmp('CcpReportFormatCmb').enable();
            Ext.getCmp('CcpReportFormatCmb').expand();


            Ext.getCmp('CcpReportFormatCmb').getStore().removeFilter();
            Ext.getCmp('CcpReportFormatCmb').getStore().filterBy(function(rec, id) {
                return permissions.tipo_stampa.includes(rec.get('field1'));
            });


        }

        if(permissions.raggruppamento.length > 0) {
            Ext.getCmp('CcpReportCategoryCmb').enable();
            Ext.getCmp('CcpReportCategoryCmb').expand();


            Ext.getStore('CcpPrintCategories').removeFilter();
            Ext.getStore('CcpPrintCategories').filterBy(function(rec, id) {
                return permissions.raggruppamento.includes(rec.get('id'));
            });
        }

        if(permissions.colonne.length > 0) {
            Ext.getCmp('CcpReportColumnsGrd').enable();

            Ext.getStore('CcpPrintStudentCols').removeFilter();
            Ext.getStore('CcpPrintStudentCols').filterBy(function(rec, id) {
                return permissions.colonne.includes(rec.get('id'));
            });

        }

        */
        if (record.get('mcType')) {
            Ext.getStore('CcpMcPrintTemplate').load({params: {print_type: record.get('mcType')}});
        } else {
            Ext.getStore('CcpMcPrintTemplate').removeAll();
        }
    },

    onComboboxAfterRender: function(component, eOpts) {
        Ext.getStore('McDbs').filterBy(function(v){
            return v.get('current')===true;
        });
        var year = Ext.getStore('McDbs').getRange()[0];
        Ext.getStore('McDbs').clearFilter();
        if(year) {
            component.select(year);

            // var type_id = Ext.getCmp('CcpMovementTypeId').getValue();
            Ext.getStore('CcpStudentsTree').load({params: {subject_school_year: year.get('name')}});
            Ext.getStore('CcpSubjects').load({params: {subject_school_year: year.get('name')}});
        }
    },

    onComboboxSelect13: function(combo, records, eOpts) {
        var params = {
            student_status: parseInt(Ext.getCmp('CcpReportStudentStatusCmb').getValue()),
            subject_school_year: records[0].get('name')
        };
        Ext.getStore('CcpSubjects').load({params: params});
        Ext.getStore('CcpStudentsTree').load({params: params});
        Ext.getStore('CcpFamilies').load({params: params});
    },

    onComboboxAfterRender1: function(component, eOpts) {
        component.setValue(0);
    },

    onComboboxSelect15: function(combo, records, eOpts) {
        var params = {
            student_status: parseInt(Ext.getCmp('CcpReportStudentStatusCmb').getValue()),
            subject_school_year: Ext.getCmp('CcpPrintWizardSchoolYear').getValue()
        };
        Ext.getStore('CcpSubjects').load({params: params});
        Ext.getStore('CcpStudentsTree').load({params: params});
        Ext.getStore('CcpFamilies').load({params: params});
    },

    onTreepanelCheckChange: function(node, checked, eOpts) {
        mc2ui.app.treePropagateChange(node);
        if(Ext.getCmp('CcpPrintStudentTree').getChecked().length===0) {
            Ext.getCmp('CcpReportSelectParentsStudentsCnt').enable();
            Ext.getCmp('CcpPrintStudentTree').enable();
        } else {
            Ext.getCmp('CcpReportSelectParentsStudentsCnt').disable();
        }
    },

    onTextfieldChange3: function(field, newValue, oldValue, eOpts) {
        Ext.getStore('CcpFamilies').clearFilter();
        Ext.getStore('CcpSubjects').clearFilter();

        if(newValue && newValue !== oldValue) {


             Ext.getStore('CcpFamilies').filterBy(function(rec, id) {
                if(rec.get('text').toLowerCase().indexOf(newValue.toLowerCase()) > -1 ||
                   rec.get('parentele').toLowerCase().indexOf(newValue.toLowerCase()) > -1) {
                    return true;
                }
                else {
                    return false;
                }
            });

            Ext.getStore('CcpSubjects').filterBy(function(rec, id) {
                if(rec.get('display').toLowerCase().indexOf(newValue.toLowerCase()) > -1) {
                    return true;
                }
                else {
                    return false;
                }
            });

        }

    },

    onCcpReportFrmRender: function(component, eOpts) {
        Ext.getStore('CcpFamilies').load({
            params: {
                family: true
            }
        });



        var store = Ext.getStore('CcpPrintWhat'),
            records = store.getRange();

        store.load();
        Ext.each(records,function(r,i) {
            if(mc2ui.app.settings.familyManagementEnabled===true) {
                //if(r.get('id') == 'credits') store.remove(r);
                if(r.get('family') === false) store.remove(r);
            } else {
                if(r.get('family') === true) store.remove(r);
                //if(r.get('id') != 'credits') store.remove(r);
            }
        });


    },

    onDatefieldRender: function(component, eOpts) {
        var dd = new Date();
        component.setValue(dd);
    },

    onComboboxRender: function(component, eOpts) {
        component.setValue('all');
    },

    onComboboxChange2: function(field, newValue, oldValue, eOpts) {
        var dayPressed = Ext.getCmp('CcpCorrispettiviByDayBtn').pressed;

        if(dayPressed) {
            Ext.getStore('Corrispettivi').load({params: {group: 'D', 'subject_school_year': newValue}});
        } else {
            Ext.getStore('Corrispettivi').load({params: {group: 'M', 'subject_school_year': newValue}});
        }
    },

    onTabShow1: function(component, eOpts) {
        var dayPressed = Ext.getCmp('CcpCorrispettiviByDayBtn').pressed,
            ssy = Ext.getCmp('CcpCorrispettiviSchoolYearCmb').getValue();

        if(dayPressed) {
            Ext.getStore('Corrispettivi').load({params: {group: 'D', 'subject_school_year': ssy}});
        } else {
            Ext.getStore('Corrispettivi').load({params: {group: 'M', 'subject_school_year': ssy}});
        }

    },

    onTabShow2: function(tab, eOpts) {
        Ext.getCmp('CcpReportFilterFrm').loadFilterData();
    },

    onViewItemContextMenu3: function(dataview, record, item, index, e, eOpts) {
        e.stopEvent();
        var newX = e.xy[0],
            newY = e.xy[1];
        console.log('SI');
        Ext.getCmp('DocumentReportMn').showAt([newX,newY]);
    },

    onCcpReportFilterFrmAfterRender: function(component, eOpts) {
        component.loadFilterData();
    },

    onPanelRender1: function(component, eOpts) {
        Ext.getStore('CcpReports').load();
    },

    onPanelAfterRender: function(component, eOpts) {
        if(mc2ui.app.settings.username == 'mc2master' || mc2ui.app.settings.username == 'nexus_mc2') {
            component.show();
        }
    },

    onButtonRender: function(component, eOpts) {
        if(!mc2ui.app.settings.discountEnabled) component.hide();
    },

    onCcpPnlBoxReady: function(component, width, height, eOpts) {
        var sy = Ext.getCmp('CcpSchoolYear'),
            ca = Ext.getCmp('CcpCategory'),
            pm = Ext.getCmp('CcpPaymentMethod'),
            pd = Ext.getCmp('CcpPaymentDestination'),
            date = new Date();


        // Main Stores
        sy.getStore().load();
        Ext.getStore('CcpSchoolYearsFilter').load();
        Ext.getStore('CcpCategories').load();
        Ext.getStore('CcpAdditionals').load();
        Ext.getStore('CcpTypes').load();
        Ext.getStore('CcpTypesFilter').load();
        Ext.getStore('CcpPaymentMethods').load();
        Ext.getStore('CcpPaymentDestinations').load();
        Ext.getStore('CcpSubjects').load({
            callback: function() {
                Ext.getStore('CcpStudentsDiscount').add(Ext.getStore('CcpSubjects').getRange());
            }
        });
        Ext.getStore('CcpDepositSlips').load();
        Ext.getStore('CoreBankAccounts').load();
        Ext.getStore('CreditsType').load();
        Ext.getStore('MagisterEsercizi').load();
        Ext.getStore('Indirizzi').load();
        Ext.getStore('Classi').load();
        Ext.getStore('CcpPrintOfCategories').load();


        // Movements Filter Set
        Ext.getCmp('CcpIncoming').select('A');
        Ext.getCmp('CcpCreationDateStart').setValue('01/01/' + date.getFullYear());
        // Ext.getCmp('CcpCreationDateEnd').setValue('31/12/' + date.getFullYear());

        sy.getStore().on('load', function(store, records, options) {
            sy.select('all');
        });

        ca.getStore().on('load', function(store, records, options) {
            ca.select(0);
        });


        // Payments Filter Set
        Ext.getCmp('CcpIncoming1').select('A');
        Ext.getCmp('CcpPSubjectType').select('A');
        Ext.getCmp('CcpPayerType').select('A');
        Ext.getCmp('CcpOperationDateStart').setValue('01/01/' + date.getFullYear());
        Ext.getCmp('CcpOperationDateEnd').setValue('31/12/' + date.getFullYear());

        pm.getStore().on('load', function(store, records, options) {
            //pm.select(0);
        });

        pd.getStore().on('load', function(store, records, options) {
            pd.select(0);
        });


        // Receipts Filter Set
        Ext.getCmp('CcpEmissionDateStart').setValue('01/01/' + date.getFullYear());
        Ext.getCmp('CcpEmissionDateEnd').setValue('31/12/' + date.getFullYear());


    }

});