/*
 * File: app/view/SettingsPanel.js
 *
 * This file was generated by Sencha Architect version 3.2.0.
 * http://www.sencha.com/products/architect/
 *
 * This file requires use of the Ext JS 4.2.x library, under independent license.
 * License of Sencha Architect does not include license for Ext JS 4.2.x. For more
 * details see http://www.sencha.com/license <NAME_EMAIL>.
 *
 * This file will be auto-generated each and everytime you save your project.
 *
 * Do NOT hand edit this file.
 */

Ext.define('mc2ui.view.SettingsPanel', {
    extend: 'Ext.panel.Panel',
    alias: 'widget.SettingsPanel',

    requires: [
        'Ext.tab.Panel',
        'Ext.tab.Tab',
        'Ext.form.Panel',
        'Ext.toolbar.Toolbar',
        'Ext.form.field.Hidden',
        'Ext.form.FieldSet',
        'Ext.form.field.ComboBox',
        'Ext.grid.Panel',
        'Ext.grid.column.Number',
        'Ext.form.field.Checkbox',
        'Ext.grid.column.Action',
        'Ext.grid.View',
        'Ext.toolbar.Spacer',
        'Ext.form.Label',
        'Ext.grid.plugin.RowEditing',
        'Ext.menu.Menu',
        'Ext.menu.Item',
        'Ext.tree.Panel',
        'Ext.tree.View',
        'Ext.tree.Column',
        'Ext.Img',
        'Ext.form.field.File',
        'Ext.form.field.Number',
        'Ext.form.field.HtmlEditor'
    ],

    border: false,
    hidden: true,
    id: 'SettingsPanel',
    itemId: 'SettingsPanel',
    layout: 'fit',
    titleAlign: 'center',

    initComponent: function () {
        var me = this;

        Ext.applyIf(me, {
            items: [
                {
                    xtype: 'tabpanel',
                    permissible: true,
                    id: 'SettingsTabs',
                    itemId: 'SettingsTabs',
                    activeTab: 0,
                    items: [
                        {
                            xtype: 'panel',
                            border: false,
                            id: 'SettingsInstitutesTab',
                            itemId: 'SettingsInstitutesTab',
                            title: 'Istituto',
                            layout: {
                                type: 'vbox',
                                align: 'stretch'
                            },
                            tabConfig: {
                                xtype: 'tab',
                                id: 'SettingsInstitutessMenuTab',
                                itemId: 'SettingsInstitutessMenuTab',
                                iconCls: 'icon-house',
                                textAlign: 'left'
                            },
                            items: [
                                {
                                    xtype: 'form',
                                    flex: 1,
                                    border: false,
                                    id: 'InstituteFrm',
                                    itemId: 'InstituteFrm',
                                    autoScroll: true,
                                    bodyCls: [
                                        'bck-content',
                                        'x-panel-body-default',
                                        'x-box-layout-ct'
                                    ],
                                    bodyPadding: 10,
                                    header: false,
                                    url: '/mc2/applications/core/institutes/update_def.php',
                                    layout: {
                                        type: 'vbox',
                                        align: 'stretch'
                                    },
                                    dockedItems: [
                                        {
                                            xtype: 'toolbar',
                                            permissible: true,
                                            flex: 1,
                                            dock: 'top',
                                            id: 'InstituteToolbar',
                                            itemId: 'InstituteToolbar',
                                            items: [
                                                {
                                                    xtype: 'button',
                                                    handler: function (button, e) {
                                                        Ext.getCmp('InstituteFrm').setLoading();

                                                        Ext.getCmp('InstituteFrm').getForm().submit({
                                                            success: function (a, response) {
                                                                Ext.getCmp('InstituteFrm').setLoading(false);
                                                                mc2ui.app.showNotifySave();
                                                                Ext.getStore('Employees').load();
                                                            }
                                                        });
                                                    },
                                                    id: 'InstituteSaveBtn',
                                                    itemId: 'InstituteSaveBtn',
                                                    iconCls: 'icon-disk',
                                                    text: 'Salva'
                                                },
                                                {
                                                    xtype: 'button',
                                                    handler: function (button, e) {
                                                        Ext.widget('InstitutesWin').show();
                                                    },
                                                    id: 'SettingsInstitutesBtn',
                                                    itemId: 'SettingsInstitutesBtn',
                                                    iconCls: 'icon-neighbourhood',
                                                    text: 'Altri Istituti'
                                                }
                                            ]
                                        }
                                    ],
                                    items: [
                                        {
                                            xtype: 'hiddenfield',
                                            id: 'InstituteId',
                                            itemId: 'InstituteId',
                                            name: 'institute_id'
                                        },
                                        {
                                            xtype: 'hiddenfield',
                                            id: 'InstituteContact',
                                            itemId: 'InstituteContact',
                                            name: 'contact_id'
                                        },
                                        {
                                            xtype: 'container',
                                            layout: {
                                                type: 'hbox',
                                                align: 'stretch'
                                            },
                                            items: [
                                                {
                                                    xtype: 'fieldset',
                                                    flex: 1,
                                                    margin: '0 5 0 0',
                                                    title: 'Anagrafica',
                                                    layout: {
                                                        type: 'vbox',
                                                        align: 'stretch'
                                                    },
                                                    items: [
                                                        {
                                                            xtype: 'textfield',
                                                            id: 'InstituteName',
                                                            itemId: 'InstituteName',
                                                            fieldLabel: 'Nome',
                                                            labelAlign: 'right',
                                                            name: 'name'
                                                        },
                                                        {
                                                            xtype: 'textfield',
                                                            id: 'InstituteMechanCode',
                                                            itemId: 'InstituteMechanCode',
                                                            fieldLabel: 'Meccanografico',
                                                            labelAlign: 'right',
                                                            name: 'mechan_code'
                                                        },
                                                        {
                                                            xtype: 'textfield',
                                                            flex: 1,
                                                            fieldLabel: 'Codice IPA',
                                                            labelAlign: 'right',
                                                            name: 'ipa_code'
                                                        },
                                                        {
                                                            xtype: 'combobox',
                                                            id: 'InstituteType',
                                                            itemId: 'InstituteType',
                                                            fieldLabel: 'Tipologia',
                                                            labelAlign: 'right',
                                                            name: 'school_type',
                                                            displayField: 'description',
                                                            store: 'SchoolTypes',
                                                            valueField: 'school_code'
                                                        },
                                                        {
                                                            xtype: 'textfield',
                                                            id: 'InstituteAddress',
                                                            itemId: 'InstituteAddress',
                                                            fieldLabel: 'Indirizzo',
                                                            labelAlign: 'right',
                                                            name: 'address'
                                                        },
                                                        {
                                                            xtype: 'combobox',
                                                            id: 'InstituteCity',
                                                            itemId: 'InstituteCity',
                                                            fieldLabel: 'Città',
                                                            labelAlign: 'right',
                                                            name: 'city_id',
                                                            hideTrigger: true,
                                                            displayField: 'description',
                                                            forceSelection: true,
                                                            queryMode: 'local',
                                                            store: 'CoreCities',
                                                            typeAhead: true,
                                                            valueField: 'city_id'
                                                        },
                                                        {
                                                            xtype: 'textfield',
                                                            id: 'InstituteCap',
                                                            itemId: 'InstituteCap',
                                                            fieldLabel: 'CAP',
                                                            labelAlign: 'right',
                                                            name: 'cap'
                                                        },
                                                        {
                                                            xtype: 'container',
                                                            flex: 1,
                                                            layout: {
                                                                type: 'hbox',
                                                                align: 'stretch'
                                                            },
                                                            items: [
                                                                {
                                                                    xtype: 'textfield',
                                                                    flex: 1,
                                                                    id: 'InstituteFiscalCode',
                                                                    itemId: 'InstituteFiscalCode',
                                                                    fieldLabel: 'Cod. Fiscale',
                                                                    labelAlign: 'right',
                                                                    name: 'school_fiscal_code'
                                                                },
                                                                {
                                                                    xtype: 'textfield',
                                                                    flex: 1,
                                                                    id: 'InstitutePIVA',
                                                                    itemId: 'InstitutePIVA',
                                                                    margin: '0 0 0 5',
                                                                    fieldLabel: 'Partita IVA',
                                                                    labelAlign: 'right',
                                                                    name: 'fiscal_code'
                                                                }
                                                            ]
                                                        },
                                                        {
                                                            xtype: 'container',
                                                            flex: 1,
                                                            margins: '5 0 5 0',
                                                            layout: {
                                                                type: 'hbox',
                                                                align: 'stretch'
                                                            },
                                                            items: [
                                                                {
                                                                    xtype: 'textfield',
                                                                    flex: 1,
                                                                    id: 'InstitutePhoneNum',
                                                                    itemId: 'InstitutePhoneNum',
                                                                    fieldLabel: 'Telefono',
                                                                    labelAlign: 'right',
                                                                    name: 'phone_num'
                                                                },
                                                                {
                                                                    xtype: 'textfield',
                                                                    flex: 1,
                                                                    margins: '0 0 0 5',
                                                                    id: 'InstituteFax',
                                                                    itemId: 'InstituteFax',
                                                                    fieldLabel: 'Fax',
                                                                    labelAlign: 'right',
                                                                    name: 'fax'
                                                                }
                                                            ]
                                                        },
                                                        {
                                                            xtype: 'textfield',
                                                            id: 'InstituteEmail',
                                                            itemId: 'InstituteEmail',
                                                            fieldLabel: 'Email',
                                                            labelAlign: 'right',
                                                            name: 'email'
                                                        },
                                                        {
                                                            xtype: 'textfield',
                                                            id: 'InstituteAdeEmail',
                                                            itemId: 'InstituteAdeEmail',
                                                            fieldLabel: 'Email per comunicazione AdE',
                                                            labelAlign: 'right',
                                                            name: 'ade_email'
                                                        },
                                                        {
                                                            xtype: 'textfield',
                                                            id: 'InstituteWeb',
                                                            itemId: 'InstituteWeb',
                                                            fieldLabel: 'Web',
                                                            labelAlign: 'right',
                                                            name: 'web'
                                                        }
                                                    ]
                                                },
                                                {
                                                    xtype: 'fieldset',
                                                    flex: 1,
                                                    margin: '0 0 0 5',
                                                    title: 'Ruoli',
                                                    layout: {
                                                        type: 'vbox',
                                                        align: 'stretch'
                                                    },
                                                    items: [
                                                        {
                                                            xtype: 'combobox',
                                                            id: 'InstituteRolePreside',
                                                            itemId: 'InstituteRolePreside',
                                                            fieldLabel: 'Preside',
                                                            labelAlign: 'right',
                                                            name: 'job_director_id',
                                                            hideTrigger: true,
                                                            displayField: 'denomination',
                                                            forceSelection: true,
                                                            queryMode: 'local',
                                                            store: 'Employees',
                                                            typeAhead: true,
                                                            valueField: 'employee_id'
                                                        },
                                                        {
                                                            xtype: 'combobox',
                                                            id: 'InstituteRoleVicePreside',
                                                            itemId: 'InstituteRoleVicePreside',
                                                            fieldLabel: 'Vice Preside',
                                                            labelAlign: 'right',
                                                            name: 'job_vice_director_id',
                                                            hideTrigger: true,
                                                            displayField: 'denomination',
                                                            forceSelection: true,
                                                            queryMode: 'local',
                                                            store: 'Employees',
                                                            typeAhead: true,
                                                            valueField: 'employee_id'
                                                        },
                                                        {
                                                            xtype: 'combobox',
                                                            id: 'InstituteRoleDSGA',
                                                            itemId: 'InstituteRoleDSGA',
                                                            fieldLabel: 'DSGA',
                                                            labelAlign: 'right',
                                                            name: 'job_dsga_id',
                                                            hideTrigger: true,
                                                            displayField: 'denomination',
                                                            forceSelection: true,
                                                            queryMode: 'local',
                                                            store: 'Employees',
                                                            typeAhead: true,
                                                            valueField: 'employee_id'
                                                        },
                                                        {
                                                            xtype: 'combobox',
                                                            id: 'InstituteRoleSegreteria',
                                                            itemId: 'InstituteRoleSegreteria',
                                                            fieldLabel: 'Resp. Segreteria',
                                                            labelAlign: 'right',
                                                            name: 'job_personnel_id',
                                                            hideTrigger: true,
                                                            displayField: 'denomination',
                                                            forceSelection: true,
                                                            queryMode: 'local',
                                                            store: 'Employees',
                                                            typeAhead: true,
                                                            valueField: 'employee_id'
                                                        },
                                                        {
                                                            xtype: 'combobox',
                                                            id: 'InstituteRoleContabilita',
                                                            itemId: 'InstituteRoleContabilita',
                                                            fieldLabel: 'Resp. Contabilità',
                                                            labelAlign: 'right',
                                                            name: 'job_accounting_id',
                                                            hideTrigger: true,
                                                            displayField: 'denomination',
                                                            forceSelection: true,
                                                            queryMode: 'local',
                                                            store: 'Employees',
                                                            typeAhead: true,
                                                            valueField: 'employee_id'
                                                        },
                                                        {
                                                            xtype: 'combobox',
                                                            id: 'InstituteRoleAcquisti',
                                                            itemId: 'InstituteRoleAcquisti',
                                                            fieldLabel: 'Resp. Acquisti',
                                                            labelAlign: 'right',
                                                            name: 'job_warehouse_id',
                                                            hideTrigger: true,
                                                            displayField: 'denomination',
                                                            forceSelection: true,
                                                            queryMode: 'local',
                                                            store: 'Employees',
                                                            typeAhead: true,
                                                            valueField: 'employee_id'
                                                        },
                                                        {
                                                            xtype: 'fieldset',
                                                            title: 'Resp. Protocollo',
                                                            layout: {
                                                                type: 'hbox',
                                                                align: 'stretch'
                                                            },
                                                            padding: 5,
                                                            items: [
                                                                {
                                                                    xtype: 'textfield',
                                                                    fieldLabel: 'Nome',
                                                                    labelAlign: 'right',
                                                                    name: 'protocol_manager_name',
                                                                    flex: 1
                                                                }, {
                                                                    xtype: 'textfield',
                                                                    fieldLabel: 'Cognome',
                                                                    labelAlign: 'right',
                                                                    name: 'protocol_manager_surname',
                                                                    flex: 1
                                                                }, {
                                                                    xtype: 'textfield',
                                                                    fieldLabel: 'Codice fiscale',
                                                                    labelAlign: 'right',
                                                                    name: 'protocol_manager_fiscal_code',
                                                                    flex: 1
                                                                },

                                                            ]
                                                        }
                                                    ]
                                                }
                                            ]
                                        },
                                        {
                                            xtype: 'fieldset',
                                            flex: 1,
                                            minHeight: 200,
                                            title: 'Posta elettronica',
                                            layout: {
                                                type: 'vbox',
                                                align: 'stretch'
                                            },
                                            items: [
                                                {
                                                    xtype: 'gridpanel',
                                                    flex: 1,
                                                    id: 'ArchiveMailAccountGrid',
                                                    itemId: 'ArchiveMailAccountGrid',
                                                    title: '',
                                                    store: 'ArchiveMailAccounts',
                                                    columns: [
                                                        {
                                                            xtype: 'gridcolumn',
                                                            dataIndex: 'name',
                                                            text: 'Nome',
                                                            flex: 1,
                                                            editor: {
                                                                xtype: 'textfield',
                                                                name: 'name'
                                                            }
                                                        },
                                                        {
                                                            xtype: 'gridcolumn',
                                                            dataIndex: 'protocol_text',
                                                            text: 'Protocollo'
                                                        },
                                                        {
                                                            xtype: 'gridcolumn',
                                                            dataIndex: 'host',
                                                            text: 'Host'
                                                        },
                                                        {
                                                            xtype: 'numbercolumn',
                                                            dataIndex: 'port',
                                                            text: 'Porta',
                                                            format: '0'
                                                        },
                                                        {
                                                            xtype: 'gridcolumn',
                                                            dataIndex: 'security_text',
                                                            text: 'Sicurezza'
                                                        },
                                                        {
                                                            xtype: 'gridcolumn',
                                                            dataIndex: 'username',
                                                            text: 'Username'
                                                        },
                                                        {
                                                            xtype: 'gridcolumn',
                                                            renderer: function (value, metaData, record, rowIndex, colIndex, store, view) {
                                                                return '*****';
                                                            },
                                                            dataIndex: 'password',
                                                            text: 'Password',
                                                            editor: {
                                                                xtype: 'textfield',
                                                                name: 'password',
                                                                inputType: 'password'
                                                            }
                                                        },
                                                        {
                                                            xtype: 'gridcolumn',
                                                            renderer: function (value, metaData, record, rowIndex, colIndex, store, view) {
                                                                if (value === true) {
                                                                    return 'Si';
                                                                }
                                                                return 'No';
                                                            },
                                                            dataIndex: 'active',
                                                            text: 'Attivo',
                                                            editor: {
                                                                xtype: 'checkboxfield',
                                                                name: 'active'
                                                            }
                                                        },
                                                        {
                                                            xtype: 'actioncolumn',
                                                            width: 40,
                                                            dataIndex: 'out',
                                                            items: [
                                                                {
                                                                    getClass: function (v, metadata, r, rowIndex, colIndex, store) {
                                                                        if (v === true) {
                                                                            return 'icon-arrow_up';
                                                                        }
                                                                        return 'icon-arrow_down';
                                                                    }
                                                                }
                                                            ]
                                                        }
                                                    ],
                                                    dockedItems: [
                                                        {
                                                            xtype: 'toolbar',
                                                            dock: 'top',
                                                            items: [
                                                                {
                                                                    xtype: 'tbspacer',
                                                                    flex: 1
                                                                },
                                                                {
                                                                    xtype: 'label',
                                                                    html: '<i style="color:red">Questa sezione è esclusivamente in lettura. Per la configurazione degli account di posta rivolgersi all\'assistenza. La modifica è prevista solo per i campi editabili facendo doppio click sull\'account</i>',
                                                                    text: ''
                                                                },
                                                                {
                                                                    xtype: 'tbspacer',
                                                                    flex: 1
                                                                }
                                                            ]
                                                        }
                                                    ],
                                                    plugins: [
                                                        Ext.create('Ext.grid.plugin.RowEditing', {
                                                            listeners: {
                                                                edit: {
                                                                    fn: me.onRowEditingEdit,
                                                                    scope: me
                                                                },
                                                                canceledit: {
                                                                    fn: me.onRowEditingCanceledit,
                                                                    scope: me
                                                                }
                                                            }
                                                        })
                                                    ]
                                                }
                                            ]
                                        },
                                        {
                                            xtype: 'fieldset',
                                            flex: 1,
                                            minHeight: 200,
                                            width: 400,
                                            title: 'Contabilità',
                                            layout: {
                                                type: 'vbox',
                                                align: 'stretch'
                                            },
                                            items: [
                                                {
                                                    xtype: 'container',
                                                    hidden: true,
                                                    items: [
                                                        {
                                                            xtype: 'textfield',
                                                            disabled: true,
                                                            id: 'InstituteCCPostal',
                                                            itemId: 'InstituteCCPostal',
                                                            fieldLabel: 'CC postale',
                                                            labelAlign: 'right',
                                                            name: 'postal_account',
                                                            fieldStyle: 'text-align: right',
                                                            size: 25
                                                        }
                                                    ]
                                                },
                                                {
                                                    xtype: 'gridpanel',
                                                    permissible: true,
                                                    flex: 1,
                                                    id: 'InstituteBankProfile',
                                                    itemId: 'InstituteBankProfile',
                                                    margin: '0 0 5 0',
                                                    title: 'Banche/Conti Correnti',
                                                    titleAlign: 'center',
                                                    emptyText: 'Nessun conto corrente abbinato.',
                                                    enableColumnHide: false,
                                                    enableColumnMove: false,
                                                    enableColumnResize: false,
                                                    sortableColumns: false,
                                                    store: 'CoreBankAccounts',
                                                    dockedItems: [
                                                        {
                                                            xtype: 'toolbar',
                                                            permissible: true,
                                                            dock: 'top',
                                                            id: 'InstituteBanksToolbar',
                                                            itemId: 'InstituteBanksToolbar',
                                                            items: [
                                                                {
                                                                    xtype: 'button',
                                                                    handler: function (button, event) {
                                                                        var instituteId = Ext.getCmp('InstituteId').getValue(),
                                                                            as = Ext.getStore('CoreBankAccounts'),
                                                                            pg = Ext.getCmp('InstituteBankProfile');

                                                                        as.insert(0, { ise_id: instituteId, ise_type: 'I', type: 'B' });

                                                                        pg.getPlugin().blocked = false;
                                                                        pg.getPlugin().startEdit(0, 0);
                                                                        pg.getPlugin().blocked = true;
                                                                    },
                                                                    id: 'InstituteBankProfileAddBtn',
                                                                    itemId: 'InstituteBankProfileAddBtn',
                                                                    iconCls: 'icon-add',
                                                                    text: 'Nuovo'
                                                                }
                                                            ]
                                                        }
                                                    ],
                                                    viewConfig: {
                                                        listeners: {
                                                            itemcontextmenu: {
                                                                fn: me.onViewItemContextMenu1,
                                                                scope: me
                                                            }
                                                        }
                                                    },
                                                    columns: [
                                                        {
                                                            xtype: 'gridcolumn',
                                                            dataIndex: 'denomination',
                                                            text: 'Nome',
                                                            flex: 1,
                                                            editor: {
                                                                xtype: 'textfield',
                                                                allowBlank: false,
                                                                allowOnlyWhitespace: false
                                                            }
                                                        },
                                                        {
                                                            xtype: 'gridcolumn',
                                                            width: 152,
                                                            dataIndex: 'creditor_identifier',
                                                            text: 'Identificativo creditore',
                                                            editor: {
                                                                xtype: 'textfield'
                                                            }
                                                        },
                                                        {
                                                            xtype: 'gridcolumn',
                                                            renderer: function (value, metaData, record, rowIndex, colIndex, store, view) {
                                                                switch (value) {
                                                                    case 'P':
                                                                        return 'Postale';
                                                                    case 'B':
                                                                        return 'Bancario';
                                                                    default:
                                                                        return '-';
                                                                }
                                                            },
                                                            width: 85,
                                                            dataIndex: 'type',
                                                            text: 'Tipologia',
                                                            editor: {
                                                                xtype: 'combobox',
                                                                allowBlank: false,
                                                                allowOnlyWhitespace: false,
                                                                editable: false,
                                                                forceSelection: true,
                                                                queryMode: 'local',
                                                                store: 'CoreBankAccountTypes',
                                                                valueField: 'id'
                                                            }
                                                        },
                                                        {
                                                            xtype: 'gridcolumn',
                                                            dataIndex: 'cuc',
                                                            text: 'CUC/CBI',
                                                            editor: {
                                                                xtype: 'textfield'
                                                            }
                                                        },
                                                        {
                                                            xtype: 'gridcolumn',
                                                            width: 85,
                                                            resizable: false,
                                                            align: 'center',
                                                            dataIndex: 'country_code',
                                                            text: 'Cod. paese',
                                                            editor: {
                                                                xtype: 'textfield',
                                                                emptyText: 'IT',
                                                                maxLength: 2,
                                                                minLength: 2,
                                                                size: 2
                                                            }
                                                        },
                                                        {
                                                            xtype: 'gridcolumn',
                                                            width: 85,
                                                            resizable: false,
                                                            align: 'center',
                                                            dataIndex: 'check_code',
                                                            text: 'Cod. controllo',
                                                            editor: {
                                                                xtype: 'textfield',
                                                                emptyText: '00',
                                                                maxLength: 2,
                                                                minLength: 2,
                                                                size: 2
                                                            }
                                                        },
                                                        {
                                                            xtype: 'gridcolumn',
                                                            width: 200,
                                                            dataIndex: 'bban',
                                                            text: 'Bban',
                                                            editor: {
                                                                xtype: 'textfield',
                                                                allowBlank: false,
                                                                allowOnlyWhitespace: false
                                                            }
                                                        }
                                                    ],
                                                    plugins: [
                                                        Ext.create('Ext.grid.plugin.RowEditing', {
                                                            blocked: true,
                                                            listeners: {
                                                                edit: {
                                                                    fn: me.onRowEditingEdit1,
                                                                    scope: me
                                                                },
                                                                canceledit: {
                                                                    fn: me.onRowEditingCanceledit1,
                                                                    scope: me
                                                                },
                                                                beforeedit: {
                                                                    fn: me.onRowEditingBeforeEdit,
                                                                    scope: me
                                                                }
                                                            }
                                                        })
                                                    ]
                                                },
                                                {
                                                    xtype: 'menu',
                                                    permissible: true,
                                                    hidden: true,
                                                    id: 'InstituteBankProfileMn',
                                                    itemId: 'InstituteBankProfileMn',
                                                    items: [
                                                        {
                                                            xtype: 'menuitem',
                                                            handler: function (item, e) {
                                                                var pg = Ext.getCmp('InstituteBankProfile'),
                                                                    record = pg.getSelectionModel().getSelection()[0];

                                                                pg.getPlugin().blocked = false;
                                                                pg.getPlugin().startEdit(record);
                                                                pg.getPlugin().blocked = true;
                                                            },
                                                            id: 'contextInstituteBankProfileEdit',
                                                            itemId: 'contextInstituteBankProfileEdit',
                                                            iconCls: 'icon-pencil',
                                                            text: 'Modifica'
                                                        },
                                                        {
                                                            xtype: 'menuitem',
                                                            handler: function (item, e) {
                                                                var record = Ext.getCmp('InstituteBankProfile').getSelectionModel().getSelection()[0];

                                                                Ext.Msg.show({
                                                                    title: record.get('denomination'),
                                                                    msg: 'Sei sicuro di voler eliminare questo conto?',
                                                                    buttons: Ext.Msg.YESNO,
                                                                    fn: function (r) {
                                                                        if (r == 'yes') {
                                                                            store = Ext.getStore('CoreBankAccounts');
                                                                            store.remove(record);
                                                                            store.sync();
                                                                        }
                                                                    }
                                                                });
                                                            },
                                                            id: 'contextInstituteBankProfileDelete',
                                                            itemId: 'contextInstituteBankProfileDelete',
                                                            iconCls: 'icon-cancel',
                                                            text: 'Elimina'
                                                        }
                                                    ]
                                                }
                                            ]
                                        }
                                    ]
                                }
                            ]
                        },
                        {
                            xtype: 'panel',
                            permissible: true,
                            border: false,
                            id: 'SettingsUsersTab',
                            itemId: 'SettingsUsersTab',
                            layout: 'fit',
                            title: 'Utenti',
                            tabConfig: {
                                xtype: 'tab',
                                id: 'SettingsUsersMenuTab',
                                itemId: 'SettingsUsersMenuTab',
                                iconCls: 'icon-user_key',
                                textAlign: 'left'
                            },
                            items: [
                                {
                                    xtype: 'gridpanel',
                                    border: false,
                                    id: 'UsersGrid',
                                    itemId: 'UsersGrid',
                                    header: false,
                                    store: 'SettingsUsers',
                                    viewConfig: {
                                        listeners: {
                                            itemcontextmenu: {
                                                fn: me.onGridviewItemContextMenu1,
                                                scope: me
                                            }
                                        }
                                    },
                                    columns: [
                                        {
                                            xtype: 'gridcolumn',
                                            width: 202,
                                            dataIndex: 'user_name',
                                            hideable: false,
                                            text: 'Nome utente',
                                        },
                                        {
                                            xtype: 'gridcolumn',
                                            renderer: function (value, metaData, record, rowIndex, colIndex, store, view) {
                                                var res = '';
                                                if (record.get('surname')) res += record.get('surname');
                                                if (record.get('name')) res += ' ' + record.get('name');
                                                return res;
                                            },
                                            hideable: false,
                                            flex: 1,
                                            text: 'Nome'
                                        },
                                        {
                                            xtype: 'gridcolumn',
                                            dataIndex: 'fiscal_code',
                                            hideable: false,
                                            text: 'Codice fiscale',
                                            width: 200
                                        },
                                        {
                                            xtype: 'gridcolumn',
                                            width: 225,
                                            dataIndex: 'user_type_str',
                                            hideable: false,
                                            text: 'Gruppo'
                                        },
                                        {
                                            xtype: 'gridcolumn',
                                            width: 189,
                                            dataIndex: 'email',
                                            text: 'Email'
                                        },
                                        {
                                            xtype: 'gridcolumn',
                                            renderer: function (value, metaData, record, rowIndex, colIndex, store, view) {
                                                if (value > 0) {
                                                    icon = './resources/icons/accept.png';
                                                    return '<img src="' + icon + '" />';
                                                }
                                                return '';
                                            },
                                            resizable: false,
                                            align: 'center',
                                            dataIndex: 'enabled',
                                            hideable: false,
                                            text: 'Attivo'
                                        },
                                        {
                                            xtype: 'gridcolumn',
                                            renderer: function (value, metaData, record, rowIndex, colIndex, store, view) {
                                                if (value > 0) {
                                                    icon = './resources/icons/accept.png';
                                                    return '<img src="' + icon + '" />';
                                                }
                                                return '';
                                            },
                                            resizable: false,
                                            align: 'center',
                                            dataIndex: 'privelege',
                                            hideable: false,
                                            text: 'Amministratore'
                                        },
                                        {
                                            xtype: 'gridcolumn',
                                            renderer: function (value, metaData, record, rowIndex, colIndex, store, view) {
                                                if (value > 0) {
                                                    icon = './resources/icons/accept.png';
                                                    return '<img src="' + icon + '" />';
                                                }
                                                return '';
                                            },
                                            hidden: true,
                                            width: 123,
                                            resizable: false,
                                            align: 'center',
                                            dataIndex: 'modify_protocol',
                                            text: 'Modifica Protocollo'
                                        }
                                    ],
                                    dockedItems: [
                                        {
                                            xtype: 'toolbar',
                                            dock: 'top',
                                            width: 743,
                                            items: [
                                                {
                                                    xtype: 'button',
                                                    handler: function (button, e) {
                                                        Ext.widget('UserEditWin').show();
                                                    },
                                                    id: 'UserAddBtn',
                                                    itemId: 'UserAddBtn',
                                                    iconCls: 'icon-add',
                                                    text: 'Nuovo'
                                                }
                                            ]
                                        }
                                    ]
                                },
                                {
                                    xtype: 'menu',
                                    permissible: true,
                                    id: 'UsersEditMn',
                                    itemId: 'UsersEditMn',
                                    items: [
                                        {
                                            xtype: 'menuitem',
                                            handler: function (item, e) {
                                                Ext.widget('UserEditWin').show();

                                                var form = Ext.getCmp('EditUserFrm').getForm(),
                                                    combo = Ext.getCmp('user_type'),
                                                    record = Ext.getCmp('UsersGrid').getSelectionModel().getSelection()[0];


                                                form.loadRecord(record);

                                                Ext.getCmp('user_password').disable();
                                                Ext.getCmp('user_password_r').disable();

                                                if (parseInt(record.raw.user_type) > 0) {
                                                    combo.setValue(parseInt(record.raw.user_type));
                                                } else {
                                                    combo.reset();
                                                }
                                            },
                                            id: 'contextUserEdit',
                                            itemId: 'contextUserEdit',
                                            iconCls: 'icon-pencil',
                                            text: 'Modifica'
                                        },
                                        {
                                            xtype: 'menuitem',
                                            handler: function (item, e) {
                                                Ext.widget('PaswdEditWin').show();
                                            },
                                            id: 'contextUserPassword',
                                            itemId: 'contextUserPassword',
                                            iconCls: 'icon-key',
                                            text: 'Cambia password'
                                        },
                                        {
                                            xtype: 'menuitem',
                                            handler: function (item, e) {
                                                var record = Ext.getCmp('UsersGrid').getSelectionModel().getSelection()[0];

                                                Ext.Msg.show({
                                                    title: record.raw.group_name,
                                                    msg: 'Sei sicuro di voler eliminare questo utente?',
                                                    buttons: Ext.Msg.YESNO,
                                                    fn: function (r) {
                                                        if (r == 'yes') {
                                                            store = Ext.getStore('SettingsUsers');
                                                            store.remove(record);
                                                            store.sync();
                                                        }
                                                    }
                                                });
                                            },
                                            id: 'contextUserDelete',
                                            itemId: 'contextUserDelete',
                                            iconCls: 'icon-cancel',
                                            text: 'Elimina'
                                        }
                                    ]
                                }
                            ]
                        },
                        {
                            xtype: 'panel',
                            border: false,
                            id: 'SettingsGroupsTab',
                            itemId: 'SettingsGroupsTab',
                            layout: 'border',
                            title: 'Gruppi',
                            tabConfig: {
                                xtype: 'tab',
                                id: 'SettingsGroupsMenuTab',
                                itemId: 'SettingsGroupsMenuTab',
                                iconCls: 'icon-group_key',
                                textAlign: 'left'
                            },
                            items: [
                                {
                                    xtype: 'gridpanel',
                                    permissible: true,
                                    region: 'west',
                                    split: true,
                                    id: 'GroupsPnl',
                                    itemId: 'GroupsPnl',
                                    width: 200,
                                    title: 'Elenco gruppi',
                                    titleCollapse: true,
                                    emptyText: 'Nessun gruppo creato',
                                    enableColumnHide: false,
                                    enableColumnMove: false,
                                    enableColumnResize: false,
                                    hideHeaders: true,
                                    sortableColumns: false,
                                    store: 'SettingsGroups',
                                    viewConfig: {
                                        listeners: {
                                            itemclick: {
                                                fn: me.onGridviewItemClick,
                                                scope: me
                                            },
                                            itemcontextmenu: {
                                                fn: me.onGridviewItemContextMenu2,
                                                scope: me
                                            }
                                        }
                                    },
                                    columns: [
                                        {
                                            xtype: 'gridcolumn',
                                            dataIndex: 'group_name',
                                            text: 'Nome',
                                            flex: 1
                                        }
                                    ],
                                    dockedItems: [
                                        {
                                            xtype: 'toolbar',
                                            dock: 'top',
                                            width: 743,
                                            items: [
                                                {
                                                    xtype: 'button',
                                                    handler: function (button, e) {
                                                        Ext.widget('GroupEditWin').show();
                                                    },
                                                    id: 'GroupAddBtn',
                                                    itemId: 'GroupAddBtn',
                                                    iconCls: 'icon-add',
                                                    text: 'Nuovo'
                                                }
                                            ]
                                        }
                                    ]
                                },
                                {
                                    xtype: 'treepanel',
                                    flex: 1,
                                    region: 'center',
                                    split: true,
                                    id: 'PermissionsPanel',
                                    itemId: 'PermissionsPanel',
                                    title: 'Nessun gruppo selezionato',
                                    emptyText: 'Nessun permesso disponibile',
                                    enableColumnHide: false,
                                    enableColumnMove: false,
                                    store: 'Permissions',
                                    displayField: 'name',
                                    rootVisible: false,
                                    singleExpand: true,
                                    viewConfig: {

                                    },
                                    columns: [
                                        {
                                            xtype: 'treecolumn',
                                            width: 300,
                                            dataIndex: 'name',
                                            text: 'Operazione'
                                        },
                                        {
                                            xtype: 'gridcolumn',
                                            renderer: function (value, metaData, record, rowIndex, colIndex, store, view) {
                                                if (record.data.leaf !== true) {
                                                    return '';
                                                } else {
                                                    icon = './resources/icons/delete.png';
                                                    if (value === true) {
                                                        icon = './resources/icons/accept.png';
                                                    }
                                                    return '<img src="' + icon + '" />';
                                                }
                                            },
                                            dataIndex: 'allow',
                                            text: 'Consentito',
                                            flex: 1
                                        }
                                    ],
                                    listeners: {
                                        itemcontextmenu: {
                                            fn: me.onPermissionsPanelItemContextMenu,
                                            scope: me
                                        }
                                    }
                                },
                                {
                                    xtype: 'menu',
                                    permissible: true,
                                    region: 'east',
                                    id: 'GroupsEditMn',
                                    itemId: 'GroupsEditMn',
                                    items: [
                                        {
                                            xtype: 'menuitem',
                                            handler: function (item, e) {
                                                Ext.widget('GroupEditWin').show();
                                                var form = Ext.getCmp('EditGroupFrm').getForm();
                                                var record = Ext.getCmp('GroupsPnl').getSelectionModel().getSelection()[0];
                                                form.loadRecord(record);
                                            },
                                            id: 'contextPermissionEdit',
                                            itemId: 'contextPermissionEdit',
                                            iconCls: 'icon-pencil',
                                            text: 'Modifica'
                                        },
                                        {
                                            xtype: 'menuitem',
                                            handler: function (item, e) {
                                                var record = Ext.getCmp('GroupsPnl').getSelectionModel().getSelection()[0];

                                                Ext.Msg.show({
                                                    title: record.raw.group_name,
                                                    msg: 'Sei sicuro di voler eliminare questo elemento?',
                                                    buttons: Ext.Msg.YESNO,
                                                    fn: function (r) {
                                                        if (r == 'yes') {
                                                            store = Ext.getStore('SettingsGroups');
                                                            store.remove(record);
                                                            store.sync();
                                                        }
                                                    }
                                                });
                                            },
                                            id: 'contextPermissionDelete',
                                            itemId: 'contextPermissionDelete',
                                            iconCls: 'icon-cancel',
                                            text: 'Elimina'
                                        }
                                    ]
                                },
                                {
                                    xtype: 'menu',
                                    permissible: true,
                                    region: 'east',
                                    id: 'PermissionsEditMn',
                                    itemId: 'PermissionsEditMn',
                                    items: [
                                        {
                                            xtype: 'menuitem',
                                            handler: function (item, e) {
                                                var record = Ext.getCmp('PermissionsPanel').getSelectionModel().getSelection()[0],
                                                    group = Ext.getCmp('GroupsPnl').getSelectionModel().getSelection()[0].raw.gid,
                                                    store = Ext.getStore('Permissions');

                                                record.set('allow', !record.get('allow'));
                                            },
                                            id: 'contextPermissionToggle',
                                            itemId: 'contextPermissionToggle',
                                            iconCls: 'icon-key',
                                            text: 'Modifica permesso'
                                        }
                                    ]
                                }
                            ]
                        },
                        {
                            xtype: 'panel',
                            showFunc: function () {
                                var store = Ext.getStore('CoreParameter'),
                                    hl = store.getAt(store.find('name', 'SETTINGS_PRINTS_HEADER_LOGO')).get('value'),
                                    hbg = store.getAt(store.find('name', 'INVOICE_BG_IMG')).get('value'),
                                    h1 = store.getAt(store.find('name', 'SETTINGS_PRINTS_HEADER_1ROW_TEXT')).get('value'),
                                    h1a = store.getAt(store.find('name', 'SETTINGS_PRINTS_HEADER_1ROW_ALIGNMENT')).get('value'),
                                    h2 = store.getAt(store.find('name', 'SETTINGS_PRINTS_HEADER_2ROW_TEXT')).get('value'),
                                    h2a = store.getAt(store.find('name', 'SETTINGS_PRINTS_HEADER_2ROW_ALIGNMENT')).get('value'),
                                    h3 = store.getAt(store.find('name', 'SETTINGS_PRINTS_HEADER_3ROW_TEXT')).get('value'),
                                    h3a = store.getAt(store.find('name', 'SETTINGS_PRINTS_HEADER_3ROW_ALIGNMENT')).get('value'),
                                    h4 = store.getAt(store.find('name', 'SETTINGS_PRINTS_HEADER_4ROW_TEXT')).get('value'),
                                    h4a = store.getAt(store.find('name', 'SETTINGS_PRINTS_HEADER_4ROW_ALIGNMENT')).get('value'),
                                    f1 = store.getAt(store.find('name', 'SETTINGS_PRINTS_FOOTER_1ROW_TEXT')).get('value'),
                                    f1a = store.getAt(store.find('name', 'SETTINGS_PRINTS_FOOTER_1ROW_ALIGNMENT')).get('value'),
                                    f2 = store.getAt(store.find('name', 'SETTINGS_PRINTS_FOOTER_2ROW_TEXT')).get('value'),
                                    f2a = store.getAt(store.find('name', 'SETTINGS_PRINTS_FOOTER_2ROW_ALIGNMENT')).get('value'),
                                    f3 = store.getAt(store.find('name', 'SETTINGS_PRINTS_FOOTER_3ROW_TEXT')).get('value'),
                                    f3a = store.getAt(store.find('name', 'SETTINGS_PRINTS_FOOTER_3ROW_ALIGNMENT')).get('value'),
                                    f4 = store.getAt(store.find('name', 'SETTINGS_PRINTS_FOOTER_4ROW_TEXT')).get('value'),
                                    f4a = store.getAt(store.find('name', 'SETTINGS_PRINTS_FOOTER_4ROW_ALIGNMENT')).get('value'),
                                    fps = store.getAt(store.find('name', 'SETTINGS_PRINTS_FOOTER_PAGENUMBER_TEXT')).get('value'),
                                    fpa = store.getAt(store.find('name', 'SETTINGS_PRINTS_FOOTER_PAGENUMBER_ALIGNMENT')).get('value'),
                                    mt = store.getAt(store.find('name', 'SETTINGS_PRINTS_MARGIN_TOP')).get('value'),
                                    mr = store.getAt(store.find('name', 'SETTINGS_PRINTS_MARGIN_RIGHT')).get('value'),
                                    mb = store.getAt(store.find('name', 'SETTINGS_PRINTS_MARGIN_BOTTOM')).get('value'),
                                    ml = store.getAt(store.find('name', 'SETTINGS_PRINTS_MARGIN_LEFT')).get('value');

                                // Value set



                                Ext.getCmp('SettingsPrintsH1').setValue(h1);
                                Ext.getCmp('SettingsPrintsH1A').setValue(h1a);
                                Ext.getCmp('SettingsPrintsH2').setValue(h2);
                                Ext.getCmp('SettingsPrintsH2A').setValue(h2a);
                                Ext.getCmp('SettingsPrintsH3').setValue(h3);
                                Ext.getCmp('SettingsPrintsH3A').setValue(h3a);
                                Ext.getCmp('SettingsPrintsH4').setValue(h4);
                                Ext.getCmp('SettingsPrintsH4A').setValue(h4a);

                                // Footer
                                Ext.getCmp('SettingsPrintsF1').setValue(f1);
                                Ext.getCmp('SettingsPrintsF1A').setValue(f1a);
                                Ext.getCmp('SettingsPrintsF2').setValue(f2);
                                Ext.getCmp('SettingsPrintsF2A').setValue(f2a);
                                Ext.getCmp('SettingsPrintsF3').setValue(f3);
                                Ext.getCmp('SettingsPrintsF3A').setValue(f3a);
                                Ext.getCmp('SettingsPrintsF4').setValue(f4);
                                Ext.getCmp('SettingsPrintsF4A').setValue(f4a);
                                Ext.getCmp('SettingsPrintsFP').setValue(fps);
                                Ext.getCmp('SettingsPrintsFPA').setValue(fpa);

                                // Margins
                                Ext.getCmp('SettingsPrintsMT').setValue(mt);
                                Ext.getCmp('SettingsPrintsMR').setValue(mr);
                                Ext.getCmp('SettingsPrintsMB').setValue(mb);
                                Ext.getCmp('SettingsPrintsML').setValue(ml);

                                // Alignment buttons set

                                // Header
                                if (h1a === 'L') {
                                    Ext.getCmp('SettingsPrintsH1L').toggle(true, true);
                                } else if (h1a === 'C') {
                                    Ext.getCmp('SettingsPrintsH1C').toggle(true, true);
                                } else if (h1a === 'R') {
                                    Ext.getCmp('SettingsPrintsH1R').toggle(true, true);
                                }

                                if (h2a === 'L') {
                                    Ext.getCmp('SettingsPrintsH2L').toggle(true, true);
                                } else if (h2a === 'C') {
                                    Ext.getCmp('SettingsPrintsH2C').toggle(true, true);
                                } else if (h2a === 'R') {
                                    Ext.getCmp('SettingsPrintsH2R').toggle(true, true);
                                }

                                if (h3a === 'L') {
                                    Ext.getCmp('SettingsPrintsH3L').toggle(true, true);
                                } else if (h3a === 'C') {
                                    Ext.getCmp('SettingsPrintsH3C').toggle(true, true);
                                } else if (h3a === 'R') {
                                    Ext.getCmp('SettingsPrintsH3R').toggle(true, true);
                                }

                                if (h4a === 'L') {
                                    Ext.getCmp('SettingsPrintsH4L').toggle(true, true);
                                } else if (h4a === 'C') {
                                    Ext.getCmp('SettingsPrintsH4C').toggle(true, true);
                                } else if (h4a === 'R') {
                                    Ext.getCmp('SettingsPrintsH4R').toggle(true, true);
                                }

                                // Footer
                                if (f1a === 'L') {
                                    Ext.getCmp('SettingsPrintsF1L').toggle(true, true);
                                } else if (f1a === 'C') {
                                    Ext.getCmp('SettingsPrintsF1C').toggle(true, true);
                                } else if (f1a === 'R') {
                                    Ext.getCmp('SettingsPrintsF1R').toggle(true, true);
                                }

                                if (f2a === 'L') {
                                    Ext.getCmp('SettingsPrintsF2L').toggle(true, true);
                                } else if (f2a === 'C') {
                                    Ext.getCmp('SettingsPrintsF2C').toggle(true, true);
                                } else if (f2a === 'R') {
                                    Ext.getCmp('SettingsPrintsF2R').toggle(true, true);
                                }

                                if (f3a === 'L') {
                                    Ext.getCmp('SettingsPrintsF3L').toggle(true, true);
                                } else if (f3a === 'C') {
                                    Ext.getCmp('SettingsPrintsF3C').toggle(true, true);
                                } else if (f3a === 'R') {
                                    Ext.getCmp('SettingsPrintsF3R').toggle(true, true);
                                }

                                if (f4a === 'L') {
                                    Ext.getCmp('SettingsPrintsF4L').toggle(true, true);
                                } else if (f4a === 'C') {
                                    Ext.getCmp('SettingsPrintsF4C').toggle(true, true);
                                } else if (f4a === 'R') {
                                    Ext.getCmp('SettingsPrintsF4R').toggle(true, true);
                                }

                                if (fps.indexOf("#") < 0) {
                                    Ext.getCmp('SettingsPrintsFPSP').toggle(false, true);
                                    Ext.getCmp('SettingsPrintsFPSI').toggle(false, true);
                                    Ext.getCmp('SettingsPrintsFPS1').toggle(false, true);
                                    Ext.getCmp('SettingsPrintsFPS2').toggle(false, true);
                                    Ext.getCmp('SettingsPrintsFPSI').setDisabled(true);
                                    Ext.getCmp('SettingsPrintsFPS1').setDisabled(true);
                                    Ext.getCmp('SettingsPrintsFPS2').setDisabled(true);
                                } else {
                                    Ext.getCmp('SettingsPrintsFPSP').toggle(true, true);
                                    Ext.getCmp('SettingsPrintsFPSI').setDisabled(false);
                                    Ext.getCmp('SettingsPrintsFPS1').setDisabled(false);
                                    Ext.getCmp('SettingsPrintsFPS2').setDisabled(false);

                                    if (fps.indexOf("Pagina") > -1) {
                                        Ext.getCmp('SettingsPrintsFPSI').toggle(true, true);
                                    }

                                    if (fps.indexOf("di") > -1) {
                                        Ext.getCmp('SettingsPrintsFPS1').toggle(true, true);
                                    } else if (fps.indexOf("/") > -1) {
                                        Ext.getCmp('SettingsPrintsFPS2').toggle(true, true);
                                    }
                                }

                                if (fpa === 'L') {
                                    Ext.getCmp('SettingsPrintsFPAL').toggle(true, true);
                                } else if (fpa === 'C') {
                                    Ext.getCmp('SettingsPrintsFPAC').toggle(true, true);
                                } else if (fpa === 'R') {
                                    Ext.getCmp('SettingsPrintsFPAR').toggle(true, true);
                                }

                                // Header

                                setTimeout(function () { Ext.getCmp('SettingsPrintsForm').setLogo(hl); Ext.getCmp('SettingsPrintsForm').setBgImg(hbg); }, 500);

                            },
                            border: false,
                            id: 'SettingsPrintsTab',
                            itemId: 'SettingsPrintsTab',
                            title: 'Stampe',
                            layout: {
                                type: 'vbox',
                                align: 'stretch'
                            },
                            tabConfig: {
                                xtype: 'tab',
                                id: 'SettingsPrintsMenuTab',
                                itemId: 'SettingsPrintsMenuTab',
                                iconCls: 'icon-printer',
                                textAlign: 'left'
                            },
                            items: [
                                {
                                    xtype: 'form',
                                    setPageShowMode: function () {
                                        var mode = Ext.getCmp('SettingsPrintsFP'),
                                            initial = Ext.getCmp('SettingsPrintsFPSI'),
                                            page = Ext.getCmp('SettingsPrintsFPSP'),
                                            separator1 = Ext.getCmp('SettingsPrintsFPS1').pressed,
                                            separator2 = Ext.getCmp('SettingsPrintsFPS2').pressed,
                                            value = '';

                                        if (page.pressed) {
                                            if (initial.pressed) {
                                                value = 'Pagina ';
                                            }

                                            value += '#';

                                            if (separator1) {
                                                value += ' di @';
                                            } else if (separator2) {
                                                value += ' / @';
                                            }
                                        }

                                        mode.setValue(value);
                                    },
                                    setLogo: function (logo) {
                                        var emptyLogo = logo === '' ? true : false,
                                            store = Ext.getStore('CoreParameter'),
                                            old = store.getAt(store.find('name', 'SETTINGS_PRINTS_HEADER_LOGO')).get('value');

                                        if (logo === old) {
                                            Ext.getCmp('SettingsPrintsHLogoAction').setValue(0);
                                            Ext.getCmp('SettingsPrintsHLogo').setValue('');
                                        } else {
                                            Ext.getCmp('SettingsPrintsHLogoAction').setValue(1);
                                            Ext.getCmp('SettingsPrintsHLogo').setValue(logo);
                                        }

                                        Ext.getCmp('SettingsPrintsHLRemove').setDisabled(emptyLogo);

                                        Ext.getCmp('SettingsPrintsHLImg').hide();
                                        Ext.getCmp('SettingsPrintsHLImg').setSrc(logo);

                                        if (!emptyLogo) {
                                            Ext.getCmp('SettingsPrintsHLImg').show();
                                        }
                                    },
                                    setBgImg: function (img) {
                                        var emptyLogo = img === '' ? true : false,
                                            store = Ext.getStore('CoreParameter'),
                                            old = store.getAt(store.find('name', 'INVOICE_BG_IMG')).get('value');

                                        if (img === old) {
                                            Ext.getCmp('SettingsPrintsHBgAction').setValue(0);
                                            Ext.getCmp('SettingsPrintsHBg').setValue('');
                                        } else {
                                            Ext.getCmp('SettingsPrintsHBgAction').setValue(1);
                                            Ext.getCmp('SettingsPrintsHBg').setValue(img);
                                        }

                                        Ext.getCmp('SettingsPrintsHBgRemove').setDisabled(emptyLogo);
                                        Ext.getCmp('SettingsPrintsHBImg').hide();
                                        Ext.getCmp('SettingsPrintsHBImg').setSrc(img);

                                        if (!emptyLogo) {
                                            Ext.getCmp('SettingsPrintsHBImg').show();
                                        }
                                    },
                                    checkDependencies: function () {
                                        var initial = Ext.getCmp('SettingsPrintsFPSI'),
                                            page = Ext.getCmp('SettingsPrintsFPSP'),
                                            separator1 = Ext.getCmp('SettingsPrintsFPS1'),
                                            separator2 = Ext.getCmp('SettingsPrintsFPS2');

                                        if (page.pressed) {
                                            initial.setDisabled(false);
                                            separator1.setDisabled(false);
                                            separator2.setDisabled(false);
                                        } else {
                                            initial.setDisabled(true);
                                            initial.toggle(false, true);
                                            separator1.setDisabled(true);
                                            separator1.toggle(false, true);
                                            separator2.setDisabled(true);
                                            separator2.toggle(false, true);
                                        }

                                        Ext.getCmp('SettingsPrintsForm').setPageShowMode();
                                    },
                                    flex: 1,
                                    border: false,
                                    id: 'SettingsPrintsForm',
                                    itemId: 'SettingsPrintsForm',
                                    autoScroll: true,
                                    bodyCls: 'bck-content',
                                    bodyPadding: 10,
                                    url: '/mc2-api/core/parameter/batch',
                                    layout: {
                                        type: 'vbox',
                                        align: 'stretch'
                                    },
                                    items: [
                                        {
                                            xtype: 'fieldset',
                                            title: 'Intestazione',
                                            layout: {
                                                type: 'vbox',
                                                align: 'stretch'
                                            },
                                            items: [
                                                {
                                                    xtype: 'container',
                                                    flex: 1,
                                                    margin: '0 0 5 0',
                                                    layout: {
                                                        type: 'vbox',
                                                        align: 'center'
                                                    },
                                                    items: [
                                                        {
                                                            xtype: 'image',
                                                            id: 'SettingsPrintsHLImg',
                                                            itemId: 'SettingsPrintsHLImg',
                                                            maxHeight: 200,
                                                            maxWidth: 200,
                                                            alt: 'Logo istituto'
                                                        }
                                                    ]
                                                },
                                                {
                                                    xtype: 'container',
                                                    flex: 1,
                                                    margin: '0 0 5 0',
                                                    layout: {
                                                        type: 'hbox',
                                                        align: 'stretch'
                                                    },
                                                    items: [
                                                        {
                                                            xtype: 'filefield',
                                                            flex: 1,
                                                            id: 'SettingsPrintsHL',
                                                            itemId: 'SettingsPrintsHL',
                                                            fieldLabel: 'Logo',
                                                            labelAlign: 'right',
                                                            name: 'logo',
                                                            editable: false,
                                                            buttonText: 'Sfoglia...',
                                                            listeners: {
                                                                change: {
                                                                    fn: me.onSettingsPrintsHLChange,
                                                                    scope: me
                                                                }
                                                            }
                                                        },
                                                        {
                                                            xtype: 'button',
                                                            handler: function (button, e) {
                                                                Ext.getCmp('SettingsPrintsForm').setLogo('');
                                                            },
                                                            id: 'SettingsPrintsHLRemove',
                                                            itemId: 'SettingsPrintsHLRemove',
                                                            margin: '0 0 0 10',
                                                            text: 'Rimuovi'
                                                        },
                                                        {
                                                            xtype: 'button',
                                                            handler: function (button, e) {
                                                                var img = 'data:image/png;base64,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';
                                                                Ext.getCmp('SettingsPrintsForm').setLogo(img);
                                                            },
                                                            margin: '0 0 0 10',
                                                            text: 'Imposta default'
                                                        },
                                                        {
                                                            xtype: 'hiddenfield',
                                                            id: 'SettingsPrintsHLogo',
                                                            itemId: 'SettingsPrintsHLogo',
                                                            name: 'SETTINGS_PRINTS_HEADER_LOGO'
                                                        },
                                                        {
                                                            xtype: 'hiddenfield',
                                                            id: 'SettingsPrintsHLogoAction',
                                                            itemId: 'SettingsPrintsHLogoAction',
                                                            name: 'logo_action',
                                                            value: 0
                                                        }
                                                    ]
                                                },
                                                {
                                                    xtype: 'container',
                                                    flex: 1,
                                                    margin: '0 0 5 0',
                                                    layout: {
                                                        type: 'vbox',
                                                        align: 'center'
                                                    },
                                                    items: [
                                                        {
                                                            xtype: 'image',
                                                            id: 'SettingsPrintsHBImg',
                                                            maxHeight: 200,
                                                            maxWidth: 200
                                                        }
                                                    ]
                                                },
                                                {
                                                    xtype: 'container',
                                                    flex: 1,
                                                    margin: '0 0 5 0',
                                                    layout: {
                                                        type: 'hbox',
                                                        align: 'stretch'
                                                    },
                                                    items: [
                                                        {
                                                            xtype: 'filefield',
                                                            flex: 1,
                                                            id: 'SettingsPrintsHB',
                                                            fieldLabel: 'Carta intestata fatture/ricevute',
                                                            labelAlign: 'right',
                                                            name: 'bgImg',
                                                            buttonText: 'Sfoglia...',
                                                            listeners: {
                                                                change: {
                                                                    fn: me.onSettingsPrintsHBChange,
                                                                    scope: me
                                                                }
                                                            }
                                                        },
                                                        {
                                                            xtype: 'button',
                                                            handler: function (button, e) {
                                                                Ext.getCmp('SettingsPrintsForm').setBgImg('');
                                                            },
                                                            id: 'SettingsPrintsHBgRemove',
                                                            margin: '0 0 0 10',
                                                            text: 'Rimuovi'
                                                        },
                                                        {
                                                            xtype: 'hiddenfield',
                                                            flex: 1,
                                                            id: 'SettingsPrintsHBg',
                                                            fieldLabel: 'Label',
                                                            name: 'INVOICE_BG_IMG'
                                                        },
                                                        {
                                                            xtype: 'hiddenfield',
                                                            flex: 1,
                                                            id: 'SettingsPrintsHBgAction',
                                                            fieldLabel: 'Label',
                                                            name: 'bg_action'
                                                        }
                                                    ]
                                                },
                                                {
                                                    xtype: 'container',
                                                    flex: 1,
                                                    margin: '0 0 5 0',
                                                    layout: {
                                                        type: 'hbox',
                                                        align: 'stretch'
                                                    },
                                                    items: [
                                                        {
                                                            xtype: 'textfield',
                                                            flex: 1,
                                                            id: 'SettingsPrintsH1',
                                                            itemId: 'SettingsPrintsH1',
                                                            fieldLabel: '1ª Riga',
                                                            labelAlign: 'right',
                                                            name: 'SETTINGS_PRINTS_HEADER_1ROW_TEXT'
                                                        },
                                                        {
                                                            xtype: 'button',
                                                            toggleHandler: function (button, state) {
                                                                Ext.getCmp('SettingsPrintsH1A').setValue('L');
                                                            },
                                                            id: 'SettingsPrintsH1L',
                                                            itemId: 'SettingsPrintsH1L',
                                                            margin: '0 0 0 10',
                                                            allowDepress: false,
                                                            enableToggle: true,
                                                            iconCls: 'icon-text_align_left',
                                                            toggleGroup: 'h1a'
                                                        },
                                                        {
                                                            xtype: 'button',
                                                            toggleHandler: function (button, state) {
                                                                Ext.getCmp('SettingsPrintsH1A').setValue('C');
                                                            },
                                                            id: 'SettingsPrintsH1C',
                                                            itemId: 'SettingsPrintsH1C',
                                                            margin: '0 0 0 5',
                                                            allowDepress: false,
                                                            enableToggle: true,
                                                            iconCls: 'icon-text_align_center',
                                                            toggleGroup: 'h1a'
                                                        },
                                                        {
                                                            xtype: 'button',
                                                            toggleHandler: function (button, state) {
                                                                Ext.getCmp('SettingsPrintsH1A').setValue('R');
                                                            },
                                                            id: 'SettingsPrintsH1R',
                                                            itemId: 'SettingsPrintsH1R',
                                                            margin: '0 0 0 5',
                                                            allowDepress: false,
                                                            enableToggle: true,
                                                            iconCls: 'icon-text_align_right',
                                                            toggleGroup: 'h1a'
                                                        },
                                                        {
                                                            xtype: 'hiddenfield',
                                                            id: 'SettingsPrintsH1A',
                                                            itemId: 'SettingsPrintsH1A',
                                                            name: 'SETTINGS_PRINTS_HEADER_1ROW_ALIGNMENT'
                                                        }
                                                    ]
                                                },
                                                {
                                                    xtype: 'container',
                                                    flex: 1,
                                                    margin: '0 0 5 0',
                                                    layout: {
                                                        type: 'hbox',
                                                        align: 'stretch'
                                                    },
                                                    items: [
                                                        {
                                                            xtype: 'textfield',
                                                            flex: 1,
                                                            id: 'SettingsPrintsH2',
                                                            itemId: 'SettingsPrintsH2',
                                                            fieldLabel: '2ª Riga',
                                                            labelAlign: 'right',
                                                            name: 'SETTINGS_PRINTS_HEADER_2ROW_TEXT'
                                                        },
                                                        {
                                                            xtype: 'button',
                                                            toggleHandler: function (button, state) {
                                                                Ext.getCmp('SettingsPrintsH2A').setValue('L');
                                                            },
                                                            id: 'SettingsPrintsH2L',
                                                            itemId: 'SettingsPrintsH2L',
                                                            margin: '0 0 0 10',
                                                            allowDepress: false,
                                                            enableToggle: true,
                                                            iconCls: 'icon-text_align_left',
                                                            toggleGroup: 'h2a'
                                                        },
                                                        {
                                                            xtype: 'button',
                                                            toggleHandler: function (button, state) {
                                                                Ext.getCmp('SettingsPrintsH2A').setValue('C');
                                                            },
                                                            id: 'SettingsPrintsH2C',
                                                            itemId: 'SettingsPrintsH2C',
                                                            margin: '0 0 0 5',
                                                            allowDepress: false,
                                                            enableToggle: true,
                                                            iconCls: 'icon-text_align_center',
                                                            toggleGroup: 'h2a'
                                                        },
                                                        {
                                                            xtype: 'button',
                                                            toggleHandler: function (button, state) {
                                                                Ext.getCmp('SettingsPrintsH2A').setValue('R');
                                                            },
                                                            id: 'SettingsPrintsH2R',
                                                            itemId: 'SettingsPrintsH2R',
                                                            margin: '0 0 0 5',
                                                            allowDepress: false,
                                                            enableToggle: true,
                                                            iconCls: 'icon-text_align_right',
                                                            toggleGroup: 'h2a'
                                                        },
                                                        {
                                                            xtype: 'hiddenfield',
                                                            id: 'SettingsPrintsH2A',
                                                            itemId: 'SettingsPrintsH2A',
                                                            name: 'SETTINGS_PRINTS_HEADER_2ROW_ALIGNMENT'
                                                        }
                                                    ]
                                                },
                                                {
                                                    xtype: 'container',
                                                    flex: 1,
                                                    margin: '0 0 5 0',
                                                    layout: {
                                                        type: 'hbox',
                                                        align: 'stretch'
                                                    },
                                                    items: [
                                                        {
                                                            xtype: 'textfield',
                                                            flex: 1,
                                                            id: 'SettingsPrintsH3',
                                                            itemId: 'SettingsPrintsH3',
                                                            fieldLabel: '3ª Riga',
                                                            labelAlign: 'right',
                                                            name: 'SETTINGS_PRINTS_HEADER_3ROW_TEXT'
                                                        },
                                                        {
                                                            xtype: 'button',
                                                            toggleHandler: function (button, state) {
                                                                Ext.getCmp('SettingsPrintsH3A').setValue('L');
                                                            },
                                                            id: 'SettingsPrintsH3L',
                                                            itemId: 'SettingsPrintsH3L',
                                                            margin: '0 0 0 10',
                                                            allowDepress: false,
                                                            enableToggle: true,
                                                            iconCls: 'icon-text_align_left',
                                                            toggleGroup: 'h3a'
                                                        },
                                                        {
                                                            xtype: 'button',
                                                            toggleHandler: function (button, state) {
                                                                Ext.getCmp('SettingsPrintsH3A').setValue('C');
                                                            },
                                                            id: 'SettingsPrintsH3C',
                                                            itemId: 'SettingsPrintsH3C',
                                                            margin: '0 0 0 5',
                                                            allowDepress: false,
                                                            enableToggle: true,
                                                            iconCls: 'icon-text_align_center',
                                                            toggleGroup: 'h3a'
                                                        },
                                                        {
                                                            xtype: 'button',
                                                            toggleHandler: function (button, state) {
                                                                Ext.getCmp('SettingsPrintsH3A').setValue('R');
                                                            },
                                                            id: 'SettingsPrintsH3R',
                                                            itemId: 'SettingsPrintsH3R',
                                                            margin: '0 0 0 5',
                                                            allowDepress: false,
                                                            enableToggle: true,
                                                            iconCls: 'icon-text_align_right',
                                                            toggleGroup: 'h3a'
                                                        },
                                                        {
                                                            xtype: 'hiddenfield',
                                                            id: 'SettingsPrintsH3A',
                                                            itemId: 'SettingsPrintsH3A',
                                                            name: 'SETTINGS_PRINTS_HEADER_3ROW_ALIGNMENT'
                                                        }
                                                    ]
                                                },
                                                {
                                                    xtype: 'container',
                                                    flex: 1,
                                                    margin: '0 0 5 0',
                                                    layout: {
                                                        type: 'hbox',
                                                        align: 'stretch'
                                                    },
                                                    items: [
                                                        {
                                                            xtype: 'textfield',
                                                            flex: 1,
                                                            id: 'SettingsPrintsH4',
                                                            itemId: 'SettingsPrintsH4',
                                                            fieldLabel: '4ª Riga',
                                                            labelAlign: 'right',
                                                            name: 'SETTINGS_PRINTS_HEADER_4ROW_TEXT'
                                                        },
                                                        {
                                                            xtype: 'button',
                                                            toggleHandler: function (button, state) {
                                                                Ext.getCmp('SettingsPrintsH4A').setValue('L');
                                                            },
                                                            id: 'SettingsPrintsH4L',
                                                            itemId: 'SettingsPrintsH4L',
                                                            margin: '0 0 0 10',
                                                            allowDepress: false,
                                                            enableToggle: true,
                                                            iconCls: 'icon-text_align_left',
                                                            toggleGroup: 'h4a'
                                                        },
                                                        {
                                                            xtype: 'button',
                                                            toggleHandler: function (button, state) {
                                                                Ext.getCmp('SettingsPrintsH4A').setValue('C');
                                                            },
                                                            id: 'SettingsPrintsH4C',
                                                            itemId: 'SettingsPrintsH4C',
                                                            margin: '0 0 0 5',
                                                            allowDepress: false,
                                                            enableToggle: true,
                                                            iconCls: 'icon-text_align_center',
                                                            toggleGroup: 'h4a'
                                                        },
                                                        {
                                                            xtype: 'button',
                                                            toggleHandler: function (button, state) {
                                                                Ext.getCmp('SettingsPrintsH4A').setValue('R');
                                                            },
                                                            id: 'SettingsPrintsH4R',
                                                            itemId: 'SettingsPrintsH4R',
                                                            margin: '0 0 0 5',
                                                            allowDepress: false,
                                                            enableToggle: true,
                                                            iconCls: 'icon-text_align_right',
                                                            toggleGroup: 'h4a'
                                                        },
                                                        {
                                                            xtype: 'hiddenfield',
                                                            id: 'SettingsPrintsH4A',
                                                            itemId: 'SettingsPrintsH4A',
                                                            name: 'SETTINGS_PRINTS_HEADER_4ROW_ALIGNMENT'
                                                        }
                                                    ]
                                                }
                                            ]
                                        },
                                        {
                                            xtype: 'fieldset',
                                            title: 'Piè di pagina',
                                            layout: {
                                                type: 'vbox',
                                                align: 'stretch'
                                            },
                                            items: [
                                                {
                                                    xtype: 'container',
                                                    flex: 1,
                                                    margin: '0 0 5 0',
                                                    layout: {
                                                        type: 'hbox',
                                                        align: 'stretch'
                                                    },
                                                    items: [
                                                        {
                                                            xtype: 'textfield',
                                                            flex: 1,
                                                            id: 'SettingsPrintsF1',
                                                            itemId: 'SettingsPrintsF1',
                                                            fieldLabel: '1ª Riga',
                                                            labelAlign: 'right',
                                                            name: 'SETTINGS_PRINTS_FOOTER_1ROW_TEXT'
                                                        },
                                                        {
                                                            xtype: 'button',
                                                            toggleHandler: function (button, state) {
                                                                Ext.getCmp('SettingsPrintsF1A').setValue('L');
                                                            },
                                                            id: 'SettingsPrintsF1L',
                                                            itemId: 'SettingsPrintsF1L',
                                                            margin: '0 0 0 10',
                                                            allowDepress: false,
                                                            enableToggle: true,
                                                            iconCls: 'icon-text_align_left',
                                                            toggleGroup: 'f1a'
                                                        },
                                                        {
                                                            xtype: 'button',
                                                            toggleHandler: function (button, state) {
                                                                Ext.getCmp('SettingsPrintsF1A').setValue('C');
                                                            },
                                                            id: 'SettingsPrintsF1C',
                                                            itemId: 'SettingsPrintsF1C',
                                                            margin: '0 0 0 5',
                                                            allowDepress: false,
                                                            enableToggle: true,
                                                            iconCls: 'icon-text_align_center',
                                                            toggleGroup: 'f1a'
                                                        },
                                                        {
                                                            xtype: 'button',
                                                            toggleHandler: function (button, state) {
                                                                Ext.getCmp('SettingsPrintsF1A').setValue('R');
                                                            },
                                                            id: 'SettingsPrintsF1R',
                                                            itemId: 'SettingsPrintsF1R',
                                                            margin: '0 0 0 5',
                                                            allowDepress: false,
                                                            enableToggle: true,
                                                            iconCls: 'icon-text_align_right',
                                                            toggleGroup: 'f1a'
                                                        },
                                                        {
                                                            xtype: 'hiddenfield',
                                                            id: 'SettingsPrintsF1A',
                                                            itemId: 'SettingsPrintsF1A',
                                                            name: 'SETTINGS_PRINTS_FOOTER_1ROW_ALIGNMENT'
                                                        }
                                                    ]
                                                },
                                                {
                                                    xtype: 'container',
                                                    flex: 1,
                                                    margin: '0 0 5 0',
                                                    layout: {
                                                        type: 'hbox',
                                                        align: 'stretch'
                                                    },
                                                    items: [
                                                        {
                                                            xtype: 'textfield',
                                                            flex: 1,
                                                            id: 'SettingsPrintsF2',
                                                            itemId: 'SettingsPrintsF2',
                                                            fieldLabel: '2ª Riga',
                                                            labelAlign: 'right',
                                                            name: 'SETTINGS_PRINTS_FOOTER_2ROW_TEXT'
                                                        },
                                                        {
                                                            xtype: 'button',
                                                            toggleHandler: function (button, state) {
                                                                Ext.getCmp('SettingsPrintsF2A').setValue('L');
                                                            },
                                                            id: 'SettingsPrintsF2L',
                                                            itemId: 'SettingsPrintsF2L',
                                                            margin: '0 0 0 10',
                                                            allowDepress: false,
                                                            enableToggle: true,
                                                            iconCls: 'icon-text_align_left',
                                                            toggleGroup: 'f2a'
                                                        },
                                                        {
                                                            xtype: 'button',
                                                            toggleHandler: function (button, state) {
                                                                Ext.getCmp('SettingsPrintsF2A').setValue('C');
                                                            },
                                                            id: 'SettingsPrintsF2C',
                                                            itemId: 'SettingsPrintsF2C',
                                                            margin: '0 0 0 5',
                                                            allowDepress: false,
                                                            enableToggle: true,
                                                            iconCls: 'icon-text_align_center',
                                                            toggleGroup: 'f2a'
                                                        },
                                                        {
                                                            xtype: 'button',
                                                            toggleHandler: function (button, state) {
                                                                Ext.getCmp('SettingsPrintsF2A').setValue('R');
                                                            },
                                                            id: 'SettingsPrintsF2R',
                                                            itemId: 'SettingsPrintsF2R',
                                                            margin: '0 0 0 5',
                                                            allowDepress: false,
                                                            enableToggle: true,
                                                            iconCls: 'icon-text_align_right',
                                                            toggleGroup: 'f2a'
                                                        },
                                                        {
                                                            xtype: 'hiddenfield',
                                                            id: 'SettingsPrintsF2A',
                                                            itemId: 'SettingsPrintsF2A',
                                                            name: 'SETTINGS_PRINTS_FOOTER_2ROW_ALIGNMENT'
                                                        }
                                                    ]
                                                },
                                                {
                                                    xtype: 'container',
                                                    flex: 1,
                                                    margin: '0 0 5 0',
                                                    layout: {
                                                        type: 'hbox',
                                                        align: 'stretch'
                                                    },
                                                    items: [
                                                        {
                                                            xtype: 'textfield',
                                                            flex: 1,
                                                            id: 'SettingsPrintsF3',
                                                            itemId: 'SettingsPrintsF3',
                                                            fieldLabel: '3ª Riga',
                                                            labelAlign: 'right',
                                                            name: 'SETTINGS_PRINTS_FOOTER_3ROW_TEXT'
                                                        },
                                                        {
                                                            xtype: 'button',
                                                            toggleHandler: function (button, state) {
                                                                Ext.getCmp('SettingsPrintsF3A').setValue('L');
                                                            },
                                                            id: 'SettingsPrintsF3L',
                                                            itemId: 'SettingsPrintsF3L',
                                                            margin: '0 0 0 10',
                                                            allowDepress: false,
                                                            enableToggle: true,
                                                            iconCls: 'icon-text_align_left',
                                                            toggleGroup: 'f3a'
                                                        },
                                                        {
                                                            xtype: 'button',
                                                            toggleHandler: function (button, state) {
                                                                Ext.getCmp('SettingsPrintsF3A').setValue('C');
                                                            },
                                                            id: 'SettingsPrintsF3C',
                                                            itemId: 'SettingsPrintsF3C',
                                                            margin: '0 0 0 5',
                                                            allowDepress: false,
                                                            enableToggle: true,
                                                            iconCls: 'icon-text_align_center',
                                                            toggleGroup: 'f3a'
                                                        },
                                                        {
                                                            xtype: 'button',
                                                            toggleHandler: function (button, state) {
                                                                Ext.getCmp('SettingsPrintsF3A').setValue('R');
                                                            },
                                                            id: 'SettingsPrintsF3R',
                                                            itemId: 'SettingsPrintsF3R',
                                                            margin: '0 0 0 5',
                                                            allowDepress: false,
                                                            enableToggle: true,
                                                            iconCls: 'icon-text_align_right',
                                                            toggleGroup: 'f3a'
                                                        },
                                                        {
                                                            xtype: 'hiddenfield',
                                                            id: 'SettingsPrintsF3A',
                                                            itemId: 'SettingsPrintsF3A',
                                                            name: 'SETTINGS_PRINTS_FOOTER_3ROW_ALIGNMENT'
                                                        }
                                                    ]
                                                },
                                                {
                                                    xtype: 'container',
                                                    flex: 1,
                                                    margin: '0 0 5 0',
                                                    layout: {
                                                        type: 'hbox',
                                                        align: 'stretch'
                                                    },
                                                    items: [
                                                        {
                                                            xtype: 'textfield',
                                                            flex: 1,
                                                            id: 'SettingsPrintsF4',
                                                            itemId: 'SettingsPrintsF4',
                                                            fieldLabel: '4ª Riga',
                                                            labelAlign: 'right',
                                                            name: 'SETTINGS_PRINTS_FOOTER_4ROW_TEXT'
                                                        },
                                                        {
                                                            xtype: 'button',
                                                            toggleHandler: function (button, state) {
                                                                Ext.getCmp('SettingsPrintsF4A').setValue('L');
                                                            },
                                                            id: 'SettingsPrintsF4L',
                                                            itemId: 'SettingsPrintsF4L',
                                                            margin: '0 0 0 10',
                                                            allowDepress: false,
                                                            enableToggle: true,
                                                            iconCls: 'icon-text_align_left',
                                                            toggleGroup: 'f4a'
                                                        },
                                                        {
                                                            xtype: 'button',
                                                            toggleHandler: function (button, state) {
                                                                Ext.getCmp('SettingsPrintsF4A').setValue('C');
                                                            },
                                                            id: 'SettingsPrintsF4C',
                                                            itemId: 'SettingsPrintsF4C',
                                                            margin: '0 0 0 5',
                                                            allowDepress: false,
                                                            enableToggle: true,
                                                            iconCls: 'icon-text_align_center',
                                                            toggleGroup: 'f4a'
                                                        },
                                                        {
                                                            xtype: 'button',
                                                            toggleHandler: function (button, state) {
                                                                Ext.getCmp('SettingsPrintsF4A').setValue('R');
                                                            },
                                                            id: 'SettingsPrintsF4R',
                                                            itemId: 'SettingsPrintsF4R',
                                                            margin: '0 0 0 5',
                                                            allowDepress: false,
                                                            enableToggle: true,
                                                            iconCls: 'icon-text_align_right',
                                                            toggleGroup: 'f4a'
                                                        },
                                                        {
                                                            xtype: 'hiddenfield',
                                                            id: 'SettingsPrintsF4A',
                                                            itemId: 'SettingsPrintsF4A',
                                                            name: 'SETTINGS_PRINTS_FOOTER_4ROW_ALIGNMENT'
                                                        }
                                                    ]
                                                },
                                                {
                                                    xtype: 'fieldcontainer',
                                                    flex: 1,
                                                    margin: '0 0 5 0',
                                                    fieldLabel: 'Numero pagina',
                                                    labelAlign: 'right',
                                                    layout: {
                                                        type: 'hbox',
                                                        align: 'stretch'
                                                    },
                                                    items: [
                                                        {
                                                            xtype: 'button',
                                                            id: 'SettingsPrintsFPSI',
                                                            itemId: 'SettingsPrintsFPSI',
                                                            enableToggle: true,
                                                            text: 'Pagina',
                                                            listeners: {
                                                                toggle: {
                                                                    fn: me.onSettingsPrintsFPSSToggle,
                                                                    scope: me
                                                                }
                                                            }
                                                        },
                                                        {
                                                            xtype: 'button',
                                                            id: 'SettingsPrintsFPSP',
                                                            itemId: 'SettingsPrintsFPSP',
                                                            margin: '0 0 0 5',
                                                            enableToggle: true,
                                                            text: '1',
                                                            listeners: {
                                                                toggle: {
                                                                    fn: me.onSettingsPrintsFPSPToggle,
                                                                    scope: me
                                                                }
                                                            }
                                                        },
                                                        {
                                                            xtype: 'button',
                                                            id: 'SettingsPrintsFPS1',
                                                            itemId: 'SettingsPrintsFPS1',
                                                            margin: '0 0 0 5',
                                                            enableToggle: true,
                                                            text: 'di 3',
                                                            toggleGroup: 'fpss',
                                                            listeners: {
                                                                toggle: {
                                                                    fn: me.onSettingsPrintsFPS2Toggle,
                                                                    scope: me
                                                                }
                                                            }
                                                        },
                                                        {
                                                            xtype: 'button',
                                                            id: 'SettingsPrintsFPS2',
                                                            itemId: 'SettingsPrintsFPS2',
                                                            margin: '0 0 0 5',
                                                            enableToggle: true,
                                                            text: '/ 3',
                                                            toggleGroup: 'fpss',
                                                            listeners: {
                                                                toggle: {
                                                                    fn: me.onSettingsPrintsFPS3Toggle,
                                                                    scope: me
                                                                }
                                                            }
                                                        },
                                                        {
                                                            xtype: 'hiddenfield',
                                                            id: 'SettingsPrintsFP',
                                                            itemId: 'SettingsPrintsFP',
                                                            name: 'SETTINGS_PRINTS_FOOTER_PAGENUMBER_TEXT'
                                                        },
                                                        {
                                                            xtype: 'button',
                                                            toggleHandler: function (button, state) {
                                                                Ext.getCmp('SettingsPrintsFPA').setValue('L');
                                                            },
                                                            id: 'SettingsPrintsFPAL',
                                                            itemId: 'SettingsPrintsFPAL',
                                                            margin: '0 0 0 40',
                                                            allowDepress: false,
                                                            enableToggle: true,
                                                            iconCls: 'icon-text_align_left',
                                                            toggleGroup: 'fpsa'
                                                        },
                                                        {
                                                            xtype: 'button',
                                                            toggleHandler: function (button, state) {
                                                                Ext.getCmp('SettingsPrintsFPA').setValue('C');
                                                            },
                                                            id: 'SettingsPrintsFPAC',
                                                            itemId: 'SettingsPrintsFPAC',
                                                            margin: '0 0 0 10',
                                                            allowDepress: false,
                                                            enableToggle: true,
                                                            iconCls: 'icon-text_align_center',
                                                            toggleGroup: 'fpsa'
                                                        },
                                                        {
                                                            xtype: 'button',
                                                            toggleHandler: function (button, state) {
                                                                Ext.getCmp('SettingsPrintsFPA').setValue('R');
                                                            },
                                                            id: 'SettingsPrintsFPAR',
                                                            itemId: 'SettingsPrintsFPAR',
                                                            margin: '0 0 0 10',
                                                            allowDepress: false,
                                                            enableToggle: true,
                                                            iconCls: 'icon-text_align_right',
                                                            toggleGroup: 'fpsa'
                                                        },
                                                        {
                                                            xtype: 'hiddenfield',
                                                            id: 'SettingsPrintsFPA',
                                                            itemId: 'SettingsPrintsFPA',
                                                            name: 'SETTINGS_PRINTS_FOOTER_PAGENUMBER_ALIGNMENT'
                                                        }
                                                    ]
                                                }
                                            ]
                                        },
                                        {
                                            xtype: 'fieldset',
                                            title: 'Margini (mm)',
                                            layout: {
                                                type: 'vbox',
                                                align: 'center'
                                            },
                                            items: [
                                                {
                                                    xtype: 'numberfield',
                                                    id: 'SettingsPrintsMT',
                                                    itemId: 'SettingsPrintsMT',
                                                    width: 65,
                                                    name: 'SETTINGS_PRINTS_MARGIN_TOP',
                                                    allowBlank: false,
                                                    allowOnlyWhitespace: false,
                                                    emptyText: 'Alto',
                                                    allowDecimals: false,
                                                    allowExponential: false,
                                                    autoStripChars: true,
                                                    maxValue: 140,
                                                    minValue: 0
                                                },
                                                {
                                                    xtype: 'container',
                                                    layout: {
                                                        type: 'hbox',
                                                        align: 'middle',
                                                        pack: 'center'
                                                    },
                                                    items: [
                                                        {
                                                            xtype: 'numberfield',
                                                            id: 'SettingsPrintsML',
                                                            itemId: 'SettingsPrintsML',
                                                            width: 65,
                                                            name: 'SETTINGS_PRINTS_MARGIN_LEFT',
                                                            allowBlank: false,
                                                            allowOnlyWhitespace: false,
                                                            emptyText: 'Sinistro',
                                                            allowDecimals: false,
                                                            allowExponential: false,
                                                            autoStripChars: true,
                                                            maxValue: 100,
                                                            minValue: 0
                                                        },
                                                        {
                                                            xtype: 'image',
                                                            margin: '5 5 5 5',
                                                            minHeight: 130,
                                                            width: 100,
                                                            src: 'resources/images/settingsIcons/margins.png'
                                                        },
                                                        {
                                                            xtype: 'numberfield',
                                                            id: 'SettingsPrintsMR',
                                                            itemId: 'SettingsPrintsMR',
                                                            width: 65,
                                                            name: 'SETTINGS_PRINTS_MARGIN_RIGHT',
                                                            allowBlank: false,
                                                            allowOnlyWhitespace: false,
                                                            emptyText: 'Destro',
                                                            allowDecimals: false,
                                                            allowExponential: false,
                                                            autoStripChars: true,
                                                            maxValue: 100,
                                                            minValue: 0
                                                        }
                                                    ]
                                                },
                                                {
                                                    xtype: 'numberfield',
                                                    id: 'SettingsPrintsMB',
                                                    itemId: 'SettingsPrintsMB',
                                                    width: 65,
                                                    name: 'SETTINGS_PRINTS_MARGIN_BOTTOM',
                                                    allowBlank: false,
                                                    allowOnlyWhitespace: false,
                                                    emptyText: 'Basso',
                                                    allowDecimals: false,
                                                    allowExponential: false,
                                                    autoStripChars: true,
                                                    maxValue: 140,
                                                    minValue: 0
                                                }
                                            ]
                                        }
                                    ],
                                    dockedItems: [
                                        {
                                            xtype: 'toolbar',
                                            flex: 1,
                                            dock: 'top',
                                            items: [
                                                {
                                                    xtype: 'button',
                                                    handler: function (button, e) {
                                                        var form = Ext.getCmp('SettingsPrintsForm'),
                                                            store = Ext.getStore('CoreParameter'),
                                                            logo = store.getAt(store.find('name', 'SETTINGS_PRINTS_HEADER_LOGO')).get('value'),
                                                            bgImg = store.getAt(store.find('name', 'INVOICE_BG_IMG')).get('value');

                                                        if (logo === Ext.getCmp('SettingsPrintsHL').getValue()) {
                                                            Ext.getCmp('SettingsPrintsHL').setDisabled(true);
                                                        }
                                                        if (bgImg === Ext.getCmp('SettingsPrintsHBg').getValue()) {
                                                            Ext.getCmp('SettingsPrintsHBg').setDisabled(true);
                                                        }

                                                        form.setLoading();

                                                        if (form.isValid()) {
                                                            form.submit({
                                                                success: function (f, action) {
                                                                    form.setLoading(false);
                                                                    Ext.getCmp('SettingsPrintsHL').setDisabled(false);
                                                                    Ext.getCmp('SettingsPrintsHBg').setDisabled(false);
                                                                    mc2ui.app.showNotifySave();
                                                                    store.load({
                                                                        callback: function () {
                                                                            /*var newLogo = store.getAt(store.find('name', 'SETTINGS_PRINTS_HEADER_LOGO')).get('value');
                                                                            Ext.getCmp('SettingsPrintsForm').setLogo(newLogo);
                                                                            var newBgImg = store.getAt(store.find('name', 'INVOICE_BG_IMG')).get('value');
                                                                            Ext.getCmp('SettingsPrintsForm').setBgImg(newBgImg);
                                                                            */
                                                                            Ext.getCmp('SettingsPrintsTab').showFunc();
                                                                        }
                                                                    });
                                                                },
                                                                failure: function (f, action) {
                                                                    form.setLoading(false);
                                                                    Ext.getCmp('SettingsPrintsHL').setDisabled(false);
                                                                    Ext.getCmp('SettingsPrintsHBg').setDisabled(false);
                                                                    Ext.Msg.alert('Attenzione', 'Valori NON salvati');
                                                                }
                                                            });
                                                        }
                                                    },
                                                    formBind: true,
                                                    iconCls: 'icon-disk',
                                                    text: 'Salva'
                                                }
                                            ]
                                        }
                                    ]
                                }
                            ],
                            listeners: {
                                show: {
                                    fn: me.onSettingsPrintsTabShow,
                                    scope: me
                                }
                            }
                        },
                        {
                            xtype: 'panel',
                            title: 'Solleciti',
                            layout: {
                                type: 'hbox',
                                align: 'stretch'
                            },
                            items: [
                                {
                                    xtype: 'gridpanel',
                                    id: 'SettingsReminderGrd',
                                    width: 250,
                                    title: '',
                                    store: 'ReminderTypes',
                                    columns: [
                                        {
                                            xtype: 'gridcolumn',
                                            dataIndex: 'description',
                                            text: 'Sollecito',
                                            flex: 1
                                        }
                                    ],
                                    listeners: {
                                        itemclick: {
                                            fn: me.onGridpanelItemClick,
                                            scope: me
                                        }
                                    }
                                },
                                {
                                    xtype: 'container',
                                    flex: 1,
                                    layout: {
                                        type: 'vbox',
                                        align: 'stretch'
                                    },
                                    items: [
                                        {
                                            xtype: 'toolbar',
                                            items: [
                                                {
                                                    xtype: 'button',
                                                    handler: function (button, e) {
                                                        var record = Ext.getCmp('SettingsReminderGrd').getSelectionModel().getSelection()[0];
                                                        Ext.getCmp('SettingsReminderCustomHtml').setLoading();
                                                        Ext.Ajax.request({
                                                            url: '/mc2-api/ccp/reminder_type/' + record.get('name'),
                                                            method: 'PUT',
                                                            params: {
                                                                value: Ext.getCmp('SettingsReminderCustomHtml').getValue()
                                                            },
                                                            success: function (res) {
                                                                var r = Ext.decode(res.responseText);
                                                                if (r.success) {
                                                                    Ext.getCmp('SettingsReminderCustomHtml').setValue(r.results.value);
                                                                } else {
                                                                    Ext.getCmp('SettingsReminderCustomHtml').setValue();
                                                                    Ext.Msg.alert('ERRORE', 'Errore durante la richiesta');
                                                                }
                                                                Ext.getCmp('SettingsReminderCustomHtml').setLoading(false);
                                                            }
                                                        });
                                                    },
                                                    text: 'Salva'
                                                }
                                            ]
                                        },
                                        {
                                            xtype: 'htmleditor',
                                            flex: 1,
                                            height: 150,
                                            id: 'SettingsReminderCustomHtml',
                                            fieldLabel: ''
                                        }
                                    ]
                                },
                                {
                                    xtype: 'panel',
                                    width: 250,
                                    title: 'Chiavi disponibili',
                                    layout: {
                                        type: 'vbox',
                                        align: 'stretch'
                                    },
                                    items: [
                                        {
                                            xtype: 'label',
                                            html: '<b>{{ data }} </b>Data invio sollecito',
                                            padding: 5,
                                            text: ''
                                        },
                                        {
                                            xtype: 'label',
                                            html: '<b>{{ movimenti }} </b>Lista movimenti',
                                            padding: 5,
                                            text: ''
                                        },
                                        {
                                            xtype: 'label',
                                            html: '<b>{{ movimenti_per_studente }} </b><br>Lista movimenti raggruppati per studente',
                                            padding: 5,
                                            text: ''
                                        },
                                        {
                                            xtype: 'label',
                                            html: '<b>{{ modalita_pagamento }} </b><br> Frase che definisce la modalità con cui il genitore può pagare',
                                            padding: 5,
                                            text: ''
                                        },
                                        {
                                            xtype: 'label',
                                            html: '<b>{{ pagante_nome }} </b> Nome pagante',
                                            padding: 5,
                                            text: ''
                                        },
                                        {
                                            xtype: 'label',
                                            html: '<b>{{ pagante_cognome }} </b>Cognome pagante',
                                            padding: 5,
                                            text: ''
                                        },
                                        {
                                            xtype: 'label',
                                            html: '<b>{{ pagante_iban }} </b> IBAN del pagante',
                                            padding: 5,
                                            text: ''
                                        }
                                    ]
                                }
                            ],
                            listeners: {
                                render: {
                                    fn: me.onPanelRender,
                                    scope: me
                                }
                            }
                        }
                    ]
                }
            ],
            listeners: {
                boxready: {
                    fn: me.onSettingsPanelBoxReady,
                    scope: me
                }
            },
        });

        me.callParent(arguments);
    },

    onRowEditingEdit: function (editor, context, eOpts) {
        Ext.getStore('ArchiveMailAccounts').save();
    },

    onRowEditingCanceledit: function (editor, context, eOpts) {
        if (context.record.get('id') < 1) {
            Ext.getStore('ArchiveMailAccounts').remove(context.record);
        }
    },

    onViewItemContextMenu1: function (dataview, record, item, index, e, eOpts) {
        e.stopEvent();
        var newX = e.xy[0];
        var newY = e.xy[1];
        Ext.getCmp('InstituteBankProfileMn').showAt([newX, newY]);
    },

    onRowEditingEdit1: function (editor, context, eOpts) {
        Ext.getStore('CoreBankAccounts').sync();
    },

    onRowEditingCanceledit1: function (editor, context, eOpts) {
        if (!context.value) {
            Ext.getStore('CoreBankAccounts').remove(context.record);
        }
    },

    onRowEditingBeforeEdit: function (editor, context, eOpts) {
        var pg = Ext.getCmp('InstituteBankProfile');

        return !pg.getPlugin().blocked;
    },

    onGridviewItemContextMenu1: function (dataview, record, item, index, e, eOpts) {
        e.stopEvent();
        var newX = e.xy[0];
        var newY = e.xy[1];
        Ext.getCmp('UsersEditMn').showAt([newX, newY]);
    },

    onGridviewItemClick: function (dataview, record, item, index, e, eOpts) {
        Ext.getCmp('PermissionsPanel').setTitle('Permessi: ' + record.raw.group_name);

        var group = record.raw.gid,
            store = Ext.getStore('Permissions');

        store.getProxy().setExtraParam('group_id', group);
        store.load();
    },

    onGridviewItemContextMenu2: function (dataview, record, item, index, e, eOpts) {
        e.stopEvent();
        var newX = e.xy[0];
        var newY = e.xy[1];
        Ext.getCmp('GroupsEditMn').showAt([newX, newY]);
    },

    onPermissionsPanelItemContextMenu: function (dataview, record, item, index, e, eOpts) {
        if (record.data.leaf === true) {
            e.stopEvent();
            var newX = e.xy[0];
            var newY = e.xy[1];
            Ext.getCmp('PermissionsEditMn').showAt([newX, newY]);
            if (record.data.allow) {
                Ext.getCmp('contextPermissionToggle').setText('Blocca');
            } else {
                Ext.getCmp('contextPermissionToggle').setText('Consenti');
            }
        }
    },

    onSettingsPrintsHLChange: function (filefield, value, eOpts) {
        Ext.getCmp('SettingsPrintsForm').setLogo(value);
    },

    onSettingsPrintsHBChange: function (filefield, value, eOpts) {
        Ext.getCmp('SettingsPrintsForm').setBgImg(value);
    },

    onSettingsPrintsFPSSToggle: function (button, pressed, eOpts) {
        Ext.getCmp('SettingsPrintsForm').setPageShowMode();
    },

    onSettingsPrintsFPSPToggle: function (button, pressed, eOpts) {
        Ext.getCmp('SettingsPrintsForm').checkDependencies();
    },

    onSettingsPrintsFPS2Toggle: function (button, pressed, eOpts) {
        Ext.getCmp('SettingsPrintsForm').setPageShowMode();
    },

    onSettingsPrintsFPS3Toggle: function (button, pressed, eOpts) {
        Ext.getCmp('SettingsPrintsForm').setPageShowMode();
    },

    onSettingsPrintsTabShow: function (component, eOpts) {
        setTimeout(function () { Ext.getCmp('SettingsPrintsTab').showFunc(); }, 100);
    },

    onGridpanelItemClick: function (dataview, record, item, index, e, eOpts) {
        Ext.getCmp('SettingsReminderCustomHtml').setLoading();
        Ext.Ajax.request({
            url: '/mc2-api/ccp/reminder_type/' + record.get('name'),
            success: function (res) {
                var r = Ext.decode(res.responseText);
                if (r.success) {
                    Ext.getCmp('SettingsReminderCustomHtml').setValue(r.results.value);
                } else {
                    Ext.getCmp('SettingsReminderCustomHtml').setValue();
                    Ext.Msg.alert('ERRORE', 'Errore durante la richiesta');
                }
                Ext.getCmp('SettingsReminderCustomHtml').setLoading(false);
            }
        });
    },

    onPanelRender: function (component, eOpts) {
        Ext.getStore('ReminderTypes').load();
    },

    onSettingsPanelBoxReady: function (component, width, height, eOpts) {
        Ext.getStore('EmployeesAll').load();
        Ext.getStore('CoreCities').load();
        Ext.getStore('SchoolTypes').load();
        //Ext.getStore('Institutes').load();
        Ext.getStore('SettingsUsers').load();
        Ext.getStore('SettingsGroups').load();
        Ext.getStore('CoreParameter').load();

        Ext.Ajax.request({
            url: '/mc2/applications/core/institutes/read_def.php',
            success: function (response) {
                var res = Ext.decode(response.responseText);

                if (res.success === true) {
                    var data = res.results;

                    data.city_id = parseInt(data.city_id);
                    data.job_director_id = parseInt(data.job_director_id);
                    data.job_vice_director_id = parseInt(data.job_vice_director_id);
                    data.job_dsga_id = parseInt(data.job_dsga_id);
                    data.job_personnel_id = parseInt(data.job_personnel_id);
                    data.job_accounting_id = parseInt(data.job_accounting_id);
                    data.job_warehouse_id = parseInt(data.job_warehouse_id);
                    data.job_registry_id = parseInt(data.job_registry_id);

                    if(data.roles) {
                        var role = '';
                        for (var i = 0; i < data.roles.length; i++) {
                            role = data.roles[i];
                            data[role.type + '_name' ] = role.name;
                            data[role.type + '_surname' ] = role.surname;
                            data[role.type + '_fiscal_code' ] = role.fiscal_code;
                        }
                    }

                    Ext.getCmp('InstituteFrm').getForm().setValues(data);

                    Ext.getStore('CoreBankAccounts').load({
                        params: {
                            institute: res.results.institute_id
                        }
                    });
                }
            }
        });

        Ext.getStore('ArchiveMailAccounts').load();

    }

});