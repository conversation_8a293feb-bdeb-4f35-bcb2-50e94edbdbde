/*
 * File: app/view/EmployeePnl.js
 *
 * This file was generated by Sencha Architect version 3.2.0.
 * http://www.sencha.com/products/architect/
 *
 * This file requires use of the Ext JS 4.2.x library, under independent license.
 * License of Sencha Architect does not include license for Ext JS 4.2.x. For more
 * details see http://www.sencha.com/license <NAME_EMAIL>.
 *
 * This file will be auto-generated each and everytime you save your project.
 *
 * Do NOT hand edit this file.
 */

Ext.define('mc2ui.view.EmployeePnl', {
    extend: 'Ext.panel.Panel',
    alias: 'widget.EmployeePnl',

    requires: [
        'Ext.menu.Menu',
        'Ext.grid.Panel',
        'Ext.grid.View',
        'Ext.grid.column.Action',
        'Ext.selection.RowModel',
        'Ext.grid.feature.Grouping',
        'Ext.XTemplate',
        'Ext.tab.Panel',
        'Ext.tab.Tab',
        'Ext.form.Panel',
        'Ext.form.RadioGroup',
        'Ext.form.field.Radio',
        'Ext.form.field.Date',
        'Ext.form.field.ComboBox',
        'Ext.form.field.Number',
        'Ext.form.field.Hidden',
        'Ext.menu.Separator',
        'Ext.toolbar.Paging',
        'Ext.grid.column.Date',
        'Ext.form.Label',
        'Ext.grid.plugin.CellEditing',
        'Ext.grid.plugin.RowEditing',
        'Ext.form.FieldSet',
        'Ext.toolbar.Separator',
        'Ext.toolbar.Spacer'
    ],

    border: false,
    hidden: true,
    id: 'EmployeePnl',
    itemId: 'EmployeePnl',
    layout: 'border',
    header: false,
    title: 'Personale',
    titleAlign: 'center',

    initComponent: function() {
        var me = this;

        Ext.applyIf(me, {
            items: [
                {
                    xtype: 'panel',
                    EmployeeInlineFilter: function() {
                        var name = Ext.getCmp('EmployeeSearchTxt').getValue();

                        Ext.getStore('Employees').load({
                            params:{
                                q_name: name
                            }
                        });

                        Ext.getCmp('EmployeeListPnl').getSelectionModel().deselectAll();
                        Ext.getCmp('EmployeeFrm').getForm().reset();
                        Ext.getCmp('EmployeeTabPnl').disable();
                        Ext.getCmp('EmployeeTabPnl').setTitle('Nessun Personale selezionato');
                    },
                    permissible: true,
                    region: 'west',
                    split: true,
                    id: 'EmployeeLeftPnl',
                    itemId: 'EmployeeLeftPnl',
                    width: 225,
                    collapseDirection: 'left',
                    collapsible: true,
                    iconCls: 'icon-group',
                    title: 'Personale',
                    titleCollapse: true,
                    layout: {
                        type: 'vbox',
                        align: 'stretch'
                    },
                    dockedItems: [
                        {
                            xtype: 'toolbar',
                            flex: 1,
                            dock: 'top',
                            items: [
                                {
                                    xtype: 'button',
                                    handler: function(button, e) {
                                        Ext.getCmp('EmployeeListPnl').getSelectionModel().deselectAll();
                                        Ext.getCmp('EmployeeTabPnl').setTitle('Nuovo Personale');
                                        var form = Ext.getCmp('EmployeeFrm').getForm();
                                        form.reset();
                                        Ext.getCmp('EmpGeneralMenuTab').show();
                                        Ext.getCmp('EmployeeTabPnl').setActiveTab(0);
                                        Ext.getCmp('EmployeeTabPnl').enable();
                                    },
                                    id: 'EmployeeAddBtn',
                                    itemId: 'EmployeeAddBtn',
                                    iconCls: 'icon-add',
                                    text: 'Nuovo',
                                    tooltip: 'Inserisci nuovo dipendente'
                                }
                            ]
                        },
                        {
                            xtype: 'toolbar',
                            flex: 1,
                            dock: 'top',
                            items: [
                                {
                                    xtype: 'textfield',
                                    flex: 1,
                                    id: 'EmployeeSearchTxt',
                                    itemId: 'EmployeeSearchTxt',
                                    margin: 2,
                                    inputId: 'q_name',
                                    emptyText: 'Cerca la persona...',
                                    listeners: {
                                        change: {
                                            fn: me.onEmployeeSearchTxtChange,
                                            delay: 300,
                                            buffer: 300,
                                            scope: me
                                        }
                                    }
                                }
                            ]
                        }
                    ],
                    items: [
                        {
                            xtype: 'menu',
                            permissible: true,
                            id: 'EmployeeEditMn',
                            itemId: 'EmployeeEditMn',
                            items: [
                                {
                                    xtype: 'menuitem',
                                    handler: function(item, e) {
                                        var record = Ext.getCmp('EmployeeListPnl').getSelectionModel().getSelection()[0];

                                        Ext.Msg.show({
                                            title: record.get('denomination'),
                                            msg: 'Questa operazione cancellerà anche tutti i dati relativi alla persona quali assenze, timbrature, orari o altro. Sei sicuro di voler eliminare questo dipendente?',
                                            buttons: Ext.Msg.YESNO,
                                            fn: function(r){
                                                if (r == 'yes') {
                                                    var store = Ext.getStore('Employees');
                                                    store.remove(record);
                                                    store.sync();
                                                    Ext.getCmp('EmployeeTabPnl').setTitle('Nessun Personale selezionato');
                                                    Ext.getCmp('EmployeeTabPnl').disable();
                                                    var form = Ext.getCmp('EmployeeFrm').getForm();
                                                    form.reset();
                                                    var q_group = Ext.getCmp('EmployeeSearchRadioGroup').getValue().q_liquid_group;
                                                    var q_denomination = Ext.getCmp('EmployeeSearchTxt').getValue();

                                                    store.load({
                                                        params: {
                                                            q_name: q_denomination,
                                                            q_liquid_group: q_group
                                                        }
                                                    });
                                                }
                                            }
                                        });
                                    },
                                    id: 'EmployeeCancelBtn',
                                    itemId: 'EmployeeCancelBtn',
                                    iconCls: 'icon-cancel',
                                    text: 'Elimina'
                                }
                            ]
                        },
                        {
                            xtype: 'gridpanel',
                            flex: 1,
                            border: false,
                            id: 'EmployeeListPnl',
                            itemId: 'EmployeeListPnl',
                            width: 187,
                            header: false,
                            emptyText: 'Nessun personale trovato',
                            hideHeaders: true,
                            store: 'Employees',
                            viewConfig: {
                                loadForm: function(record) {
                                    Ext.getCmp('EmployeeTabPnl').setTitle('Nome: ' + record.get('surname') + ' ' + record.get('name'));

                                    var form = Ext.getCmp('EmployeeFrm').getForm();
                                    form.reset();

                                    var st = Ext.getStore('CoreCities');
                                    st.clearFilter();

                                    form.loadRecord(record);

                                    Ext.getCmp('EmployeeTabPnl').enable();
                                    Ext.getCmp('EmployeeTabPnl').setActiveTab(0);
                                },
                                emptyText: 'Nessun personale trovato',
                                listeners: {
                                    itemcontextmenu: {
                                        fn: me.onGridviewItemContextMenu4,
                                        scope: me
                                    }
                                }
                            },
                            columns: [
                                {
                                    xtype: 'actioncolumn',
                                    width: 20,
                                    resizable: false,
                                    hideable: false,
                                    items: [
                                        {
                                            getClass: function(v, metadata, r, rowIndex, colIndex, store) {
                                                if (r.raw.job === 'E') {
                                                    return 'icon-user_brown';
                                                } else if (r.raw.job === 'P') {
                                                    return 'icon-user_suit_black';
                                                } else if (r.raw.job === 'V') {
                                                    return 'icon-user_gray';
                                                } else if (r.raw.job === 'P') {
                                                    return 'icon-user';
                                                } else if (r.raw.job === 'A') {
                                                    return 'icon-user_green';
                                                } else if (r.raw.job === 'W') {
                                                    return 'icon-user_orange';
                                                } else if (r.raw.job === 'R') {
                                                    return 'icon-user_red';
                                                } else if (r.raw.job.length > 1) {
                                                    return 'icon-group';
                                                }
                                            },
                                            getTip: function(v, meta, record, rowIndex, colIndex, store) {
                                                if (record.raw.job !== null) {
                                                    return record.raw.job_description;
                                                }
                                            }
                                        }
                                    ]
                                },
                                {
                                    xtype: 'gridcolumn',
                                    renderer: function(value, metaData, record, rowIndex, colIndex, store, view) {
                                        metaData.tdAttr = 'data-qtip="' + value + '"';

                                        if (record.raw.active === false) {
                                            return '<div class="employee-inactive">' + value + '</div>';
                                        }
                                        return value;
                                    },
                                    resizable: false,
                                    dataIndex: 'surname',
                                    hideable: false,
                                    flex: 1
                                },
                                {
                                    xtype: 'gridcolumn',
                                    renderer: function(value, metaData, record, rowIndex, colIndex, store, view) {
                                        metaData.tdAttr = 'data-qtip="' + value + '"';

                                        if (record.raw.active === false) {
                                            return '<div class="employee-inactive">' + value + '</div>';
                                        }
                                        return value;
                                    },
                                    resizable: false,
                                    dataIndex: 'name',
                                    hideable: false,
                                    flex: 1
                                }
                            ],
                            selModel: Ext.create('Ext.selection.RowModel', {
                                mode: 'SINGLE',
                                listeners: {
                                    selectionchange: {
                                        fn: me.onRowModelSelectionChange,
                                        scope: me
                                    }
                                }
                            }),
                            features: [
                                {
                                    ftype: 'grouping',
                                    enableGroupingMenu: false,
                                    enableNoGroups: false,
                                    groupHeaderTpl: [
                                        '{name} ({children.length})'
                                    ],
                                    startCollapsed: true
                                }
                            ]
                        }
                    ],
                    listeners: {
                        render: {
                            fn: me.onEmployeeLeftPnlRender,
                            scope: me
                        }
                    }
                },
                {
                    xtype: 'tabpanel',
                    checkLockedMonth: function(ids, window, fields) {
                        Ext.Ajax.request({
                            url: '/mc2/applications/employees/extraordinary/lastStoredMonth.php',
                            params: {
                                employees: ids
                            },
                            success: function(response) {
                                res = Ext.decode(response.responseText);
                                if (res.success === true) {
                                    Ext.widget(window).show();

                                    dateArr = res.results;
                                    if (dateArr.year != -1 && dateArr.month != -1) {
                                        date = new Date(dateArr.year, dateArr.month, 1);
                                        Ext.each(fields, function(field) {
                                            Ext.getCmp(field).setMinValue(date);
                                        });
                                    }
                                }
                            }
                        });
                    },
                    permissible: true,
                    flex: 1,
                    region: 'center',
                    splitterResize: false,
                    disabled: true,
                    id: 'EmployeeTabPnl',
                    itemId: 'EmployeeTabPnl',
                    iconCls: 'icon-user',
                    title: 'Nessun Personale selezionato',
                    activeTab: 0,
                    items: [
                        {
                            xtype: 'panel',
                            permissible: true,
                            id: 'EmpGeneralMenuTab',
                            itemId: 'EmpGeneralMenuTab',
                            width: 900,
                            layout: 'fit',
                            bodyCls: [
                                'bck-content',
                                'x-panel-body-default',
                                'x-layout-fit'
                            ],
                            title: 'Anagrafica',
                            tabConfig: {
                                xtype: 'tab',
                                id: 'EmployeeGeneralTab',
                                itemId: 'EmployeeGeneralTab',
                                iconCls: 'icon-vcard',
                                textAlign: 'left'
                            },
                            items: [
                                {
                                    xtype: 'form',
                                    border: false,
                                    id: 'EmployeeFrm',
                                    itemId: 'EmployeeFrm',
                                    autoScroll: true,
                                    bodyCls: [
                                        'bck-content',
                                        'x-panel-body-default',
                                        'x-box-layout-ct'
                                    ],
                                    header: false,
                                    url: '/mc2/applications/employees/employee/write.php',
                                    layout: {
                                        type: 'vbox',
                                        align: 'stretch'
                                    },
                                    items: [
                                        {
                                            xtype: 'container',
                                            layout: {
                                                type: 'vbox',
                                                align: 'stretch'
                                            },
                                            items: [
                                                {
                                                    xtype: 'container',
                                                    flex: 1,
                                                    layout: {
                                                        type: 'hbox',
                                                        align: 'stretch',
                                                        pack: 'center'
                                                    },
                                                    items: [
                                                        {
                                                            xtype: 'radiogroup',
                                                            flex: 1,
                                                            padding: 5,
                                                            fieldLabel: 'Tipologia',
                                                            invalidCls: 'x-form-invalid',
                                                            labelAlign: 'right',
                                                            labelStyle: 'font-weight:bold',
                                                            labelWidth: 120,
                                                            msgTarget: 'side',
                                                            combineErrors: true,
                                                            allowBlank: false,
                                                            blankText: 'Il tipo di personale è obbligatorio',
                                                            layout: {
                                                                type: 'vbox',
                                                                align: 'stretch',
                                                                pack: 'center'
                                                            },
                                                            items: [
                                                                {
                                                                    xtype: 'radiofield',
                                                                    msgTarget: 'side',
                                                                    name: 'liquid_group',
                                                                    boxLabel: 'ATA',
                                                                    checked: true,
                                                                    inputValue: '0002'
                                                                },
                                                                {
                                                                    xtype: 'radiofield',
                                                                    msgTarget: 'side',
                                                                    name: 'liquid_group',
                                                                    boxLabel: 'DOCENTE',
                                                                    inputValue: '0001'
                                                                }
                                                            ]
                                                        },
                                                        {
                                                            xtype: 'checkboxfield',
                                                            flex: 1,
                                                            padding: 5,
                                                            fieldLabel: 'Attivo',
                                                            labelAlign: 'right',
                                                            labelStyle: 'font-weight:bold',
                                                            labelWidth: 120,
                                                            name: 'active',
                                                            boxLabel: '',
                                                            checked: true
                                                        }
                                                    ]
                                                },
                                                {
                                                    xtype: 'container',
                                                    flex: 1,
                                                    layout: 'hbox',
                                                    items: [
                                                        {
                                                            xtype: 'container',
                                                            flex: 1,
                                                            padding: 5,
                                                            layout: {
                                                                type: 'vbox',
                                                                align: 'stretch'
                                                            },
                                                            items: [
                                                                {
                                                                    xtype: 'textfield',
                                                                    id: 'EmployeeSurnameTxt',
                                                                    itemId: 'EmployeeSurnameTxt',
                                                                    fieldLabel: 'Cognome',
                                                                    labelAlign: 'right',
                                                                    labelStyle: 'font-weight:bold',
                                                                    labelWidth: 120,
                                                                    msgTarget: 'side',
                                                                    inputId: 'surname',
                                                                    allowBlank: false,
                                                                    allowOnlyWhitespace: false
                                                                },
                                                                {
                                                                    xtype: 'textfield',
                                                                    id: 'EmployeeNameTxt',
                                                                    itemId: 'EmployeeNameTxt',
                                                                    fieldLabel: 'Nome',
                                                                    labelAlign: 'right',
                                                                    labelStyle: 'font-weight:bold',
                                                                    labelWidth: 120,
                                                                    msgTarget: 'side',
                                                                    inputId: 'name',
                                                                    allowBlank: false,
                                                                    allowOnlyWhitespace: false
                                                                },
                                                                {
                                                                    xtype: 'datefield',
                                                                    id: 'EmployeeBirthdate',
                                                                    itemId: 'EmployeeBirthdate',
                                                                    fieldLabel: 'Nato il',
                                                                    labelAlign: 'right',
                                                                    labelWidth: 120,
                                                                    inputId: 'birthdate',
                                                                    editable: false,
                                                                    format: 'd/m/Y',
                                                                    showToday: false,
                                                                    startDay: 1,
                                                                    submitFormat: 'd-m-Y'
                                                                },
                                                                {
                                                                    xtype: 'combobox',
                                                                    id: 'EmployeeBirthplace',
                                                                    itemId: 'EmployeeBirthplace',
                                                                    fieldLabel: 'Comune di nascita',
                                                                    labelAlign: 'right',
                                                                    labelWidth: 120,
                                                                    inputId: 'birthplace',
                                                                    hideTrigger: true,
                                                                    displayField: 'description',
                                                                    forceSelection: true,
                                                                    minChars: 2,
                                                                    queryMode: 'local',
                                                                    store: 'CoreCities',
                                                                    typeAhead: true,
                                                                    valueField: 'city_id',
                                                                    listeners: {
                                                                        focus: {
                                                                            fn: me.onEmployeeBirthplaceFocus,
                                                                            scope: me
                                                                        }
                                                                    }
                                                                },
                                                                {
                                                                    xtype: 'combobox',
                                                                    id: 'EmployeeBirthstate',
                                                                    itemId: 'EmployeeBirthstate',
                                                                    fieldLabel: 'Stato di nascita',
                                                                    labelAlign: 'right',
                                                                    labelWidth: 120,
                                                                    inputId: 'state_birth',
                                                                    editable: false,
                                                                    displayField: 'description',
                                                                    forceSelection: true,
                                                                    store: 'CoreCountries',
                                                                    valueField: 'code'
                                                                }
                                                            ]
                                                        },
                                                        {
                                                            xtype: 'container',
                                                            flex: 1,
                                                            padding: 5,
                                                            layout: {
                                                                type: 'vbox',
                                                                align: 'stretch'
                                                            },
                                                            items: [
                                                                {
                                                                    xtype: 'combobox',
                                                                    flex: 1,
                                                                    id: 'EmployeeGender',
                                                                    itemId: 'EmployeeGender',
                                                                    fieldLabel: 'Sesso',
                                                                    labelAlign: 'right',
                                                                    labelWidth: 120,
                                                                    inputId: 'gender',
                                                                    editable: false,
                                                                    forceSelection: true,
                                                                    queryMode: 'local',
                                                                    store: 'Gender',
                                                                    valueField: 'id'
                                                                },
                                                                {
                                                                    xtype: 'textfield',
                                                                    id: 'EmployeeFiscalCode',
                                                                    itemId: 'EmployeeFiscalCode',
                                                                    fieldLabel: 'Codice fiscale',
                                                                    labelAlign: 'right',
                                                                    labelWidth: 120,
                                                                    inputId: 'fiscal_code'
                                                                },
                                                                {
                                                                    xtype: 'combobox',
                                                                    flex: 1,
                                                                    id: 'EmployeeSocialPos',
                                                                    itemId: 'EmployeeSocialPos',
                                                                    fieldLabel: 'Stato sociale',
                                                                    labelAlign: 'right',
                                                                    labelWidth: 120,
                                                                    inputId: 'social_position',
                                                                    editable: false,
                                                                    displayField: 'description',
                                                                    forceSelection: true,
                                                                    store: 'SocialPosition',
                                                                    valueField: 'soc_pos_id'
                                                                },
                                                                {
                                                                    xtype: 'numberfield',
                                                                    validator: function(value) {
                                                                        var store = Ext.getStore('Employees'),
                                                                            currentEmployee = Ext.getCmp('EmployeePnl').getSelectedEmployee(),
                                                                            found = "";

                                                                        if (value.length > 0) {
                                                                            store.each(function(rec) {
                                                                                if (value !== 0 && value.length > 0 && rec.raw.badge_number == value && currentEmployee.employee_id != rec.raw.employee_id) {
                                                                                    found = rec.raw.surname + " " + rec.raw.name;
                                                                                    return false;
                                                                                }
                                                                            });
                                                                        }

                                                                        if (found === "") {
                                                                            return true;
                                                                        } else {
                                                                            return "Questo numero di badge è già assegnato a " + found;
                                                                        }
                                                                    },
                                                                    id: 'EmployeeBadge',
                                                                    itemId: 'EmployeeBadge',
                                                                    fieldLabel: 'Numero di Badge',
                                                                    labelAlign: 'right',
                                                                    labelWidth: 120,
                                                                    msgTarget: 'under',
                                                                    inputId: 'badge_number',
                                                                    hideTrigger: true,
                                                                    allowDecimals: false,
                                                                    allowExponential: false
                                                                },
                                                                {
                                                                    xtype: 'hiddenfield',
                                                                    id: 'EmployeeId',
                                                                    itemId: 'EmployeeId',
                                                                    fieldLabel: 'Label',
                                                                    inputId: 'employee_id'
                                                                }
                                                            ]
                                                        }
                                                    ]
                                                }
                                            ]
                                        },
                                        {
                                            xtype: 'tabpanel',
                                            id: 'EmployeeContactTab',
                                            itemId: 'EmployeeContactTab',
                                            padding: 5,
                                            activeTab: 0,
                                            items: [
                                                {
                                                    xtype: 'panel',
                                                    padding: 5,
                                                    layout: 'hbox',
                                                    title: 'Residenza',
                                                    tabConfig: {
                                                        xtype: 'tab',
                                                        textAlign: 'left'
                                                    },
                                                    items: [
                                                        {
                                                            xtype: 'container',
                                                            flex: 1,
                                                            padding: 5,
                                                            layout: {
                                                                type: 'vbox',
                                                                align: 'stretch'
                                                            },
                                                            items: [
                                                                {
                                                                    xtype: 'textfield',
                                                                    fieldLabel: 'Indirizzo',
                                                                    labelAlign: 'right',
                                                                    inputId: 'res_address'
                                                                },
                                                                {
                                                                    xtype: 'combobox',
                                                                    id: 'EmployeeResidenceCity',
                                                                    itemId: 'EmployeeResidenceCity',
                                                                    fieldLabel: 'Comune',
                                                                    labelAlign: 'right',
                                                                    inputId: 'res_city_id',
                                                                    hideTrigger: true,
                                                                    displayField: 'description',
                                                                    forceSelection: true,
                                                                    minChars: 2,
                                                                    queryMode: 'local',
                                                                    store: 'CoreCities',
                                                                    typeAhead: true,
                                                                    valueField: 'city_id',
                                                                    listeners: {
                                                                        focus: {
                                                                            fn: me.onComboboxFocus,
                                                                            scope: me
                                                                        }
                                                                    }
                                                                },
                                                                {
                                                                    xtype: 'textfield',
                                                                    fieldLabel: 'CAP',
                                                                    labelAlign: 'right',
                                                                    inputId: 'res_cap',
                                                                    maskRe: /[\d]+/,
                                                                    maxLength: 5
                                                                },
                                                                {
                                                                    xtype: 'hiddenfield',
                                                                    fieldLabel: 'Label',
                                                                    inputId: 'residence_id'
                                                                },
                                                                {
                                                                    xtype: 'textfield',
                                                                    fieldLabel: 'Telefono',
                                                                    labelAlign: 'right',
                                                                    inputId: 'res_phone_num'
                                                                }
                                                            ]
                                                        },
                                                        {
                                                            xtype: 'container',
                                                            flex: 1,
                                                            padding: 5,
                                                            layout: {
                                                                type: 'vbox',
                                                                align: 'stretch'
                                                            },
                                                            items: [
                                                                {
                                                                    xtype: 'textfield',
                                                                    fieldLabel: 'Celulare',
                                                                    labelAlign: 'right',
                                                                    inputId: 'res_mobile'
                                                                },
                                                                {
                                                                    xtype: 'textfield',
                                                                    fieldLabel: 'Fax',
                                                                    labelAlign: 'right',
                                                                    inputId: 'res_fax'
                                                                },
                                                                {
                                                                    xtype: 'textfield',
                                                                    fieldLabel: 'Email',
                                                                    labelAlign: 'right',
                                                                    inputId: 'res_email'
                                                                }
                                                            ]
                                                        }
                                                    ]
                                                },
                                                {
                                                    xtype: 'panel',
                                                    padding: 5,
                                                    layout: 'hbox',
                                                    title: 'Domicilio',
                                                    tabConfig: {
                                                        xtype: 'tab',
                                                        textAlign: 'left'
                                                    },
                                                    items: [
                                                        {
                                                            xtype: 'container',
                                                            flex: 1,
                                                            padding: 5,
                                                            layout: {
                                                                type: 'vbox',
                                                                align: 'stretch'
                                                            },
                                                            items: [
                                                                {
                                                                    xtype: 'textfield',
                                                                    fieldLabel: 'Indirizzo',
                                                                    labelAlign: 'right',
                                                                    inputId: 'add_address'
                                                                },
                                                                {
                                                                    xtype: 'combobox',
                                                                    id: 'EmployeeAddressCity',
                                                                    itemId: 'EmployeeAddressCity',
                                                                    fieldLabel: 'Comune',
                                                                    labelAlign: 'right',
                                                                    inputId: 'add_city_id',
                                                                    hideTrigger: true,
                                                                    displayField: 'description',
                                                                    forceSelection: true,
                                                                    minChars: 2,
                                                                    queryMode: 'local',
                                                                    store: 'CoreCities',
                                                                    typeAhead: true,
                                                                    valueField: 'city_id',
                                                                    listeners: {
                                                                        focus: {
                                                                            fn: me.onComboboxFocus1,
                                                                            scope: me
                                                                        }
                                                                    }
                                                                },
                                                                {
                                                                    xtype: 'textfield',
                                                                    fieldLabel: 'CAP',
                                                                    labelAlign: 'right',
                                                                    inputId: 'add_cap',
                                                                    maskRe: /[\d]+/,
                                                                    maxLength: 5
                                                                },
                                                                {
                                                                    xtype: 'hiddenfield',
                                                                    fieldLabel: 'Label',
                                                                    inputId: 'address_id'
                                                                },
                                                                {
                                                                    xtype: 'textfield',
                                                                    fieldLabel: 'Telefono',
                                                                    labelAlign: 'right',
                                                                    inputId: 'add_phone_num'
                                                                }
                                                            ]
                                                        },
                                                        {
                                                            xtype: 'container',
                                                            flex: 1,
                                                            padding: 5,
                                                            layout: {
                                                                type: 'vbox',
                                                                align: 'stretch'
                                                            },
                                                            items: [
                                                                {
                                                                    xtype: 'textfield',
                                                                    fieldLabel: 'Celulare',
                                                                    labelAlign: 'right',
                                                                    inputId: 'add_mobile'
                                                                },
                                                                {
                                                                    xtype: 'textfield',
                                                                    fieldLabel: 'Fax',
                                                                    labelAlign: 'right',
                                                                    inputId: 'add_fax'
                                                                },
                                                                {
                                                                    xtype: 'textfield',
                                                                    fieldLabel: 'Email',
                                                                    labelAlign: 'right',
                                                                    inputId: 'add_email'
                                                                }
                                                            ]
                                                        }
                                                    ]
                                                }
                                            ]
                                        }
                                    ],
                                    dockedItems: [
                                        {
                                            xtype: 'toolbar',
                                            flex: 1,
                                            dock: 'top',
                                            items: [
                                                {
                                                    xtype: 'button',
                                                    id: 'EmployeeEditBtn',
                                                    itemId: 'EmployeeEditBtn',
                                                    iconCls: 'icon-disk',
                                                    text: 'Salva',
                                                    listeners: {
                                                        click: {
                                                            fn: me.onButtonClick5,
                                                            scope: me
                                                        }
                                                    }
                                                }
                                            ]
                                        }
                                    ]
                                }
                            ]
                        },
                        {
                            xtype: 'panel',
                            copyAbsence: function(absence, employees) {
                                Ext.getCmp('AbsencesTabPnl').setLoading();

                                Ext.Ajax.request({
                                    url: '/mc2/applications/employees/absences/copy.php',
                                    params:{
                                        absence_id: absence,
                                        employee_ids: employees
                                    },
                                    success: function(response, opts) {
                                        Ext.getCmp('AbsencesTabPnl').setLoading(false);
                                        mc2ui.app.showNotifySave();
                                    }
                                });
                            },
                            permissible: true,
                            cls: 'permissible',
                            id: 'AbsencesTabPnl',
                            itemId: 'AbsencesTabPnl',
                            layout: 'fit',
                            title: 'Assenze',
                            items: [
                                {
                                    xtype: 'menu',
                                    permissible: true,
                                    id: 'AbsenceEditMn',
                                    itemId: 'AbsenceEditMn',
                                    items: [
                                        {
                                            xtype: 'menuitem',
                                            handler: function(item, e) {
                                                var employees = Ext.getCmp('EmployeeListPnl').getSelectionModel().getSelection();

                                                Ext.getCmp('EmployeeTabPnl').checkLockedMonth(employees[0].get('employee_id'), 'AbsenceNewWin', new Array('AbsStartDate'));
                                            },
                                            id: 'contextAbsenceEdit',
                                            itemId: 'contextAbsenceEdit',
                                            iconCls: 'icon-pencil',
                                            text: 'Modifica'
                                        },
                                        {
                                            xtype: 'menuitem',
                                            handler: function(item, e) {
                                                var record = Ext.getCmp('AbsencesListPnl').getSelectionModel().getSelection()[0];

                                                Ext.Msg.show({
                                                    title: record.get('ab_kind_str'),
                                                    msg: 'Sei sicuro di voler eliminare questa assenza?',
                                                    buttons: Ext.Msg.YESNO,
                                                    fn: function(r){
                                                        if (r == 'yes') {
                                                            store = Ext.getStore('Absences');
                                                            store.remove(record);
                                                            store.sync();
                                                        }
                                                    }
                                                });
                                            },
                                            id: 'contextAbsenceDelete',
                                            itemId: 'contextAbsenceDelete',
                                            iconCls: 'icon-cancel',
                                            text: 'Elimina'
                                        },
                                        {
                                            xtype: 'menuseparator'
                                        },
                                        {
                                            xtype: 'menuitem',
                                            handler: function(item, e) {
                                                Ext.widget('EmployeeAbsenceCopyWin').show();
                                            },
                                            id: 'contextAbsenceCopy',
                                            itemId: 'contextAbsenceCopy',
                                            iconCls: 'icon-page_copy',
                                            text: 'Copia su altro personale'
                                        },
                                        {
                                            xtype: 'menuseparator'
                                        },
                                        {
                                            xtype: 'menuitem',
                                            handler: function(item, e) {
                                                Ext.widget('AbsenceDecretoWin').show();
                                                var record = Ext.getCmp('AbsencesListPnl').getSelectionModel().getSelection()[0];

                                                var record = Ext.getStore('Decreti').load({
                                                    params:{
                                                        absence_id: record.get('absence_id')
                                                    },
                                                    callback:function(r1,res){
                                                        if (res.response != undefined){
                                                            r = Ext.JSON.decode(res.response.responseText);
                                                            if (r.success === true){
                                                                Ext.getCmp('DecretoEditorHtml').setValue(r['results']['html']);
                                                            }
                                                        }
                                                    }
                                                });
                                            },
                                            id: 'contextAbsenceDecreet',
                                            itemId: 'contextAbsenceDecreet',
                                            iconCls: 'icon-overlays',
                                            text: 'Visualizza decreto'
                                        }
                                    ]
                                },
                                {
                                    xtype: 'gridpanel',
                                    border: false,
                                    id: 'AbsencesListPnl',
                                    itemId: 'AbsencesListPnl',
                                    emptyText: 'Nessuna assenza caricata',
                                    sortableColumns: false,
                                    store: 'Absences',
                                    viewConfig: {
                                        listeners: {
                                            itemcontextmenu: {
                                                fn: me.onGridviewItemContextMenu,
                                                scope: me
                                            }
                                        }
                                    },
                                    columns: [
                                        {
                                            xtype: 'actioncolumn',
                                            draggable: false,
                                            width: 20,
                                            resizable: false,
                                            align: 'center',
                                            dataIndex: 'stack',
                                            hideable: false,
                                            items: [
                                                {
                                                    getClass: function(v, metadata, r, rowIndex, colIndex, store) {
                                                        if (r.raw.stack === null || r.raw.multiple > 1){
                                                            return 'icon-error';
                                                        } else {
                                                            return '';
                                                        }

                                                    },
                                                    getTip: function(v, meta, record, rowIndex, colIndex, store) {
                                                        if (record.raw.stack === null || record.raw.multiple > 1){
                                                            var i = '<ol>';
                                                            if (record.raw.stack === null){
                                                                i += '<li>Questa assenza non è attualmente legata a nessun monteore</li>';
                                                            }
                                                            if (record.raw.multiple > 1){
                                                                i += '<li>Sono presenti ' + record.raw.multiple + ' assenze durante questo periodo</li>';
                                                            }
                                                            i += '</ol>';
                                                            return i;
                                                        }
                                                    }
                                                }
                                            ]
                                        },
                                        {
                                            xtype: 'gridcolumn',
                                            resizable: false,
                                            hideable: false,
                                            text: 'Inizio',
                                            columns: [
                                                {
                                                    xtype: 'gridcolumn',
                                                    draggable: false,
                                                    width: 80,
                                                    resizable: false,
                                                    align: 'center',
                                                    dataIndex: 'start_date',
                                                    hideable: false,
                                                    text: 'Data'
                                                },
                                                {
                                                    xtype: 'gridcolumn',
                                                    draggable: false,
                                                    width: 60,
                                                    resizable: false,
                                                    align: 'center',
                                                    dataIndex: 'start_time',
                                                    hideable: false,
                                                    text: 'Ora'
                                                }
                                            ]
                                        },
                                        {
                                            xtype: 'gridcolumn',
                                            resizable: false,
                                            hideable: false,
                                            text: 'Fine',
                                            columns: [
                                                {
                                                    xtype: 'gridcolumn',
                                                    draggable: false,
                                                    width: 80,
                                                    resizable: false,
                                                    align: 'center',
                                                    dataIndex: 'end_date',
                                                    hideable: false,
                                                    text: 'Data'
                                                },
                                                {
                                                    xtype: 'gridcolumn',
                                                    draggable: false,
                                                    width: 60,
                                                    resizable: false,
                                                    align: 'center',
                                                    dataIndex: 'end_time',
                                                    hideable: false,
                                                    text: 'Ora'
                                                }
                                            ]
                                        },
                                        {
                                            xtype: 'gridcolumn',
                                            draggable: false,
                                            width: 250,
                                            dataIndex: 'description_code',
                                            hideable: false,
                                            text: 'Tipo',
                                            flex: 1
                                        },
                                        {
                                            xtype: 'actioncolumn',
                                            width: 20,
                                            resizable: false,
                                            hideable: false,
                                            items: [
                                                {
                                                    getClass: function(v, metadata, r, rowIndex, colIndex, store) {
                                                        if (r.get('note') !== '') {
                                                            return 'icon-note';
                                                        }
                                                    },
                                                    handler: function(view, rowIndex, colIndex, item, e, record, row) {
                                                        if (record.raw.note !== '') {
                                                            Ext.MessageBox.alert('Note assenza ' + record.raw.ab_kind + ' (' + record.raw.start_date + ' - ' + record.raw.end_date + ')', record.raw.note);
                                                        }
                                                    },
                                                    getTip: function(v, meta, record, rowIndex, colIndex, store) {
                                                        if (record.raw.note !== '') {
                                                            return 'Note assenza';
                                                        }
                                                    }
                                                }
                                            ]
                                        }
                                    ],
                                    dockedItems: [
                                        {
                                            xtype: 'toolbar',
                                            dock: 'top',
                                            items: [
                                                {
                                                    xtype: 'button',
                                                    handler: function(button, e) {
                                                        var employees = Ext.getCmp('EmployeeListPnl').getSelectionModel().getSelection();

                                                        Ext.getCmp('AbsencesListPnl').getSelectionModel().deselectAll();

                                                        Ext.getCmp('EmployeeTabPnl').checkLockedMonth(employees[0].get('employee_id'), 'AbsenceNewWin', new Array('AbsStartDate'));
                                                    },
                                                    id: 'AbsenceNewBtn',
                                                    itemId: 'AbsenceNewBtn',
                                                    iconCls: 'icon-add',
                                                    text: 'Nuova'
                                                }
                                            ]
                                        },
                                        {
                                            xtype: 'pagingtoolbar',
                                            dock: 'bottom',
                                            displayInfo: true,
                                            store: 'Absences',
                                            listeners: {
                                                beforechange: {
                                                    fn: me.onPagingtoolbarBeforeChange,
                                                    scope: me
                                                }
                                            }
                                        }
                                    ]
                                }
                            ],
                            tabConfig: {
                                xtype: 'tab',
                                id: 'EmployeeAbsencesTab',
                                itemId: 'EmployeeAbsencesTab',
                                iconCls: 'icon-date_error',
                                textAlign: 'left'
                            },
                            listeners: {
                                activate: {
                                    fn: me.onPanelActivate,
                                    scope: me
                                }
                            }
                        },
                        {
                            xtype: 'panel',
                            loadData: function() {
                                var month = Ext.getCmp('TimetableMonthCmb').getValue(),
                                    year = Ext.getCmp('TimetableYearCmb').getValue();

                                if (Ext.getCmp('TimeTableTabPnl').isVisible() === true) {
                                    Ext.getCmp('TimetableCalendarPnl').setLoading();

                                    Ext.getStore('CalendarTimeTables').load({
                                        params: {
                                            employee_id: Ext.getCmp('EmployeePnl').getSelectedEmployee().employee_id,
                                            month: month,
                                            year: year
                                        },
                                        callback: function(){
                                            Ext.getCmp('TimetableCalendarPnl').setLoading(false);
                                        }
                                    });
                                    Ext.getCmp('EmployeePnl').updateMonthYear(month, year, 1);
                                }
                            },
                            id: 'TimeTableTabPnl',
                            itemId: 'TimeTableTabPnl',
                            layout: 'fit',
                            bodyCls: [
                                'bck-content',
                                'x-panel-body-default',
                                'x-layout-fit'
                            ],
                            title: 'Orario',
                            tabConfig: {
                                xtype: 'tab',
                                id: 'EmployeeHourTab',
                                itemId: 'EmployeeHourTab',
                                iconCls: 'icon-time',
                                textAlign: 'left',
                                listeners: {
                                    activate: {
                                        fn: me.onEmployeeHourTabActivate,
                                        scope: me
                                    }
                                }
                            },
                            items: [
                                {
                                    xtype: 'panel',
                                    border: false,
                                    id: 'TimetableCalendarPnl',
                                    itemId: 'TimetableCalendarPnl',
                                    autoScroll: true,
                                    bodyCls: [
                                        'bck-content',
                                        'x-panel-body-default',
                                        'x-box-layout-ct'
                                    ],
                                    header: false,
                                    layout: {
                                        type: 'vbox',
                                        align: 'center'
                                    },
                                    items: [
                                        {
                                            xtype: 'menu',
                                            permissible: true,
                                            cls: 'permissible',
                                            id: 'TimeTableCalendarMn',
                                            itemId: 'TimeTableCalendarMn',
                                            items: [
                                                {
                                                    xtype: 'menuitem',
                                                    handler: function(item, e) {
                                                        var record = Ext.getCmp('TimetableCalendarView').getSelectionModel().getSelection()[0];

                                                        Ext.widget('TimeTableEditWin').show();
                                                        Ext.getStore('TimeTables').load({
                                                            params:{
                                                                ids: record.get('personnel_timetable_id'),
                                                                employee_id: record.get('employee_id'),
                                                                date: record.get('date')
                                                            },
                                                            callback: function(res){
                                                                Ext.getCmp('PeriodTimetableEditLbl').setText('Data: ' + record.get('date'));
                                                            }
                                                        });
                                                    },
                                                    id: 'contextTimetableEdit',
                                                    itemId: 'contextTimetableEdit',
                                                    iconCls: 'icon-pencil',
                                                    text: 'Crea / Modifica giorno'
                                                },
                                                {
                                                    xtype: 'menuitem',
                                                    handler: function(item, e) {
                                                        var record = Ext.getCmp('TimetableCalendarView').getSelectionModel().getSelection()[0];

                                                        Ext.getStore('CalendarTimeTables').destroy({
                                                            params:{
                                                                ids: record.get('personnel_timetable_id')
                                                            },
                                                            callback: function(res){
                                                                Ext.getCmp('TimeTableTabPnl').loadData();
                                                            }
                                                        });
                                                    },
                                                    id: 'contextTimetableDelete',
                                                    itemId: 'contextTimetableDelete',
                                                    iconCls: 'icon-cancel',
                                                    text: 'Elimina giorno'
                                                },
                                                {
                                                    xtype: 'menuseparator'
                                                },
                                                {
                                                    xtype: 'menuitem',
                                                    handler: function(item, e) {
                                                        if (Ext.getCmp('TimeTableCopyWeekWin') == undefined ){
                                                            Ext.widget('TimeTableCopyWeekWin').show();
                                                        } else {
                                                            Ext.getCmp('TimeTableCopyWeekWin').show();
                                                        }

                                                        var employee_id = Ext.getCmp('EmployeePnl').getSelectedEmployee().employee_id;

                                                        Ext.getStore('WeekBorders').load();


                                                        var index = Ext.getCmp('TimeTableCopyEmp').getStore().getById(employee_id).index;
                                                        Ext.getCmp('TimeTableCopyEmp').getSelectionModel().select(index);

                                                    },
                                                    id: 'contextTimetableCopyWeek',
                                                    itemId: 'contextTimetableCopyWeek',
                                                    iconCls: 'icon-page_copy',
                                                    text: 'Copia settimana'
                                                },
                                                {
                                                    xtype: 'menuitem',
                                                    handler: function(item, e) {
                                                        var record = Ext.getCmp('TimetableCalendarView').getSelectionModel().getSelection()[0];

                                                        Ext.getStore('CalendarTimeTables').destroy({
                                                            params:{
                                                                week: true,
                                                                date: record.get('date'),
                                                                employee_id: record.get('employee_id')
                                                            },
                                                            callback: function(res){
                                                                Ext.getCmp('TimeTableTabPnl').loadData();
                                                            }
                                                        });
                                                    },
                                                    id: 'contextTimetableDeleteWeek',
                                                    itemId: 'contextTimetableDeleteWeek',
                                                    width: 194,
                                                    iconCls: 'icon-page_cancel',
                                                    text: 'Elimina settimana'
                                                },
                                                {
                                                    xtype: 'menuseparator'
                                                },
                                                {
                                                    xtype: 'menuitem',
                                                    handler: function(item, e) {
                                                        Ext.widget('TimeTableDeleteWin').show();
                                                    },
                                                    id: 'contextTimetableDeletePeriod',
                                                    itemId: 'contextTimetableDeletePeriod',
                                                    width: 194,
                                                    iconCls: 'icon-calendar_delete',
                                                    text: 'Elimina periodo'
                                                }
                                            ]
                                        },
                                        {
                                            xtype: 'menu',
                                            permissible: true,
                                            id: 'TimeTableCalendarDisabledMn',
                                            itemId: 'TimeTableCalendarDisabledMn',
                                            items: [
                                                {
                                                    xtype: 'menuitem',
                                                    handler: function(item, e) {
                                                        if (Ext.getCmp('TimeTableCopyWeekWin') == undefined ){
                                                            Ext.widget('TimeTableCopyWeekWin').show();
                                                        } else {
                                                            Ext.getCmp('TimeTableCopyWeekWin').show();
                                                        }

                                                        var employee_id = Ext.getCmp('EmployeePnl').getSelectedEmployee().employee_id;

                                                        Ext.getStore('WeekBorders').load();


                                                        var index = Ext.getCmp('TimeTableCopyEmp').getStore().getById(employee_id).index;
                                                        Ext.getCmp('TimeTableCopyEmp').getSelectionModel().select(index);
                                                    },
                                                    id: 'contextTimetableDisabledCopyWeek',
                                                    itemId: 'contextTimetableDisabledCopyWeek',
                                                    iconCls: 'icon-page_copy',
                                                    text: 'Copia settimana'
                                                }
                                            ]
                                        },
                                        {
                                            xtype: 'dataview',
                                            id: 'TimetableCalendarView',
                                            itemId: 'TimetableCalendarView',
                                            minWidth: 650,
                                            padding: '10 0',
                                            width: 650,
                                            itemSelector: 'div.day-box',
                                            itemTpl: [
                                                '<div class="day-box {css_class}">',
                                                '    <p class="day-header {css_header}">{day}</p>',
                                                '    <div class="day-hour-box {css_box}">',
                                                '        {slots}',
                                                '    </div>',
                                                '</div>'
                                            ],
                                            store: 'CalendarTimeTables',
                                            listeners: {
                                                itemcontextmenu: {
                                                    fn: me.onTimetableCalendarViewItemContextMenu,
                                                    scope: me
                                                }
                                            }
                                        }
                                    ],
                                    dockedItems: [
                                        {
                                            xtype: 'toolbar',
                                            flex: 1,
                                            dock: 'top',
                                            items: [
                                                {
                                                    xtype: 'container',
                                                    flex: 1,
                                                    padding: 5,
                                                    style: 'width: 91%',
                                                    layout: {
                                                        type: 'hbox',
                                                        pack: 'center'
                                                    },
                                                    items: [
                                                        {
                                                            xtype: 'container',
                                                            flex: 1,
                                                            layout: {
                                                                type: 'hbox',
                                                                align: 'stretch',
                                                                pack: 'end'
                                                            },
                                                            items: [
                                                                {
                                                                    xtype: 'button',
                                                                    handler: function(button, e) {
                                                                        var month = Ext.getCmp('TimetableMonthCmb').getValue();
                                                                        var year = Ext.getCmp('TimetableYearCmb').getValue();


                                                                        if (month === null || year === null) {
                                                                            return;
                                                                        }

                                                                        month = month - 1;

                                                                        if (month < 1) {
                                                                            month = 12;
                                                                            year = year - 1 ;
                                                                        }

                                                                        Ext.getCmp('TimetableMonthCmb').setValue(month);
                                                                        Ext.getCmp('TimetableYearCmb').setValue(year);

                                                                        Ext.getCmp('TimeTableTabPnl').loadData();
                                                                    },
                                                                    iconCls: 'icon-bullet_left',
                                                                    text: '',
                                                                    tooltip: 'Mese precedente'
                                                                }
                                                            ]
                                                        },
                                                        {
                                                            xtype: 'container',
                                                            minWidth: 160,
                                                            layout: {
                                                                type: 'hbox',
                                                                align: 'stretch',
                                                                pack: 'center'
                                                            },
                                                            items: [
                                                                {
                                                                    xtype: 'combobox',
                                                                    flex: 1,
                                                                    id: 'TimetableMonthCmb',
                                                                    itemId: 'TimetableMonthCmb',
                                                                    maxWidth: 100,
                                                                    fieldLabel: 'Label',
                                                                    hideLabel: true,
                                                                    editable: false,
                                                                    forceSelection: true,
                                                                    queryMode: 'local',
                                                                    store: 'Months',
                                                                    valueField: 'number',
                                                                    listeners: {
                                                                        select: {
                                                                            fn: me.onTimetableMonthCmbSelect,
                                                                            scope: me
                                                                        }
                                                                    }
                                                                },
                                                                {
                                                                    xtype: 'combobox',
                                                                    flex: 1,
                                                                    id: 'TimetableYearCmb',
                                                                    itemId: 'TimetableYearCmb',
                                                                    maxWidth: 70,
                                                                    fieldLabel: 'Label',
                                                                    hideLabel: true,
                                                                    editable: false,
                                                                    displayField: 'year',
                                                                    queryMode: 'local',
                                                                    store: 'Years',
                                                                    valueField: 'year',
                                                                    listeners: {
                                                                        select: {
                                                                            fn: me.onTimetableYearCmbSelect,
                                                                            scope: me
                                                                        }
                                                                    }
                                                                }
                                                            ]
                                                        },
                                                        {
                                                            xtype: 'container',
                                                            flex: 1,
                                                            layout: {
                                                                type: 'hbox',
                                                                align: 'stretch'
                                                            },
                                                            items: [
                                                                {
                                                                    xtype: 'button',
                                                                    handler: function(button, e) {
                                                                        var month = Ext.getCmp('TimetableMonthCmb').getValue();
                                                                        var year = Ext.getCmp('TimetableYearCmb').getValue();


                                                                        if (month === null || year === null) {
                                                                            return;
                                                                        }

                                                                        month = month + 1;

                                                                        if (month > 12) {
                                                                            month = 1;
                                                                            year = year + 1 ;
                                                                        }

                                                                        Ext.getCmp('TimetableMonthCmb').setValue(month);
                                                                        Ext.getCmp('TimetableYearCmb').setValue(year);

                                                                        Ext.getCmp('TimeTableTabPnl').loadData();
                                                                    },
                                                                    iconCls: 'icon-bullet_right',
                                                                    text: '',
                                                                    tooltip: 'Mese successivo'
                                                                }
                                                            ]
                                                        }
                                                    ]
                                                }
                                            ]
                                        }
                                    ]
                                }
                            ]
                        },
                        {
                            xtype: 'panel',
                            loadData: function() {
                                var month = Ext.getCmp('PresencesMonthCmb').getValue(),
                                    year = Ext.getCmp('PresencesYearCmb').getValue();

                                if (Ext.getCmp('EmployeeInOutMenuTab').isVisible() === true) {
                                    Ext.getStore('Presences').load({
                                        params:{
                                            employee_id: Ext.getCmp('EmployeePnl').getSelectedEmployee().employee_id,
                                            year: year,
                                            month: month
                                        }
                                    });
                                    Ext.getCmp('EmployeePnl').updateMonthYear(month, year, 2);
                                }
                            },
                            permissible: true,
                            id: 'EmployeeInOutMenuTab',
                            itemId: 'EmployeeInOutMenuTab',
                            layout: 'fit',
                            title: 'Timbrature',
                            tabConfig: {
                                xtype: 'tab',
                                id: 'EmployeeInOutTab',
                                itemId: 'EmployeeInOutTab',
                                iconCls: 'icon-arrow_nw_se',
                                textAlign: 'left',
                                listeners: {
                                    activate: {
                                        fn: me.onEmployeeInOutTabActivate,
                                        scope: me
                                    }
                                }
                            },
                            items: [
                                {
                                    xtype: 'gridpanel',
                                    border: false,
                                    id: 'PresencesPerDayList',
                                    itemId: 'PresencesPerDayList',
                                    header: false,
                                    hideCollapseTool: true,
                                    title: 'My Grid Panel',
                                    emptyText: 'Nessuna timbratura in questo mese',
                                    enableColumnHide: false,
                                    enableColumnMove: false,
                                    sortableColumns: false,
                                    store: 'Presences',
                                    dockedItems: [
                                        {
                                            xtype: 'toolbar',
                                            dock: 'top',
                                            items: [
                                                {
                                                    xtype: 'button',
                                                    handler: function(button, e) {
                                                        var employees = Ext.getCmp('EmployeeListPnl').getSelectionModel().getSelection();

                                                        Ext.getCmp('PresencesPerDayList').getSelectionModel().deselectAll();

                                                        Ext.getCmp('EmployeeTabPnl').checkLockedMonth(employees[0].get('employee_id'), 'PresencesNewWin', new Array('PresenceDate'));

                                                        //Ext.widget('PresencesNewWin').show();
                                                        // Loads Employee id to the form
                                                        //var employee = Ext.getCmp('EmployeePnl').getSelectedEmployee();
                                                        //Ext.getCmp('PresencesEmployeeId').setValue(employees[0].get('employee_id'));
                                                    },
                                                    id: 'EmployeePresenceNewBtn',
                                                    itemId: 'EmployeePresenceNewBtn',
                                                    iconCls: 'icon-add',
                                                    text: 'Nuova'
                                                },
                                                {
                                                    xtype: 'container',
                                                    flex: 1
                                                },
                                                {
                                                    xtype: 'container',
                                                    margin: '0 10',
                                                    padding: '5 0',
                                                    layout: {
                                                        type: 'hbox',
                                                        align: 'middle',
                                                        pack: 'center'
                                                    },
                                                    items: [
                                                        {
                                                            xtype: 'container',
                                                            flex: 1,
                                                            layout: {
                                                                type: 'hbox',
                                                                align: 'stretch',
                                                                pack: 'end'
                                                            },
                                                            items: [
                                                                {
                                                                    xtype: 'button',
                                                                    handler: function(button, e) {
                                                                        var month = Ext.getCmp('PresencesMonthCmb').getValue();
                                                                        var year = Ext.getCmp('PresencesYearCmb').getValue();


                                                                        if(month === null || year === null){
                                                                            return;
                                                                        }

                                                                        month = month - 1;

                                                                        if (month < 1) {
                                                                            month = 12;
                                                                            year = year - 1 ;
                                                                        }

                                                                        Ext.getCmp('PresencesMonthCmb').setValue(month);
                                                                        Ext.getCmp('PresencesYearCmb').setValue(year);

                                                                        Ext.getCmp('EmployeeInOutMenuTab').loadData();
                                                                    },
                                                                    iconCls: 'icon-bullet_left',
                                                                    text: '',
                                                                    tooltip: 'Mese precedente'
                                                                }
                                                            ]
                                                        },
                                                        {
                                                            xtype: 'combobox',
                                                            id: 'PresencesMonthCmb',
                                                            itemId: 'PresencesMonthCmb',
                                                            width: 103,
                                                            fieldLabel: 'Label',
                                                            hideLabel: true,
                                                            editable: false,
                                                            forceSelection: true,
                                                            queryMode: 'local',
                                                            store: 'Months',
                                                            valueField: 'number',
                                                            listeners: {
                                                                select: {
                                                                    fn: me.onPresencesMonthCmbSelect,
                                                                    scope: me
                                                                }
                                                            }
                                                        },
                                                        {
                                                            xtype: 'combobox',
                                                            id: 'PresencesYearCmb',
                                                            itemId: 'PresencesYearCmb',
                                                            width: 68,
                                                            fieldLabel: 'Label',
                                                            hideLabel: true,
                                                            editable: false,
                                                            displayField: 'year',
                                                            queryMode: 'local',
                                                            store: 'Years',
                                                            valueField: 'year',
                                                            listeners: {
                                                                select: {
                                                                    fn: me.onPresencesYearCmbSelect,
                                                                    scope: me
                                                                }
                                                            }
                                                        },
                                                        {
                                                            xtype: 'container',
                                                            flex: 1,
                                                            layout: {
                                                                type: 'hbox',
                                                                align: 'stretch'
                                                            },
                                                            items: [
                                                                {
                                                                    xtype: 'button',
                                                                    handler: function(button, e) {
                                                                        var month = Ext.getCmp('PresencesMonthCmb').getValue();
                                                                        var year = Ext.getCmp('PresencesYearCmb').getValue();


                                                                        if (month === null || year === null) {
                                                                            return;
                                                                        }

                                                                        month = month + 1;

                                                                        if (month > 12) {
                                                                            month = 1;
                                                                            year = year + 1 ;
                                                                        }

                                                                        Ext.getCmp('PresencesMonthCmb').setValue(month);
                                                                        Ext.getCmp('PresencesYearCmb').setValue(year);

                                                                        Ext.getCmp('EmployeeInOutMenuTab').loadData();
                                                                    },
                                                                    iconCls: 'icon-bullet_right',
                                                                    text: '',
                                                                    tooltip: 'Mese successivo'
                                                                }
                                                            ]
                                                        }
                                                    ]
                                                }
                                            ]
                                        }
                                    ],
                                    columns: [
                                        {
                                            xtype: 'actioncolumn',
                                            draggable: false,
                                            width: 37,
                                            resizable: false,
                                            align: 'center',
                                            dataIndex: 'error',
                                            hideable: false,
                                            items: [
                                                {
                                                    getClass: function(v, metadata, r, rowIndex, colIndex, store) {
                                                        if(r.get('error')==1){
                                                            return 'icon-exclamation';
                                                        } else if(r.get('error')==2){
                                                            return 'icon-error';
                                                        } else {
                                                            return '';
                                                        }
                                                    },
                                                    tooltip: 'L\'ordine delle timbrature è errato oppure la timbratura è stata inserita dal sistema'
                                                }
                                            ]
                                        },
                                        {
                                            xtype: 'gridcolumn',
                                            renderer: function(value, metaData, record, rowIndex, colIndex, store, view) {
                                                if (parseInt(record.get('date')) < 1) {
                                                    return '';
                                                } else {
                                                    return value;
                                                }
                                            },
                                            draggable: false,
                                            width: 112,
                                            resizable: false,
                                            align: 'center',
                                            dataIndex: 'date_hour',
                                            hideable: false,
                                            text: 'Ora'
                                        },
                                        {
                                            xtype: 'gridcolumn',
                                            hidden: true,
                                            width: 112,
                                            resizable: false,
                                            align: 'center',
                                            dataIndex: 'date_day_group'
                                        },
                                        {
                                            xtype: 'gridcolumn',
                                            renderer: function(value, metaData, record, rowIndex, colIndex, store, view) {
                                                if (value == 1) {
                                                    return 'ENTRATA';
                                                } else if(value == 2) {
                                                    return 'USCITA';
                                                }
                                            },
                                            draggable: false,
                                            width: 112,
                                            resizable: false,
                                            align: 'center',
                                            dataIndex: 'original_inout_edit',
                                            hideable: false,
                                            text: 'Entrata/Uscita'
                                        },
                                        {
                                            xtype: 'gridcolumn',
                                            renderer: function(value, metaData, record, rowIndex, colIndex, store, view) {
                                                if (value == 1) {
                                                    return 'NORMALE';
                                                } else if (value == 2) {
                                                    return 'PAUSA PRANZO';
                                                } else {
                                                    return 'USCITA DI SERVIZIO';
                                                }
                                            },
                                            draggable: false,
                                            width: 112,
                                            resizable: false,
                                            align: 'center',
                                            dataIndex: 'type_edit',
                                            hideable: false,
                                            text: 'Tipo passaggio'
                                        },
                                        {
                                            xtype: 'gridcolumn',
                                            draggable: false,
                                            width: 112,
                                            resizable: false,
                                            align: 'center',
                                            dataIndex: 'insertion_mode',
                                            hideable: false,
                                            text: 'Modalità'
                                        },
                                        {
                                            xtype: 'actioncolumn',
                                            resizable: false,
                                            flex: 1,
                                            items: [
                                                {
                                                    getClass: function(v, metadata, r, rowIndex, colIndex, store) {
                                                        if (r.raw.description !== '') {
                                                            return 'icon-note';
                                                        }
                                                    },
                                                    handler: function(view, rowIndex, colIndex, item, e, record, row) {
                                                        var tipo = 'ENTRATA';

                                                        if (record.data.original_inout_edit == 2) {
                                                            tipo = 'USCITTA';
                                                        }

                                                        Ext.MessageBox.alert('Note timbratura in ' + tipo + ' del ' + record.raw.date_day + ' alle ' + record.data.date_hour, record.data.description);
                                                    },
                                                    getTip: function(v, meta, record, rowIndex, colIndex, store) {
                                                        if (record.raw.description !== '') {
                                                            return 'Note timbratura';
                                                        }
                                                    }
                                                }
                                            ]
                                        }
                                    ],
                                    features: [
                                        {
                                            ftype: 'grouping',
                                            groupHeaderTpl: [
                                                '{[Ext.Date.format(new Date(values.rows[0].raw.date_edit*1000), \'D j\')]}'
                                            ]
                                        }
                                    ],
                                    listeners: {
                                        itemcontextmenu: {
                                            fn: me.onPresencesPerDayListItemContextMenu,
                                            scope: me
                                        }
                                    }
                                },
                                {
                                    xtype: 'menu',
                                    permissible: true,
                                    hidden: true,
                                    id: 'PresencesPerDayMn',
                                    itemId: 'PresencesPerDayMn',
                                    items: [
                                        {
                                            xtype: 'menuitem',
                                            handler: function(item, e) {
                                                var employees = Ext.getCmp('EmployeeListPnl').getSelectionModel().getSelection();

                                                Ext.getCmp('EmployeeTabPnl').checkLockedMonth(employees[0].get('employee_id'), 'PresencesNewWin', new Array('PresenceDate'));

                                                //Ext.widget('PresencesNewWin').show();
                                                //var record = Ext.getCmp('PresencesPerDayList').getSelectionModel().getSelection()[0];
                                                //form = Ext.getCmp('PresencesForm').getForm();
                                                //form.loadRecord(record);
                                            },
                                            id: 'contextPresenceEdit',
                                            itemId: 'contextPresenceEdit',
                                            iconCls: 'icon-pencil',
                                            text: 'Modifica'
                                        },
                                        {
                                            xtype: 'menuitem',
                                            handler: function(item, e) {
                                                var record = Ext.getCmp('PresencesPerDayList').getSelectionModel().getSelection()[0];

                                                Ext.Msg.show({
                                                    title: 'Cancellazione Timbratura',
                                                    msg: 'Sei sicuro di voler eliminare questa timbratura?',
                                                    buttons: Ext.Msg.YESNO,
                                                    fn: function(r){
                                                        if( r == 'yes' ){
                                                            store = Ext.getStore('Presences');
                                                            store.remove(record);
                                                            store.sync({
                                                                success: function()
                                                                {
                                                                    Ext.getStore('Presences').load({
                                                                        params:{
                                                                            employee_id: Ext.getCmp('EmployeePnl').getSelectedEmployee().employee_id,
                                                                            year: Ext.getCmp('PresencesYearCmb').getValue(),
                                                                            month: Ext.getCmp('PresencesMonthCmb').getValue()
                                                                        }
                                                                    });
                                                                }
                                                            });
                                                        }
                                                    }
                                                });
                                            },
                                            id: 'contextPresenceDelete',
                                            itemId: 'contextPresenceDelete',
                                            iconCls: 'icon-cancel',
                                            text: 'Elimina'
                                        }
                                    ]
                                }
                            ]
                        },
                        {
                            xtype: 'panel',
                            permissible: true,
                            hidden: true,
                            id: 'EmployeeProjectsMenuTab',
                            itemId: 'EmployeeProjectsMenuTab',
                            layout: 'fit',
                            title: 'Progetti',
                            tabConfig: {
                                xtype: 'tab',
                                id: 'EmployeeProjectsTab',
                                itemId: 'EmployeeProjectsTab',
                                iconCls: 'icon-application_osx_cascade',
                                textAlign: 'left'
                            },
                            items: [
                                {
                                    xtype: 'gridpanel',
                                    border: false,
                                    id: 'EmployeeProjectsGrid',
                                    itemId: 'EmployeeProjectsGrid',
                                    resizable: false,
                                    header: false,
                                    title: 'My Grid Panel',
                                    emptyText: 'Nessun progetto abbinato',
                                    enableColumnResize: false,
                                    dockedItems: [
                                        {
                                            xtype: 'toolbar',
                                            dock: 'top',
                                            items: [
                                                {
                                                    xtype: 'button',
                                                    handler: function(button, e) {
                                                        Ext.getStore('BudgetActivities').load({
                                                            callback: function (records){
                                                                eps = Ext.getStore('EmployeeProjects');
                                                                eps.data.items.forEach(function(el){
                                                                    if(Ext.getStore('BudgetActivities').getById(el.get('activ_id').toString()) != null){
                                                                        var index = Ext.getStore('BudgetActivities').getById(el.get('activ_id').toString()).index;
                                                                        Ext.getCmp('BudgetActivitiesGrid').getSelectionModel().select(index, true);
                                                                    }
                                                                });
                                                            }
                                                        });
                                                        Ext.widget('EmployeeProjectsEditWin').show();
                                                    },
                                                    id: 'EmployeeProjectsLinkBtn',
                                                    itemId: 'EmployeeProjectsLinkBtn',
                                                    iconCls: 'icon-arrow_join',
                                                    text: 'Abbina progetti'
                                                }
                                            ]
                                        }
                                    ],
                                    columns: [
                                        {
                                            xtype: 'actioncolumn',
                                            items: [
                                                {

                                                },
                                                {

                                                }
                                            ]
                                        },
                                        {
                                            xtype: 'gridcolumn',
                                            width: 42,
                                            align: 'center',
                                            dataIndex: 'aggreg_code',
                                            text: 'Cod'
                                        },
                                        {
                                            xtype: 'gridcolumn',
                                            width: 37,
                                            align: 'center',
                                            dataIndex: 'aggreg_nr',
                                            text: 'Num'
                                        },
                                        {
                                            xtype: 'gridcolumn',
                                            text: 'Nome'
                                        },
                                        {
                                            xtype: 'gridcolumn',
                                            width: 226,
                                            dataIndex: 'description',
                                            text: 'Descrizione',
                                            flex: 1
                                        },
                                        {
                                            xtype: 'datecolumn',
                                            text: 'Inizio'
                                        },
                                        {
                                            xtype: 'datecolumn',
                                            text: 'Termine'
                                        },
                                        {
                                            xtype: 'actioncolumn',
                                            items: [
                                                {

                                                },
                                                {

                                                }
                                            ]
                                        }
                                    ]
                                }
                            ],
                            listeners: {
                                activate: {
                                    fn: me.onPanelActivate1,
                                    scope: me
                                }
                            }
                        },
                        {
                            xtype: 'panel',
                            setFinalTotal: function() {
                                var ext_in_month = Ext.getStore('ExtraordinaryStored').getRange();
                                var total = parseInt(Ext.getCmp('StartMonthExt').getValue());

                                Ext.each(ext_in_month, function(record){
                                    var val = record.get('total');
                                    if(!val){
                                        val = 0;
                                    }
                                    total += parseInt(val);
                                });

                                var stacks = Ext.getCmp('LockStacksGrid').getStore().getRange();
                                Ext.each(stacks, function(stack){
                                    if(stack.get('recover') == true){
                                        total -= stack.get('totalStartO') - stack.get('totalEndO');
                                    }

                                });

                                Ext.getCmp('EndMonthExt').setValue(total);
                            },
                            loadData: function() {
                                var month = Ext.getCmp('LocksMonthCmb').getValue(),
                                    year = Ext.getCmp('LocksYearCmb').getValue();

                                if (Ext.getCmp('EmployeeLockMonthMenuTab').isVisible() === true) {
                                    Ext.getCmp('EmployeeLockMonthMenuTab').setLoading();

                                    Ext.getStore('ExtraordinaryStored').removeAll();
                                    Ext.getStore('ExtraordinaryAbsStack').removeAll();

                                    Ext.getStore('ExtraordinaryStored').load({
                                        params: {
                                            employee_id: Ext.getCmp('EmployeePnl').getSelectedEmployee().employee_id,
                                            year: year,
                                            month: month
                                        },
                                        callback: function(records, r){
                                            var res = Ext.JSON.decode(r.response.responseText);
                                            if (res.success === true) {
                                                if (res.general.lastLockedMonth != '') {
                                                    Ext.getCmp('LastLockedMonth').setText(res.general.lastLockedMonth);
                                                } else {
                                                    Ext.getCmp('LastLockedMonth').setText('-');
                                                }
                                                Ext.getCmp('StartMonthExt').setValue(res.general.totalStart);
                                                Ext.getCmp('EndMonthExt').setValue(res.general.totalEnd);
                                                Ext.getCmp('StartMonthExtO').setValue(res.general.totalStartO);
                                                Ext.getCmp('EndMonthExtO').setValue(res.general.totalEndO);
                                                Ext.getCmp('MonthNote').setValue(res.general.note);

                                                if (res.general.blocked === true) {
                                                    Ext.getCmp('LockMonthCenterCnt').disable();
                                                    Ext.getCmp('StartMonthExt').disable();
                                                    Ext.getCmp('EndMonthExt').disable();
                                                    Ext.getCmp('MonthNote').disable();
                                                    Ext.getCmp('LockMonthBtn').setIconCls('icon-lock');
                                                    Ext.getCmp('LockMonthBtn').setText('Sblocca mese');
                                                } else {
                                                    Ext.getCmp('LockMonthCenterCnt').enable();
                                                    Ext.getCmp('StartMonthExt').enable();
                                                    Ext.getCmp('EndMonthExt').enable();
                                                    Ext.getCmp('MonthNote').enable();
                                                    Ext.getCmp('LockMonthBtn').setIconCls('icon-lock_open');
                                                    Ext.getCmp('LockMonthBtn').setText('Blocca mese');
                                                }
                                                if (res.can === true) {
                                                    Ext.getCmp('LockMonthBtn').enable();
                                                } else {
                                                    Ext.getCmp('LockMonthBtn').disable();
                                                }
                                                Ext.getStore('ExtraordinaryAbsStack').add(res.stacks);
                                            }
                                            Ext.getCmp('EmployeeLockMonthMenuTab').setLoading(false);
                                            // Add exception : setloading false
                                        }
                                    });
                                    Ext.getCmp('EmployeePnl').updateMonthYear(month, year, 3);
                                }
                            },
                            permissible: true,
                            cls: 'permissible',
                            id: 'EmployeeLockMonthMenuTab',
                            itemId: 'EmployeeLockMonthMenuTab',
                            title: 'Chiusura mese',
                            layout: {
                                type: 'vbox',
                                align: 'stretch'
                            },
                            tabConfig: {
                                xtype: 'tab',
                                id: 'EmployeeLockMonthTab',
                                itemId: 'EmployeeLockMonthTab',
                                iconCls: 'icon-lock',
                                textAlign: 'left'
                            },
                            dockedItems: [
                                {
                                    xtype: 'toolbar',
                                    dock: 'top',
                                    items: [
                                        {
                                            xtype: 'button',
                                            handler: function(button, e) {
                                                var s = Ext.getCmp('LockMonthGrid').getStore();
                                                var days = Ext.encode(Ext.pluck(s.data.items, 'data'));

                                                var ss = Ext.getCmp('LockStacksGrid').getStore();
                                                var stacks =  Ext.encode(Ext.pluck(ss.data.items, 'data'));

                                                var month_data = Ext.encode({
                                                    ext_start: Ext.getCmp('StartMonthExt').getValue(),
                                                    ext_end: Ext.getCmp('EndMonthExt').getValue(),
                                                    ext_start_o: Ext.getCmp('StartMonthExtO').getValue(),
                                                    ext_end_o: Ext.getCmp('EndMonthExtO').getValue(),
                                                    note: Ext.getCmp('MonthNote').getValue()
                                                });

                                                Ext.getCmp('EmployeeLockMonthMenuTab').setLoading();

                                                Ext.Ajax.request({
                                                    url: '/mc2/applications/employees/extraordinary/write.php',
                                                    params: {
                                                        employee_id: Ext.getCmp('EmployeePnl').getSelectedEmployee().employee_id,
                                                        year: Ext.getCmp('LocksYearCmb').getValue(),
                                                        month:Ext.getCmp('LocksMonthCmb').getValue(),
                                                        month_data: month_data,
                                                        stacks: stacks,
                                                        days: days
                                                    },
                                                    success: function(response, opts) {
                                                        Ext.getCmp('EmployeeLockMonthMenuTab').setLoading(false);
                                                        Ext.getCmp('EmployeeLockMonthMenuTab').loadData();
                                                    }
                                                });
                                            },
                                            id: 'LockMonthBtn',
                                            itemId: 'LockMonthBtn',
                                            width: 110,
                                            iconCls: 'icon-lock_open',
                                            text: 'Blocca mese'
                                        },
                                        {
                                            xtype: 'container',
                                            flex: 1,
                                            items: [
                                                {
                                                    xtype: 'label',
                                                    id: '',
                                                    itemId: '',
                                                    padding: 10,
                                                    text: 'Ultimo mese chiuso:'
                                                },
                                                {
                                                    xtype: 'label',
                                                    id: 'LastLockedMonth',
                                                    itemId: 'LastLockedMonth',
                                                    text: '-'
                                                }
                                            ]
                                        },
                                        {
                                            xtype: 'container',
                                            id: 'BlockMonthFilterCnt',
                                            itemId: 'BlockMonthFilterCnt',
                                            margin: '0 10',
                                            layout: {
                                                type: 'hbox',
                                                align: 'middle',
                                                pack: 'center'
                                            },
                                            items: [
                                                {
                                                    xtype: 'container',
                                                    flex: 1,
                                                    layout: {
                                                        type: 'hbox',
                                                        align: 'stretch',
                                                        pack: 'end'
                                                    },
                                                    items: [
                                                        {
                                                            xtype: 'button',
                                                            handler: function(button, e) {
                                                                var month = Ext.getCmp('LocksMonthCmb').getValue();
                                                                var year = Ext.getCmp('LocksYearCmb').getValue();


                                                                if (month === null || year === null) {
                                                                    return;
                                                                }

                                                                month = month - 1;

                                                                if (month < 1) {
                                                                    month = 12;
                                                                    year = year - 1;
                                                                }

                                                                Ext.getCmp('LocksMonthCmb').setValue(month);
                                                                Ext.getCmp('LocksYearCmb').setValue(year);

                                                                Ext.getCmp('EmployeeLockMonthMenuTab').loadData();
                                                            },
                                                            iconCls: 'icon-bullet_left',
                                                            text: '',
                                                            tooltip: 'Mese precedente'
                                                        }
                                                    ]
                                                },
                                                {
                                                    xtype: 'combobox',
                                                    id: 'LocksMonthCmb',
                                                    itemId: 'LocksMonthCmb',
                                                    padding: '5 0',
                                                    width: 100,
                                                    fieldLabel: 'Label',
                                                    hideLabel: true,
                                                    editable: false,
                                                    forceSelection: true,
                                                    queryMode: 'local',
                                                    store: 'Months',
                                                    valueField: 'number',
                                                    listeners: {
                                                        select: {
                                                            fn: me.onLocksMonthCmbSelect,
                                                            scope: me
                                                        }
                                                    }
                                                },
                                                {
                                                    xtype: 'combobox',
                                                    id: 'LocksYearCmb',
                                                    itemId: 'LocksYearCmb',
                                                    width: 70,
                                                    fieldLabel: 'Label',
                                                    hideLabel: true,
                                                    editable: false,
                                                    displayField: 'year',
                                                    queryMode: 'local',
                                                    store: 'Years',
                                                    valueField: 'year',
                                                    listeners: {
                                                        select: {
                                                            fn: me.onLocksYearCmbSelect,
                                                            scope: me
                                                        }
                                                    }
                                                },
                                                {
                                                    xtype: 'container',
                                                    flex: 1,
                                                    layout: {
                                                        type: 'hbox',
                                                        align: 'stretch'
                                                    },
                                                    items: [
                                                        {
                                                            xtype: 'button',
                                                            handler: function(button, e) {
                                                                var month = Ext.getCmp('LocksMonthCmb').getValue();
                                                                var year = Ext.getCmp('LocksYearCmb').getValue();


                                                                if (month === null || year === null) {
                                                                    return;
                                                                }

                                                                month = month + 1;

                                                                if (month > 12) {
                                                                    month = 1;
                                                                    year = year + 1;
                                                                }

                                                                Ext.getCmp('LocksMonthCmb').setValue(month);
                                                                Ext.getCmp('LocksYearCmb').setValue(year);

                                                                Ext.getCmp('EmployeeLockMonthMenuTab').loadData();
                                                            },
                                                            iconCls: 'icon-bullet_right',
                                                            text: '',
                                                            tooltip: 'Mese successivo'
                                                        }
                                                    ]
                                                }
                                            ]
                                        }
                                    ]
                                },
                                {
                                    xtype: 'toolbar',
                                    flex: 1,
                                    dock: 'top',
                                    items: [
                                        {
                                            xtype: 'hiddenfield',
                                            id: 'StartMonthExtO',
                                            itemId: 'StartMonthExtO',
                                            fieldLabel: 'Totale inizio mese',
                                            labelWidth: 120
                                        },
                                        {
                                            xtype: 'numberfield',
                                            id: 'StartMonthExt',
                                            itemId: 'StartMonthExt',
                                            margin: 2,
                                            width: 180,
                                            fieldLabel: 'Totale inizio mese (min)',
                                            labelAlign: 'right',
                                            labelWidth: 130,
                                            allowBlank: false,
                                            enforceMaxLength: true,
                                            maxLength: 5,
                                            hideTrigger: true,
                                            maxValue: 99999,
                                            listeners: {
                                                change: {
                                                    fn: me.onStartMonthExtChange,
                                                    scope: me
                                                },
                                                blur: {
                                                    fn: me.onStartMonthExtBlur,
                                                    scope: me
                                                }
                                            }
                                        },
                                        {
                                            xtype: 'numberfield',
                                            id: 'EndMonthExt',
                                            itemId: 'EndMonthExt',
                                            margin: 2,
                                            width: 180,
                                            fieldLabel: 'Totale fine mese (min)',
                                            labelAlign: 'right',
                                            labelWidth: 130,
                                            allowBlank: false,
                                            enforceMaxLength: true,
                                            maxLength: 5,
                                            hideTrigger: true,
                                            maxValue: 99999,
                                            listeners: {
                                                blur: {
                                                    fn: me.onEndMonthExtBlur,
                                                    scope: me
                                                }
                                            }
                                        },
                                        {
                                            xtype: 'hiddenfield',
                                            id: 'EndMonthExtO',
                                            itemId: 'EndMonthExtO',
                                            fieldLabel: 'Totale fine mese',
                                            labelWidth: 120
                                        },
                                        {
                                            xtype: 'textfield',
                                            flex: 1,
                                            id: 'MonthNote',
                                            itemId: 'MonthNote',
                                            margin: 2,
                                            fieldLabel: 'Note',
                                            labelAlign: 'right',
                                            labelWidth: 40
                                        }
                                    ]
                                }
                            ],
                            items: [
                                {
                                    xtype: 'container',
                                    flex: 1,
                                    id: 'LockMonthCenterCnt',
                                    itemId: 'LockMonthCenterCnt',
                                    style: 'background-color: #dfe9f6',
                                    autoScroll: true,
                                    layout: {
                                        type: 'hbox',
                                        align: 'stretch',
                                        pack: 'center'
                                    },
                                    items: [
                                        {
                                            xtype: 'gridpanel',
                                            flex: 1,
                                            id: 'LockMonthGrid',
                                            itemId: 'LockMonthGrid',
                                            minWidth: 400,
                                            padding: 5,
                                            title: 'Straordinari / Recuperi',
                                            enableColumnMove: false,
                                            sortableColumns: false,
                                            store: 'ExtraordinaryStored',
                                            viewConfig: {
                                                id: 'LockMonthGridView',
                                                itemId: 'LockMonthGridView',
                                                style: {
                                                    overflow: 'auto',
                                                    overflowX: 'hidden'
                                                },
                                                loadMask: false,
                                                listeners: {
                                                    itemupdate: {
                                                        fn: me.onLockMonthGridViewItemUpdate,
                                                        scope: me
                                                    }
                                                }
                                            },
                                            columns: [
                                                {
                                                    xtype: 'actioncolumn',
                                                    draggable: false,
                                                    width: 38,
                                                    resizable: false,
                                                    hideable: false,
                                                    items: [
                                                        {
                                                            getClass: function(v, metadata, r, rowIndex, colIndex, store) {
                                                                if (r.raw.warning !== undefined){
                                                                    return 'icon-error';
                                                                }
                                                            },
                                                            getTip: function(v, metadata, record, rowIndex, colIndex, store) {
                                                                if (record.raw.warning !== undefined){
                                                                    var warning = record.raw.warning;
                                                                    if (warning.stk || warning.oddbip || warning.nobip){
                                                                        var w = '<ol>';
                                                                        if (warning.stk){
                                                                            w += '<li>' + warning.stk + '</li>';
                                                                        }
                                                                        if (warning.oddbip){
                                                                            w += '<li>' + warning.oddbip + '</li>';
                                                                        }
                                                                        if (warning.nobip){
                                                                            w += '<li>' + warning.nobip + '</li>';
                                                                        }
                                                                        w += '</ol>';
                                                                        return w;
                                                                    }
                                                                }
                                                            }
                                                        },
                                                        {
                                                            getClass: function(v, metadata, r, rowIndex, colIndex, store) {
                                                                if (r.raw.info !== undefined){
                                                                    return 'icon-information';
                                                                }
                                                            },
                                                            getTip: function(v, meta, record, rowIndex, colIndex, store) {
                                                                if (record.raw.info !== undefined){
                                                                    var info = record.raw.info;
                                                                    if (info.abs || info.hol || info.wke){
                                                                        var i = '<ol>';
                                                                        if (info.abs){
                                                                            i += '<li>' + info.abs + '</li>';
                                                                        }
                                                                        if (info.hol){
                                                                            i += '<li>' + info.hol + '</li>';
                                                                        }
                                                                        if (info.wke){
                                                                            i += '<li>' + info.wke + '</li>';
                                                                        }
                                                                        i += '</ol>';
                                                                        return i;
                                                                    }
                                                                }
                                                            }
                                                        }
                                                    ]
                                                },
                                                {
                                                    xtype: 'gridcolumn',
                                                    renderer: function(value, metaData, record, rowIndex, colIndex, store, view) {
                                                        var color = '';
                                                        if (record.raw['info'] && (record.raw['info']['wke'] || record.raw['info']['hol'])) {
                                                            color = 'color:blue;';
                                                        }
                                                        return '<div style="float:left;' + color + '">'+ Ext.Date.format(value, 'D') + '</div>'
                                                        + '<div style="float:right;' + color + '">'+ Ext.Date.format(value, 'j') + '</div>';
                                                    },
                                                    draggable: false,
                                                    width: 53,
                                                    resizable: false,
                                                    sortable: false,
                                                    dataIndex: 'date',
                                                    hideable: false,
                                                    text: 'Giorni'
                                                },
                                                {
                                                    xtype: 'gridcolumn',
                                                    renderer: function(value, metaData, record, rowIndex, colIndex, store, view) {
                                                        var formatted = Ext.getCmp('EmployeePnl').formatHHMM(value),
                                                            formattedO = Ext.getCmp('EmployeePnl').formatHHMM(record.get('totalO'));

                                                        metaData.tdAttr = 'data-qtip="Totale: ' + formatted + ' | Originale: ' + formattedO + '"';

                                                        if (value < 0){
                                                            return '<font style="color:red">'+ value +'</font>';
                                                        } else if (value > 0){
                                                            return '<font style="color:green">'+ value +'</font>';
                                                        } else if (!value){
                                                            return 0;
                                                        } else {
                                                            return value;
                                                        }
                                                    },
                                                    draggable: false,
                                                    width: 90,
                                                    resizable: false,
                                                    defaultWidth: 70,
                                                    sortable: false,
                                                    align: 'right',
                                                    dataIndex: 'total',
                                                    hideable: false,
                                                    text: 'Totale (min)',
                                                    editor: {
                                                        xtype: 'numberfield',
                                                        allowBlank: false,
                                                        allowOnlyWhitespace: false,
                                                        enforceMaxLength: true,
                                                        maxLength: 4,
                                                        minLength: 1,
                                                        selectOnFocus: true,
                                                        hideTrigger: true,
                                                        allowDecimals: false,
                                                        decimalPrecision: 0,
                                                        maxValue: 3000,
                                                        minValue: -3000
                                                    }
                                                },
                                                {
                                                    xtype: 'gridcolumn',
                                                    draggable: false,
                                                    sortable: false,
                                                    dataIndex: 'note',
                                                    hideable: false,
                                                    text: 'Note',
                                                    flex: 1,
                                                    editor: {
                                                        xtype: 'textfield'
                                                    }
                                                }
                                            ],
                                            plugins: [
                                                Ext.create('Ext.grid.plugin.CellEditing', {
                                                    clicksToEdit: 1
                                                })
                                            ]
                                        },
                                        {
                                            xtype: 'gridpanel',
                                            flex: 1,
                                            id: 'LockStacksGrid',
                                            itemId: 'LockStacksGrid',
                                            minWidth: 330,
                                            padding: 5,
                                            title: 'Monteore',
                                            enableColumnMove: false,
                                            sortableColumns: false,
                                            store: 'ExtraordinaryAbsStack',
                                            viewConfig: {
                                                style: {
                                                    overflow: 'auto',
                                                    overflowX: 'hidden'
                                                }
                                            },
                                            columns: [
                                                {
                                                    xtype: 'actioncolumn',
                                                    draggable: false,
                                                    width: 20,
                                                    resizable: false,
                                                    enableColumnHide: false,
                                                    hideable: false,
                                                    items: [
                                                        {
                                                            getClass: function(v, metadata, r, rowIndex, colIndex, store) {
                                                                var type = r.raw.reset_type_applied;

                                                                if (type === 1 || type === 2) {
                                                                    return 'icon-control_repeat';
                                                                } else if (type === 3 || type === 4) {
                                                                    return 'icon-control_add';
                                                                } else if (type === 5 || type === 6) {
                                                                    return 'icon-control_remove';
                                                                } else {
                                                                    return '';
                                                                }
                                                            },
                                                            getTip: function(v, metadata, r, rowIndex, colIndex, store) {
                                                                var type = r.raw.reset_type_applied,
                                                                    tooltip = '<li>Questo monteore si è reimpostato ';

                                                                if (type === 1 || type === 2) {
                                                                    tooltip += 'facendo tornare il suo valore alla ';
                                                                } else if (type === 3 || type === 4) {
                                                                    tooltip += 'aggiungendo al suo valore la ';
                                                                } else if (type === 5 || type === 6) {
                                                                    tooltip += 'sottraendo al suo valore la ';
                                                                } else {
                                                                    return '';
                                                                }

                                                                tooltip += ' quota stabilita.</li>';

                                                                /*if (r.raw.reset_to_stack_id !== null) {
                                                                tooltip += '<li>Il totale è stato inoltre passato sul monteore "' + r.get('reset_to_stack_denomination') + '".</li>';
                                                                }*/

                                                                return '<ul>' + tooltip + '</ul>';
                                                            }
                                                        }
                                                    ]
                                                },
                                                {
                                                    xtype: 'gridcolumn',
                                                    draggable: false,
                                                    width: 128,
                                                    sortable: false,
                                                    dataIndex: 'denomination',
                                                    hideable: false,
                                                    text: 'Denominazione',
                                                    flex: 1
                                                },
                                                {
                                                    xtype: 'gridcolumn',
                                                    renderer: function(value, metaData, record, rowIndex, colIndex, store, view) {
                                                        if (record.raw.unit != 'd') {
                                                            formatted = Ext.getCmp('EmployeePnl').formatHHMM(value);
                                                            formattedO = Ext.getCmp('EmployeePnl').formatHHMM(record.get('totalStartO'));
                                                            metaData.tdAttr = 'data-qtip="Totale: ' + formatted + ' | Originale: ' + formattedO + '"';
                                                        } else {
                                                            metaData.tdAttr = 'data-qtip="Originale: ' + record.get('totalStartO') + '"';
                                                        }

                                                        if (value < 0){
                                                            return '<font style="color:red">'+ value +'</font>';
                                                        } else if (value > 0){
                                                            return '<font style="color:green">'+ value +'</font>';
                                                        } else if (!value){
                                                            return 0;
                                                        } else {
                                                            return value;
                                                        }
                                                    },
                                                    draggable: false,
                                                    width: 80,
                                                    resizable: false,
                                                    sortable: false,
                                                    align: 'right',
                                                    dataIndex: 'totalStart',
                                                    hideable: false,
                                                    text: 'Inizio mese',
                                                    editor: {
                                                        xtype: 'numberfield',
                                                        allowBlank: false,
                                                        allowOnlyWhitespace: false,
                                                        minLength: 1,
                                                        selectOnFocus: true,
                                                        hideTrigger: true,
                                                        decimalPrecision: 1,
                                                        listeners: {
                                                            change: {
                                                                fn: me.onNumberfieldChange,
                                                                scope: me
                                                            }
                                                        }
                                                    }
                                                },
                                                {
                                                    xtype: 'gridcolumn',
                                                    renderer: function(value, metaData, record, rowIndex, colIndex, store, view) {
                                                        if (record.raw.unit != 'd') {
                                                            formatted = Ext.getCmp('EmployeePnl').formatHHMM(value);
                                                            formattedO = Ext.getCmp('EmployeePnl').formatHHMM(record.get('totalEndO'));
                                                            metaData.tdAttr = 'data-qtip="Totale: ' + formatted + ' | Originale: ' + formattedO + '"';
                                                        } else {
                                                            metaData.tdAttr = 'data-qtip="Originale: ' + record.get('totalEndO') + '"';
                                                        }

                                                        if (value < 0){
                                                            return '<font style="color:red">'+ value +'</font>';
                                                        } else if (value > 0){
                                                            return '<font style="color:green">'+ value +'</font>';
                                                        } else if (!value){
                                                            return 0;
                                                        } else {
                                                            return value;
                                                        }
                                                    },
                                                    draggable: false,
                                                    width: 80,
                                                    resizable: false,
                                                    sortable: false,
                                                    align: 'right',
                                                    dataIndex: 'totalEnd',
                                                    hideable: false,
                                                    text: 'Fine mese',
                                                    editor: {
                                                        xtype: 'numberfield',
                                                        allowBlank: false,
                                                        allowOnlyWhitespace: false,
                                                        minLength: 1,
                                                        selectOnFocus: true,
                                                        hideTrigger: true,
                                                        decimalPrecision: 1
                                                    }
                                                },
                                                {
                                                    xtype: 'gridcolumn',
                                                    renderer: function(value, metaData, record, rowIndex, colIndex, store, view) {
                                                        if(value=='d'){
                                                            return 'giorni';
                                                        }
                                                        if(value=='h'){
                                                            return 'minuti';
                                                        }
                                                    },
                                                    draggable: false,
                                                    width: 50,
                                                    resizable: false,
                                                    sortable: false,
                                                    dataIndex: 'unit',
                                                    hideable: false,
                                                    text: 'Unità'
                                                },
                                                {
                                                    xtype: 'gridcolumn',
                                                    draggable: false,
                                                    hidden: true,
                                                    resizable: false,
                                                    sortable: false,
                                                    dataIndex: 'recover',
                                                    hideable: false,
                                                    text: 'Recupero'
                                                }
                                            ],
                                            plugins: [
                                                Ext.create('Ext.grid.plugin.CellEditing', {
                                                    clicksToEdit: 1
                                                })
                                            ]
                                        }
                                    ]
                                }
                            ],
                            listeners: {
                                activate: {
                                    fn: me.onEmployeeLockMonthMenuTabActivate,
                                    scope: me
                                }
                            }
                        },
                        {
                            xtype: 'panel',
                            saveParameters: function(employees) {
                                var form = Ext.getCmp('EmpParametersFrm').getForm();

                                if (employees === null) {
                                    employees = Ext.JSON.encode(Ext.getCmp('EmployeePnl').getSelectedEmployee().employee_id);
                                }

                                Ext.getCmp('EmpParametersFrm').setLoading();

                                form.submit({
                                    params: {
                                        employee_ids: employees
                                    },
                                    success: function(a, response){
                                        var es = Ext.getStore('Employees'),
                                            rec = es.getById(Ext.getCmp('EmployeePnl').getSelectedEmployee().employee_id),
                                            name = Ext.getCmp('EmployeeSearchTxt').getValue();

                                        //    field = Ext.getCmp('EmployeeSearchRadioGroup').getChecked()[0].inputId;

                                        es.load({
                                            params:{
                                                q_name: name
                                                //q_liquid_group: field
                                            },
                                            callback: function(){
                                                Ext.getCmp('EmployeeListPnl').getSelectionModel().select(rec.index);
                                                Ext.getCmp('EmpParametersFrm').setLoading(false);
                                                mc2ui.app.showNotifySave();
                                            }
                                        });
                                    }
                                });
                            },
                            saveStacksAmounts: function(employees) {
                                if (employees === null) {
                                    employees = Ext.JSON.encode(Ext.getCmp('EmployeePnl').getSelectedEmployee().employee_id);
                                }

                                var stack = Ext.getCmp('EmpSettingsStacksGrid').getSelectionModel().getSelection()[0];

                                Ext.getCmp('EmpParametersFrm').setLoading();

                                console.log('start 3');

                                Ext.Ajax.request({
                                    url: '/mc2/applications/employees/employee/write_stacks_amounts.php',
                                    params : {
                                        id: stack.get('id'),
                                        employee_ids: employees
                                    },
                                    success: function(response) {
                                        var es = Ext.getStore('Employees'),
                                            rec = es.getById(Ext.getCmp('EmployeePnl').getSelectedEmployee().employee_id),
                                            name = Ext.getCmp('EmployeeSearchTxt').getValue(),
                                            field = Ext.getCmp('EmployeeSearchRadioGroup').getChecked()[0].inputId;

                                        es.load({
                                            params:{
                                                q_name: name,
                                                q_liquid_group: field
                                            },
                                            callback: function(){
                                                Ext.getCmp('EmployeeListPnl').getSelectionModel().select(rec.index);
                                                Ext.getCmp('EmpParametersFrm').setLoading(false);
                                                mc2ui.app.showNotifySave();
                                            }
                                        });
                                    }
                                });
                            },
                            permissible: true,
                            id: 'EmployeeSettingsMenuTab',
                            itemId: 'EmployeeSettingsMenuTab',
                            title: 'Parametri',
                            layout: {
                                type: 'vbox',
                                align: 'stretch'
                            },
                            tabConfig: {
                                xtype: 'tab',
                                id: 'EmployeeSettingsTab',
                                itemId: 'EmployeeSettingsTab',
                                iconCls: 'icon-shape_align_center',
                                textAlign: 'left'
                            },
                            items: [
                                {
                                    xtype: 'form',
                                    flex: 1,
                                    border: false,
                                    id: 'EmpParametersFrm',
                                    itemId: 'EmpParametersFrm',
                                    autoScroll: true,
                                    header: false,
                                    title: 'My Form',
                                    url: '/mc2/applications/employees/employee/write_parameters.php',
                                    layout: {
                                        type: 'vbox',
                                        align: 'stretch'
                                    },
                                    dockedItems: [
                                        {
                                            xtype: 'toolbar',
                                            flex: 1,
                                            dock: 'top',
                                            items: [
                                                {
                                                    xtype: 'button',
                                                    handler: function(button, e) {
                                                        Ext.getCmp('EmployeeSettingsMenuTab').saveParameters(null);
                                                    },
                                                    formBind: true,
                                                    id: 'EmployeeParametersSaveBtn',
                                                    itemId: 'EmployeeParametersSaveBtn',
                                                    iconCls: 'icon-disk',
                                                    text: 'Salva'
                                                },
                                                {
                                                    xtype: 'button',
                                                    handler: function(button, e) {
                                                        Ext.widget('EmployeeParametersCopyWin').show();
                                                    },
                                                    formBind: true,
                                                    id: 'EmployeeParametersCopyBtn',
                                                    itemId: 'EmployeeParametersCopyBtn',
                                                    iconCls: 'icon-page_copy',
                                                    text: 'Copia su altro personale'
                                                },
                                                {
                                                    xtype: 'button',
                                                    handler: function(button, e) {
                                                        Ext.getCmp('Param_MaxContWork').setValue(720);
                                                        Ext.getCmp('Param_BreakAfterMaxWork').setValue(0);
                                                        Ext.getCmp('Param_RecoverHours').setValue(100);
                                                        Ext.getCmp('Param_RecoverHoursUnit').setValue('%');
                                                        Ext.getCmp('Param_ExtStepIn').setValue(0);
                                                        Ext.getCmp('Param_ExtStepOut').setValue(0);
                                                        Ext.getCmp('Param_ExtStepDay').setValue(0);
                                                        Ext.getCmp('Param_ExtMaxIn').setValue(999);
                                                        Ext.getCmp('Param_ExtMaxOut').setValue(999);
                                                        Ext.getCmp('Param_ExtMaxDay').setValue(999);
                                                        Ext.getCmp('Param_ExtMinIn').setValue(0);
                                                        Ext.getCmp('Param_ExtMinOut').setValue(0);
                                                        Ext.getCmp('Param_ExtMinDay').setValue(0);
                                                        Ext.getCmp('Param_RecStepIn').setValue(0);
                                                        Ext.getCmp('Param_RecStepOut').setValue(0);
                                                        Ext.getCmp('Param_RecStepDay').setValue(0);
                                                        Ext.getCmp('Param_RecMaxIn').setValue(999);
                                                        Ext.getCmp('Param_RecMaxOut').setValue(999);
                                                        Ext.getCmp('Param_RecMaxDay').setValue(999);
                                                        Ext.getCmp('Param_RecMinIn').setValue(0);
                                                        Ext.getCmp('Param_RecMinOut').setValue(0);
                                                        Ext.getCmp('Param_RecMinDay').setValue(0);

                                                        Ext.getCmp('EmployeeSettingsMenuTab').saveParameters(false);
                                                    },
                                                    id: 'EmployeeParametersResetBtn',
                                                    itemId: 'EmployeeParametersResetBtn',
                                                    iconCls: 'icon-arrow_undo',
                                                    text: 'Reimposta valori iniziali'
                                                }
                                            ]
                                        }
                                    ],
                                    items: [
                                        {
                                            xtype: 'container',
                                            flex: 1,
                                            height: 150,
                                            padding: '10 20 5 20',
                                            style: 'background-color: #dfe9f6',
                                            layout: {
                                                type: 'hbox',
                                                align: 'stretch'
                                            },
                                            items: [
                                                {
                                                    xtype: 'form',
                                                    flex: 1,
                                                    id: 'EmpSettingsGeneralFrm',
                                                    itemId: 'EmpSettingsGeneralFrm',
                                                    padding: '0 2 0 0',
                                                    bodyPadding: 10,
                                                    title: 'Generali',
                                                    titleAlign: 'center',
                                                    layout: {
                                                        type: 'vbox',
                                                        pack: 'center'
                                                    },
                                                    items: [
                                                        {
                                                            xtype: 'container',
                                                            padding: '5 0',
                                                            layout: {
                                                                type: 'hbox',
                                                                align: 'stretch'
                                                            },
                                                            items: [
                                                                {
                                                                    xtype: 'numberfield',
                                                                    id: 'Param_MaxContWork',
                                                                    itemId: 'Param_MaxContWork',
                                                                    width: 300,
                                                                    fieldLabel: 'Massimo tempo continuativo di lavoro',
                                                                    labelAlign: 'right',
                                                                    labelWidth: 250,
                                                                    inputId: 'max_cont_work',
                                                                    allowBlank: false,
                                                                    allowOnlyWhitespace: false,
                                                                    hideTrigger: true,
                                                                    allowDecimals: false,
                                                                    allowExponential: false,
                                                                    autoStripChars: true,
                                                                    maxValue: 999,
                                                                    minValue: 0
                                                                },
                                                                {
                                                                    xtype: 'label',
                                                                    padding: '0 5 ',
                                                                    style: '{\n    font-style: italic;\n    color: #AAA;\n}',
                                                                    text: ' min'
                                                                }
                                                            ]
                                                        },
                                                        {
                                                            xtype: 'container',
                                                            padding: '5 0',
                                                            layout: {
                                                                type: 'hbox',
                                                                align: 'stretch'
                                                            },
                                                            items: [
                                                                {
                                                                    xtype: 'numberfield',
                                                                    id: 'Param_BreakAfterMaxWork',
                                                                    itemId: 'Param_BreakAfterMaxWork',
                                                                    width: 300,
                                                                    fieldLabel: 'Durata pausa oltre tempo massimo',
                                                                    labelAlign: 'right',
                                                                    labelWidth: 250,
                                                                    inputId: 'break_after_max_work',
                                                                    allowBlank: false,
                                                                    allowOnlyWhitespace: false,
                                                                    hideTrigger: true,
                                                                    allowDecimals: false,
                                                                    allowExponential: false,
                                                                    autoStripChars: true,
                                                                    maxValue: 999,
                                                                    minValue: 0
                                                                },
                                                                {
                                                                    xtype: 'label',
                                                                    padding: '0 5 ',
                                                                    style: '{\n    font-style: italic;\n    color: #AAA;\n}',
                                                                    text: ' min'
                                                                }
                                                            ]
                                                        },
                                                        {
                                                            xtype: 'container',
                                                            padding: '5 0',
                                                            layout: {
                                                                type: 'hbox',
                                                                align: 'stretch'
                                                            },
                                                            items: [
                                                                {
                                                                    xtype: 'numberfield',
                                                                    id: 'Param_RecoverHours',
                                                                    itemId: 'Param_RecoverHours',
                                                                    width: 300,
                                                                    fieldLabel: 'Proporzione ore da recuperare',
                                                                    labelAlign: 'right',
                                                                    labelWidth: 250,
                                                                    inputId: 'recover_hours',
                                                                    allowBlank: false,
                                                                    allowOnlyWhitespace: false,
                                                                    hideTrigger: true,
                                                                    allowExponential: false,
                                                                    autoStripChars: true,
                                                                    minValue: 0
                                                                },
                                                                {
                                                                    xtype: 'combobox',
                                                                    flex: 1,
                                                                    id: 'Param_RecoverHoursUnit',
                                                                    itemId: 'Param_RecoverHoursUnit',
                                                                    padding: '0 5 ',
                                                                    width: 50,
                                                                    inputId: 'unit_recover_hours',
                                                                    editable: false,
                                                                    forceSelection: true,
                                                                    queryMode: 'local',
                                                                    store: 'EmpUnitRecoverHours',
                                                                    valueField: 'id'
                                                                }
                                                            ]
                                                        },
                                                        {
                                                            xtype: 'container',
                                                            padding: '5 0',
                                                            layout: {
                                                                type: 'hbox',
                                                                align: 'stretch'
                                                            },
                                                            items: [
                                                                {
                                                                    xtype: 'numberfield',
                                                                    id: 'Param_MaxWorkTime',
                                                                    itemId: 'Param_MaxWorkTime',
                                                                    width: 300,
                                                                    fieldLabel: 'Massimo tempo di lavoro',
                                                                    labelAlign: 'right',
                                                                    labelWidth: 250,
                                                                    inputId: 'max_work',
                                                                    allowBlank: false,
                                                                    allowOnlyWhitespace: false,
                                                                    hideTrigger: true,
                                                                    allowDecimals: false,
                                                                    allowExponential: false,
                                                                    autoStripChars: true,
                                                                    maxValue: 999,
                                                                    minValue: 0
                                                                },
                                                                {
                                                                    xtype: 'label',
                                                                    padding: '0 5 ',
                                                                    style: '{\n    font-style: italic;\n    color: #AAA;\n}',
                                                                    text: ' min'
                                                                }
                                                            ]
                                                        }
                                                    ]
                                                },
                                                {
                                                    xtype: 'gridpanel',
                                                    flex: 1,
                                                    id: 'EmpSettingsStacksGrid',
                                                    itemId: 'EmpSettingsStacksGrid',
                                                    padding: '0 0 0 2',
                                                    title: 'Quote monteore',
                                                    titleAlign: 'center',
                                                    emptyText: 'Nessun monteore con reset.',
                                                    enableColumnHide: false,
                                                    enableColumnMove: false,
                                                    enableColumnResize: false,
                                                    store: 'StacksPersonnelLinks',
                                                    columns: [
                                                        {
                                                            xtype: 'gridcolumn',
                                                            dataIndex: 'denomination',
                                                            text: 'Nome',
                                                            flex: 1
                                                        },
                                                        {
                                                            xtype: 'gridcolumn',
                                                            renderer: function(value, metaData, record, rowIndex, colIndex, store, view) {
                                                                if (record.get('unit') === 'd') {
                                                                    return Ext.util.Format.number(value, '0,000.00') + '  gg';
                                                                } else {
                                                                    return Ext.util.Format.number(value, '0,000') + '    min';
                                                                }
                                                            },
                                                            defaultWidth: 80,
                                                            align: 'right',
                                                            dataIndex: 'reset_quota',
                                                            text: 'Quota',
                                                            editor: {
                                                                xtype: 'numberfield',
                                                                allowBlank: false,
                                                                allowOnlyWhitespace: false,
                                                                hideTrigger: true,
                                                                allowExponential: false,
                                                                autoStripChars: true,
                                                                minValue: 0,
                                                                listeners: {
                                                                    change: {
                                                                        fn: me.onNumberfieldChange1,
                                                                        scope: me
                                                                    }
                                                                }
                                                            }
                                                        }
                                                    ],
                                                    plugins: [
                                                        Ext.create('Ext.grid.plugin.RowEditing', {
                                                            blocked: true,
                                                            listeners: {
                                                                edit: {
                                                                    fn: me.onRowEditingEdit,
                                                                    scope: me
                                                                },
                                                                beforeedit: {
                                                                    fn: me.onRowEditingBeforeEdit1,
                                                                    scope: me
                                                                }
                                                            }
                                                        })
                                                    ],
                                                    features: [
                                                        {
                                                            ftype: 'grouping',
                                                            enableGroupingMenu: false,
                                                            enableNoGroups: false,
                                                            groupHeaderTpl: [
                                                                'Unità: {name}'
                                                            ]
                                                        }
                                                    ],
                                                    listeners: {
                                                        itemcontextmenu: {
                                                            fn: me.onEmpSettingsStacksGridItemContextMenu,
                                                            scope: me
                                                        }
                                                    }
                                                },
                                                {
                                                    xtype: 'menu',
                                                    permissible: true,
                                                    hidden: true,
                                                    id: 'EmpSettingsStacksMn',
                                                    itemId: 'EmpSettingsStacksMn',
                                                    items: [
                                                        {
                                                            xtype: 'menuitem',
                                                            handler: function(item, e) {
                                                                var pg = Ext.getCmp('EmpSettingsStacksGrid'),
                                                                    record = pg.getSelectionModel().getSelection()[0];

                                                                pg.getPlugin().blocked = false;
                                                                pg.getPlugin().startEdit(record);
                                                                pg.getPlugin().blocked = true;
                                                            },
                                                            id: 'contextPersonnelStackLinkEdit',
                                                            itemId: 'contextPersonnelStackLinkEdit',
                                                            iconCls: 'icon-pencil',
                                                            text: 'Modifica'
                                                        },
                                                        {
                                                            xtype: 'menuitem',
                                                            handler: function(item, e) {
                                                                var pg = Ext.getCmp('EmpSettingsStacksGrid'),
                                                                    record = pg.getSelectionModel().getSelection()[0];

                                                                Ext.widget('EmployeeStacksCopyWin').show();
                                                            },
                                                            id: 'contextPersonnelStackLinkCopy',
                                                            itemId: 'contextPersonnelStackLinkCopy',
                                                            iconCls: 'icon-page_copy',
                                                            text: 'Copia su altro personale'
                                                        },
                                                        {
                                                            xtype: 'menuitem',
                                                            handler: function(item, e) {
                                                                var pg = Ext.getCmp('EmpSettingsStacksGrid'),
                                                                    record = pg.getSelectionModel().getSelection()[0];

                                                                Ext.Msg.show({
                                                                    title: record.raw.group_name,
                                                                    msg: 'Sei sicuro di voler reimpostare la quota di questo monteore al valore iniziale?',
                                                                    buttons: Ext.Msg.YESNO,
                                                                    fn: function(r){
                                                                        if (r == 'yes') {
                                                                            var store = Ext.getStore('StacksPersonnelLinks'),
                                                                                stacks_store = Ext.getStore('AbsenceStacks');

                                                                            stacks_store.load();

                                                                            var stack = stacks_store.getById(record.data.stack_id);

                                                                            record.set('reset_quota', stack.get('reset_default_quota'));

                                                                            store.sync();
                                                                        }
                                                                    }
                                                                });
                                                            },
                                                            id: 'contextPersonnelStackLinkReset',
                                                            itemId: 'contextPersonnelStackLinkReset',
                                                            iconCls: 'icon-arrow_undo',
                                                            text: 'Reimposta quota iniziale'
                                                        }
                                                    ]
                                                }
                                            ]
                                        },
                                        {
                                            xtype: 'container',
                                            flex: 1,
                                            padding: '5 20 10 20',
                                            style: 'background-color: #dfe9f6',
                                            autoScroll: true,
                                            layout: 'hbox',
                                            items: [
                                                {
                                                    xtype: 'form',
                                                    flex: 1,
                                                    id: 'EmpSettingsStrFrm',
                                                    itemId: 'EmpSettingsStrFrm',
                                                    padding: '0 2 0 0',
                                                    title: 'Straordinari',
                                                    titleAlign: 'center',
                                                    layout: {
                                                        type: 'vbox',
                                                        align: 'stretch'
                                                    },
                                                    items: [
                                                        {
                                                            xtype: 'fieldset',
                                                            margin: 10,
                                                            collapsed: true,
                                                            collapsible: true,
                                                            title: 'Soglie massime',
                                                            layout: {
                                                                type: 'vbox',
                                                                align: 'center'
                                                            },
                                                            items: [
                                                                {
                                                                    xtype: 'container',
                                                                    padding: '5 0',
                                                                    layout: {
                                                                        type: 'hbox',
                                                                        align: 'stretch'
                                                                    },
                                                                    items: [
                                                                        {
                                                                            xtype: 'numberfield',
                                                                            id: 'Param_ExtMaxIn',
                                                                            itemId: 'Param_ExtMaxIn',
                                                                            width: 150,
                                                                            fieldLabel: 'Entrata',
                                                                            labelAlign: 'right',
                                                                            inputId: 'max_extraordinary_in',
                                                                            allowBlank: false,
                                                                            allowOnlyWhitespace: false,
                                                                            hideTrigger: true,
                                                                            allowExponential: false,
                                                                            maxValue: 999,
                                                                            minValue: 0
                                                                        },
                                                                        {
                                                                            xtype: 'label',
                                                                            padding: '0 5',
                                                                            style: '{\n    font-style: italic;\n    color: #AAA;\n}',
                                                                            text: 'min'
                                                                        }
                                                                    ]
                                                                },
                                                                {
                                                                    xtype: 'container',
                                                                    padding: '5 0',
                                                                    layout: {
                                                                        type: 'hbox',
                                                                        align: 'stretch'
                                                                    },
                                                                    items: [
                                                                        {
                                                                            xtype: 'numberfield',
                                                                            id: 'Param_ExtMaxOut',
                                                                            itemId: 'Param_ExtMaxOut',
                                                                            width: 150,
                                                                            fieldLabel: 'Uscita',
                                                                            labelAlign: 'right',
                                                                            inputId: 'max_extraordinary_out',
                                                                            allowBlank: false,
                                                                            allowOnlyWhitespace: false,
                                                                            hideTrigger: true,
                                                                            allowExponential: false,
                                                                            maxValue: 999,
                                                                            minValue: 0
                                                                        },
                                                                        {
                                                                            xtype: 'label',
                                                                            padding: '0 5',
                                                                            style: '{\n    font-style: italic;\n    color: #AAA;\n}',
                                                                            text: 'min'
                                                                        }
                                                                    ]
                                                                },
                                                                {
                                                                    xtype: 'container',
                                                                    padding: '5 0',
                                                                    layout: {
                                                                        type: 'hbox',
                                                                        align: 'stretch'
                                                                    },
                                                                    items: [
                                                                        {
                                                                            xtype: 'numberfield',
                                                                            id: 'Param_ExtMaxDay',
                                                                            itemId: 'Param_ExtMaxDay',
                                                                            width: 150,
                                                                            fieldLabel: 'Giornaliero',
                                                                            labelAlign: 'right',
                                                                            inputId: 'max_extraordinary_total',
                                                                            allowBlank: false,
                                                                            allowOnlyWhitespace: false,
                                                                            hideTrigger: true,
                                                                            allowExponential: false,
                                                                            maxValue: 999,
                                                                            minValue: 0
                                                                        },
                                                                        {
                                                                            xtype: 'label',
                                                                            padding: '0 5',
                                                                            style: '{\n    font-style: italic;\n    color: #AAA;\n}',
                                                                            text: 'min'
                                                                        }
                                                                    ]
                                                                }
                                                            ]
                                                        },
                                                        {
                                                            xtype: 'fieldset',
                                                            margin: 10,
                                                            collapsed: true,
                                                            collapsible: true,
                                                            title: 'Soglie minime',
                                                            layout: {
                                                                type: 'vbox',
                                                                align: 'center'
                                                            },
                                                            items: [
                                                                {
                                                                    xtype: 'container',
                                                                    padding: '5 0',
                                                                    layout: {
                                                                        type: 'hbox',
                                                                        align: 'stretch'
                                                                    },
                                                                    items: [
                                                                        {
                                                                            xtype: 'numberfield',
                                                                            id: 'Param_ExtMinIn',
                                                                            itemId: 'Param_ExtMinIn',
                                                                            width: 150,
                                                                            fieldLabel: 'Entrata',
                                                                            labelAlign: 'right',
                                                                            inputId: 'min_extraordinary_in',
                                                                            allowBlank: false,
                                                                            allowOnlyWhitespace: false,
                                                                            hideTrigger: true,
                                                                            allowExponential: false,
                                                                            maxValue: 999,
                                                                            minValue: 0
                                                                        },
                                                                        {
                                                                            xtype: 'label',
                                                                            padding: '0 5',
                                                                            style: '{\n    font-style: italic;\n    color: #AAA;\n}',
                                                                            text: 'min'
                                                                        }
                                                                    ]
                                                                },
                                                                {
                                                                    xtype: 'container',
                                                                    padding: '5 0',
                                                                    layout: {
                                                                        type: 'hbox',
                                                                        align: 'stretch'
                                                                    },
                                                                    items: [
                                                                        {
                                                                            xtype: 'numberfield',
                                                                            id: 'Param_ExtMinOut',
                                                                            itemId: 'Param_ExtMinOut',
                                                                            width: 150,
                                                                            fieldLabel: 'Uscita',
                                                                            labelAlign: 'right',
                                                                            inputId: 'min_extraordinary_out',
                                                                            allowBlank: false,
                                                                            allowOnlyWhitespace: false,
                                                                            hideTrigger: true,
                                                                            allowExponential: false,
                                                                            maxValue: 999,
                                                                            minValue: 0
                                                                        },
                                                                        {
                                                                            xtype: 'label',
                                                                            padding: '0 5',
                                                                            style: '{\n    font-style: italic;\n    color: #AAA;\n}',
                                                                            text: 'min'
                                                                        }
                                                                    ]
                                                                },
                                                                {
                                                                    xtype: 'container',
                                                                    padding: '5 0',
                                                                    layout: {
                                                                        type: 'hbox',
                                                                        align: 'stretch'
                                                                    },
                                                                    items: [
                                                                        {
                                                                            xtype: 'numberfield',
                                                                            id: 'Param_ExtMinDay',
                                                                            itemId: 'Param_ExtMinDay',
                                                                            width: 150,
                                                                            fieldLabel: 'Giornaliero',
                                                                            labelAlign: 'right',
                                                                            inputId: 'min_extraordinary_total',
                                                                            allowBlank: false,
                                                                            allowOnlyWhitespace: false,
                                                                            hideTrigger: true,
                                                                            allowExponential: false,
                                                                            maxValue: 999,
                                                                            minValue: 0
                                                                        },
                                                                        {
                                                                            xtype: 'label',
                                                                            padding: '0 5',
                                                                            style: '{\n    font-style: italic;\n    color: #AAA;\n}',
                                                                            text: 'min'
                                                                        }
                                                                    ]
                                                                }
                                                            ]
                                                        },
                                                        {
                                                            xtype: 'fieldset',
                                                            flex: 1,
                                                            margin: 10,
                                                            collapsed: true,
                                                            collapsible: true,
                                                            title: 'Scaglioni',
                                                            layout: {
                                                                type: 'vbox',
                                                                align: 'center'
                                                            },
                                                            items: [
                                                                {
                                                                    xtype: 'container',
                                                                    padding: '5 0',
                                                                    layout: {
                                                                        type: 'hbox',
                                                                        align: 'stretch'
                                                                    },
                                                                    items: [
                                                                        {
                                                                            xtype: 'numberfield',
                                                                            id: 'Param_ExtStepIn',
                                                                            itemId: 'Param_ExtStepIn',
                                                                            width: 150,
                                                                            fieldLabel: 'Entrata',
                                                                            labelAlign: 'right',
                                                                            inputId: 'step_in',
                                                                            allowBlank: false,
                                                                            allowOnlyWhitespace: false,
                                                                            hideTrigger: true,
                                                                            allowExponential: false,
                                                                            minValue: 0
                                                                        },
                                                                        {
                                                                            xtype: 'label',
                                                                            padding: '0 5',
                                                                            style: '{\n    font-style: italic;\n    color: #AAA;\n}',
                                                                            text: 'min'
                                                                        }
                                                                    ]
                                                                },
                                                                {
                                                                    xtype: 'container',
                                                                    padding: '5 0',
                                                                    layout: {
                                                                        type: 'hbox',
                                                                        align: 'stretch'
                                                                    },
                                                                    items: [
                                                                        {
                                                                            xtype: 'numberfield',
                                                                            id: 'Param_ExtStepOut',
                                                                            itemId: 'Param_ExtStepOut',
                                                                            width: 150,
                                                                            fieldLabel: 'Uscita',
                                                                            labelAlign: 'right',
                                                                            inputId: 'step_out',
                                                                            allowBlank: false,
                                                                            allowOnlyWhitespace: false,
                                                                            hideTrigger: true,
                                                                            allowExponential: false,
                                                                            minValue: 0
                                                                        },
                                                                        {
                                                                            xtype: 'label',
                                                                            padding: '0 5',
                                                                            style: '{\n    font-style: italic;\n    color: #AAA;\n}',
                                                                            text: 'min'
                                                                        }
                                                                    ]
                                                                },
                                                                {
                                                                    xtype: 'container',
                                                                    padding: '5 0',
                                                                    layout: {
                                                                        type: 'hbox',
                                                                        align: 'stretch'
                                                                    },
                                                                    items: [
                                                                        {
                                                                            xtype: 'numberfield',
                                                                            id: 'Param_ExtStepDay',
                                                                            itemId: 'Param_ExtStepDay',
                                                                            width: 150,
                                                                            fieldLabel: 'Giornaliero',
                                                                            labelAlign: 'right',
                                                                            inputId: 'step_total_extraordinary',
                                                                            allowBlank: false,
                                                                            allowOnlyWhitespace: false,
                                                                            hideTrigger: true,
                                                                            allowExponential: false,
                                                                            minValue: 0
                                                                        },
                                                                        {
                                                                            xtype: 'label',
                                                                            padding: '0 5',
                                                                            style: '{\n    font-style: italic;\n    color: #AAA;\n}',
                                                                            text: 'min'
                                                                        }
                                                                    ]
                                                                }
                                                            ]
                                                        }
                                                    ]
                                                },
                                                {
                                                    xtype: 'form',
                                                    flex: 1,
                                                    id: 'EmpSettingsRecFrm',
                                                    itemId: 'EmpSettingsRecFrm',
                                                    padding: '0 0 0 2',
                                                    title: 'Recuperi',
                                                    titleAlign: 'center',
                                                    layout: {
                                                        type: 'vbox',
                                                        align: 'stretch'
                                                    },
                                                    items: [
                                                        {
                                                            xtype: 'fieldset',
                                                            margin: 10,
                                                            collapsed: true,
                                                            collapsible: true,
                                                            title: 'Soglie massime',
                                                            layout: {
                                                                type: 'vbox',
                                                                align: 'center'
                                                            },
                                                            items: [
                                                                {
                                                                    xtype: 'container',
                                                                    padding: '5 0',
                                                                    layout: {
                                                                        type: 'hbox',
                                                                        align: 'stretch'
                                                                    },
                                                                    items: [
                                                                        {
                                                                            xtype: 'numberfield',
                                                                            id: 'Param_RecMaxIn',
                                                                            itemId: 'Param_RecMaxIn',
                                                                            width: 150,
                                                                            fieldLabel: 'Entrata',
                                                                            labelAlign: 'right',
                                                                            inputId: 'max_undefined_in',
                                                                            allowBlank: false,
                                                                            allowOnlyWhitespace: false,
                                                                            hideTrigger: true,
                                                                            allowExponential: false,
                                                                            maxValue: 999,
                                                                            minValue: 0
                                                                        },
                                                                        {
                                                                            xtype: 'label',
                                                                            padding: '0 5',
                                                                            style: '{\n    font-style: italic;\n    color: #AAA;\n}',
                                                                            text: 'min'
                                                                        }
                                                                    ]
                                                                },
                                                                {
                                                                    xtype: 'container',
                                                                    padding: '5 0',
                                                                    layout: {
                                                                        type: 'hbox',
                                                                        align: 'stretch'
                                                                    },
                                                                    items: [
                                                                        {
                                                                            xtype: 'numberfield',
                                                                            id: 'Param_RecMaxOut',
                                                                            itemId: 'Param_RecMaxOut',
                                                                            width: 150,
                                                                            fieldLabel: 'Uscita',
                                                                            labelAlign: 'right',
                                                                            inputId: 'max_undefined_out',
                                                                            allowBlank: false,
                                                                            allowOnlyWhitespace: false,
                                                                            hideTrigger: true,
                                                                            allowExponential: false,
                                                                            maxValue: 999,
                                                                            minValue: 0
                                                                        },
                                                                        {
                                                                            xtype: 'label',
                                                                            padding: '0 5',
                                                                            style: '{\n    font-style: italic;\n    color: #AAA;\n}',
                                                                            text: 'min'
                                                                        }
                                                                    ]
                                                                },
                                                                {
                                                                    xtype: 'container',
                                                                    padding: '5 0',
                                                                    layout: {
                                                                        type: 'hbox',
                                                                        align: 'stretch'
                                                                    },
                                                                    items: [
                                                                        {
                                                                            xtype: 'numberfield',
                                                                            id: 'Param_RecMaxDay',
                                                                            itemId: 'Param_RecMaxDay',
                                                                            width: 150,
                                                                            fieldLabel: 'Giornaliero',
                                                                            labelAlign: 'right',
                                                                            inputId: 'max_undefined_total',
                                                                            allowBlank: false,
                                                                            allowOnlyWhitespace: false,
                                                                            hideTrigger: true,
                                                                            allowExponential: false,
                                                                            maxValue: 999,
                                                                            minValue: 0
                                                                        },
                                                                        {
                                                                            xtype: 'label',
                                                                            padding: '0 5',
                                                                            style: '{\n    font-style: italic;\n    color: #AAA;\n}',
                                                                            text: 'min'
                                                                        }
                                                                    ]
                                                                }
                                                            ]
                                                        },
                                                        {
                                                            xtype: 'fieldset',
                                                            margin: 10,
                                                            collapsed: true,
                                                            collapsible: true,
                                                            title: 'Soglie minime',
                                                            layout: {
                                                                type: 'vbox',
                                                                align: 'center'
                                                            },
                                                            items: [
                                                                {
                                                                    xtype: 'container',
                                                                    padding: '5 0',
                                                                    layout: {
                                                                        type: 'hbox',
                                                                        align: 'stretch'
                                                                    },
                                                                    items: [
                                                                        {
                                                                            xtype: 'numberfield',
                                                                            id: 'Param_RecMinIn',
                                                                            itemId: 'Param_RecMinIn',
                                                                            width: 150,
                                                                            fieldLabel: 'Entrata',
                                                                            labelAlign: 'right',
                                                                            inputId: 'min_undefined_in',
                                                                            allowBlank: false,
                                                                            allowOnlyWhitespace: false,
                                                                            hideTrigger: true,
                                                                            allowExponential: false,
                                                                            maxValue: 999,
                                                                            minValue: 0
                                                                        },
                                                                        {
                                                                            xtype: 'label',
                                                                            padding: '0 5',
                                                                            style: '{\n    font-style: italic;\n    color: #AAA;\n}',
                                                                            text: 'min'
                                                                        }
                                                                    ]
                                                                },
                                                                {
                                                                    xtype: 'container',
                                                                    padding: '5 0',
                                                                    layout: {
                                                                        type: 'hbox',
                                                                        align: 'stretch'
                                                                    },
                                                                    items: [
                                                                        {
                                                                            xtype: 'numberfield',
                                                                            id: 'Param_RecMinOut',
                                                                            itemId: 'Param_RecMinOut',
                                                                            width: 150,
                                                                            fieldLabel: 'Uscita',
                                                                            labelAlign: 'right',
                                                                            inputId: 'min_undefined_out',
                                                                            allowBlank: false,
                                                                            allowOnlyWhitespace: false,
                                                                            hideTrigger: true,
                                                                            allowExponential: false,
                                                                            maxValue: 999,
                                                                            minValue: 0
                                                                        },
                                                                        {
                                                                            xtype: 'label',
                                                                            padding: '0 5',
                                                                            style: '{\n    font-style: italic;\n    color: #AAA;\n}',
                                                                            text: 'min'
                                                                        }
                                                                    ]
                                                                },
                                                                {
                                                                    xtype: 'container',
                                                                    padding: '5 0',
                                                                    layout: {
                                                                        type: 'hbox',
                                                                        align: 'stretch'
                                                                    },
                                                                    items: [
                                                                        {
                                                                            xtype: 'numberfield',
                                                                            id: 'Param_RecMinDay',
                                                                            itemId: 'Param_RecMinDay',
                                                                            width: 150,
                                                                            fieldLabel: 'Giornaliero',
                                                                            labelAlign: 'right',
                                                                            inputId: 'min_undefined_total',
                                                                            allowBlank: false,
                                                                            allowOnlyWhitespace: false,
                                                                            hideTrigger: true,
                                                                            allowExponential: false,
                                                                            maxValue: 999,
                                                                            minValue: 0
                                                                        },
                                                                        {
                                                                            xtype: 'label',
                                                                            padding: '0 5',
                                                                            style: '{\n    font-style: italic;\n    color: #AAA;\n}',
                                                                            text: 'min'
                                                                        }
                                                                    ]
                                                                }
                                                            ]
                                                        },
                                                        {
                                                            xtype: 'fieldset',
                                                            margin: 10,
                                                            collapsed: true,
                                                            collapsible: true,
                                                            title: 'Scaglioni',
                                                            layout: {
                                                                type: 'vbox',
                                                                align: 'center'
                                                            },
                                                            items: [
                                                                {
                                                                    xtype: 'container',
                                                                    padding: '5 0',
                                                                    layout: {
                                                                        type: 'hbox',
                                                                        align: 'stretch'
                                                                    },
                                                                    items: [
                                                                        {
                                                                            xtype: 'numberfield',
                                                                            id: 'Param_RecStepIn',
                                                                            itemId: 'Param_RecStepIn',
                                                                            width: 150,
                                                                            fieldLabel: 'Entrata',
                                                                            labelAlign: 'right',
                                                                            inputId: 'step_in_und',
                                                                            allowBlank: false,
                                                                            allowOnlyWhitespace: false,
                                                                            hideTrigger: true,
                                                                            allowExponential: false,
                                                                            minValue: 0
                                                                        },
                                                                        {
                                                                            xtype: 'label',
                                                                            padding: '0 5',
                                                                            style: '{\n    font-style: italic;\n    color: #AAA;\n}',
                                                                            text: 'min'
                                                                        }
                                                                    ]
                                                                },
                                                                {
                                                                    xtype: 'container',
                                                                    padding: '5 0',
                                                                    layout: {
                                                                        type: 'hbox',
                                                                        align: 'stretch'
                                                                    },
                                                                    items: [
                                                                        {
                                                                            xtype: 'numberfield',
                                                                            id: 'Param_RecStepOut',
                                                                            itemId: 'Param_RecStepOut',
                                                                            width: 150,
                                                                            fieldLabel: 'Uscita',
                                                                            labelAlign: 'right',
                                                                            inputId: 'step_out_und',
                                                                            allowBlank: false,
                                                                            allowOnlyWhitespace: false,
                                                                            hideTrigger: true,
                                                                            allowExponential: false,
                                                                            minValue: 0
                                                                        },
                                                                        {
                                                                            xtype: 'label',
                                                                            padding: '0 5',
                                                                            style: '{\n    font-style: italic;\n    color: #AAA;\n}',
                                                                            text: 'min'
                                                                        }
                                                                    ]
                                                                },
                                                                {
                                                                    xtype: 'container',
                                                                    padding: '5 0',
                                                                    layout: {
                                                                        type: 'hbox',
                                                                        align: 'stretch'
                                                                    },
                                                                    items: [
                                                                        {
                                                                            xtype: 'numberfield',
                                                                            id: 'Param_RecStepDay',
                                                                            itemId: 'Param_RecStepDay',
                                                                            width: 150,
                                                                            fieldLabel: 'Giornaliero',
                                                                            labelAlign: 'right',
                                                                            inputId: 'step_total_undefined',
                                                                            allowBlank: false,
                                                                            allowOnlyWhitespace: false,
                                                                            hideTrigger: true,
                                                                            allowExponential: false,
                                                                            minValue: 0
                                                                        },
                                                                        {
                                                                            xtype: 'label',
                                                                            padding: '0 5',
                                                                            style: '{\n    font-style: italic;\n    color: #AAA;\n}',
                                                                            text: 'min'
                                                                        }
                                                                    ]
                                                                }
                                                            ]
                                                        }
                                                    ]
                                                }
                                            ]
                                        }
                                    ]
                                }
                            ],
                            listeners: {
                                activate: {
                                    fn: me.onPanelActivate2,
                                    scope: me
                                }
                            }
                        }
                    ]
                }
            ],
            listeners: {
                boxready: {
                    fn: me.onEmployeePnlBoxReady,
                    scope: me
                }
            },
            dockedItems: [
                {
                    xtype: 'toolbar',
                    permissible: true,
                    dock: 'top',
                    id: 'EmployeeToolbar',
                    itemId: 'EmployeeToolbar',
                    items: [
                        {
                            xtype: 'button',
                            handler: function(button, e) {
                                Ext.widget('EmployeeHolidayCalendarWin').show();
                            },
                            id: 'FestivityCalendarBtn',
                            itemId: 'FestivityCalendarBtn',
                            iconCls: 'icon-calendar',
                            text: 'Calendario Festività'
                        },
                        {
                            xtype: 'tbseparator'
                        },
                        {
                            xtype: 'button',
                            handler: function(button, e) {
                                Ext.widget('EmployeeAbsenceStackWin').show();
                                Ext.getStore('AbsenceStacks').load();
                                Ext.getStore('AbsenceKinds').load();
                            },
                            id: 'StacksManagementBtn',
                            itemId: 'StacksManagementBtn',
                            iconCls: 'icon-sitemap',
                            text: 'Monteore e Tipi Assenza'
                        },
                        {
                            xtype: 'button',
                            handler: function(button, e) {
                                Ext.widget('EmployeeProjectsHourTypesWin').show();
                                Ext.getStore('PersonnelProjects').load();
                                Ext.getStore('PersonnelHourTypes').load();
                            },
                            id: 'ProjectsManagementBtn',
                            itemId: 'ProjectsManagementBtn',
                            iconCls: 'icon-application_osx_cascade',
                            text: 'Progetti e Tipi Ora'
                        },
                        {
                            xtype: 'tbseparator'
                        },
                        {
                            xtype: 'button',
                            id: 'EmployeesPrintsBtn',
                            itemId: 'EmployeesPrintsBtn',
                            iconCls: 'icon-printer',
                            text: 'Stampe',
                            menu: {
                                xtype: 'menu',
                                items: [
                                    {
                                        xtype: 'menuitem',
                                        handler: function(item, e) {
                                            Ext.widget('EmployeePresencesPrintWin').show();
                                            Ext.getStore('AbsenceStacks').load();

                                            var empId = parseInt(Ext.getCmp('EmployeePnl').getSelectedEmployee().employee_id);
                                            if(Ext.getStore('EmployeesAll').getById(empId) != null){
                                                var index = Ext.getStore('EmployeesAll').getById(empId).index;
                                                Ext.getCmp('EmployeePresencesPrintMonthGrid').getSelectionModel().select(index, true);
                                            }
                                        },
                                        iconCls: 'icon-application_view_list',
                                        text: 'Riepilogo Mensile'
                                    },
                                    {
                                        xtype: 'menuitem',
                                        handler: function(item, e) {
                                            Ext.widget('EmployeeDayPrintWin').show();
                                        },
                                        iconCls: 'icon-date',
                                        text: 'Riepilogo Giornaliero'
                                    },
                                    {
                                        xtype: 'menuitem',
                                        handler: function(item, e) {
                                            Ext.widget('EmployeeResidualsPrint_Win').show();
                                            Ext.getStore('AbsenceStacksAndExtraordinary').load();

                                            var empId = parseInt(Ext.getCmp('EmployeePnl').getSelectedEmployee().employee_id);
                                            if(Ext.getStore('EmployeesAll').getById(empId) != null){
                                                var index = Ext.getStore('EmployeesAll').getById(empId).index;
                                                Ext.getCmp('EmployeeResidualsPrint_Employees').getSelectionModel().select(index, true);
                                            }
                                        },
                                        iconCls: 'icon-chart_bar',
                                        text: 'Residui e Monteore'
                                    },
                                    {
                                        xtype: 'menuseparator'
                                    },
                                    {
                                        xtype: 'menuitem',
                                        handler: function(item, e) {
                                            Ext.widget('EmployeeEntriesExitsPrint_Win').show();

                                            var empId = parseInt(Ext.getCmp('EmployeePnl').getSelectedEmployee().employee_id);
                                            if (Ext.getStore('EmployeesAll').getById(empId) !== null) {
                                                var index = Ext.getStore('EmployeesAll').getById(empId).index;
                                                Ext.getCmp('EmployeeEntriesExitsPrint_Employees').getSelectionModel().select(index, true);
                                            }
                                        },
                                        iconCls: 'icon-arrow_sw_ne',
                                        text: 'Entrate/Uscite'
                                    },
                                    {
                                        xtype: 'menuitem',
                                        handler: function(item, e) {
                                            // Takes all people to print and put it in JSON encode array of employee_id
                                            var ids = new Array();
                                            Ext.each(Ext.getStore('EmployeesAll').getRange(), function(a){
                                                ids = ids.concat(parseInt(a.get('employee_id')));
                                            });

                                            Ext.Ajax.request({
                                                url: '/mc2-api/core/print',
                                                params:{
                                                    newSpool: 0,
                                                    print: 'WhosInside',
                                                    namespace: 'Personnel',
                                                    type: 'PDF',
                                                    printClass: 'PrintPDFWhosInside',
                                                    mime: 'application/pdf',
                                                    employees: Ext.JSON.encode(ids),
                                                    date: Ext.Date.format(new Date(), 'd-m-Y'),
                                                    time: Ext.Date.format(new Date(), 'H:i')
                                                },
                                                success: function(response, opts) {
                                                    var res = Ext.decode(response.responseText);
                                                    mc2ui.app.showNotifyPrint(res);
                                                }
                                            });
                                        },
                                        iconCls: 'icon-door_in',
                                        text: 'Chi è dentro?'
                                    },
                                    {
                                        xtype: 'menuseparator'
                                    },
                                    {
                                        xtype: 'menuitem',
                                        handler: function(item, e) {
                                            Ext.widget('EmployeeAbsencesPrintWin').show();

                                            var empId = parseInt(Ext.getCmp('EmployeePnl').getSelectedEmployee().employee_id);
                                            if (Ext.getStore('EmployeesAll').getById(empId) !== null) {
                                                var index = Ext.getStore('EmployeesAll').getById(empId).index;
                                                Ext.getCmp('EmployeeAbsencesPrintEmployeesGrid').getSelectionModel().select(index, true);
                                            }
                                        },
                                        iconCls: 'icon-date_error',
                                        text: 'Assenze'
                                    },
                                    {
                                        xtype: 'menuseparator'
                                    },
                                    {
                                        xtype: 'menuitem',
                                        handler: function(item, e) {
                                            Ext.widget('EmployeeLastLockedMonthPrintWin').show();
                                        },
                                        iconCls: 'icon-lock',
                                        text: 'Situazione chiusure mensili'
                                    },
                                    {
                                        xtype: 'menuseparator'
                                    },
                                    {
                                        xtype: 'menuitem',
                                        handler: function(item, e) {
                                            Ext.Ajax.request({
                                                url: '/mc2-api/core/print',
                                                params:{
                                                    newSpool: 0,
                                                    print: 'Stacks',
                                                    namespace: 'Personnel',
                                                    type: 'PDF',
                                                    printClass: 'PrintPDFStacks',
                                                    mime: 'application/pdf'
                                                },
                                                success: function(response, opts) {
                                                    var res = Ext.decode(response.responseText);
                                                    mc2ui.app.showNotifyPrint(res);
                                                }
                                            });
                                        },
                                        iconCls: 'icon-sitemap',
                                        text: 'Elenco Monteore - Tipi assenza'
                                    },
                                    {
                                        xtype: 'menuitem',
                                        handler: function(item, e) {
                                            Ext.widget('EmployeeTimetablePrintWin').show();
                                        },
                                        iconCls: 'icon-time',
                                        text: 'Orario Lavorativo'
                                    }
                                ]
                            }
                        },
                        {
                            xtype: 'tbspacer',
                            flex: 1
                        },

                    ]
                }
            ]
        });

        me.callParent(arguments);
    },

    onEmployeeSearchTxtChange: function(field, newValue, oldValue, eOpts) {
        Ext.getCmp('EmployeeLeftPnl').EmployeeInlineFilter();
    },

    onGridviewItemContextMenu4: function(dataview, record, item, index, e, eOpts) {
        e.stopEvent();
        var newX = e.xy[0];
        var newY = e.xy[1];
        Ext.getCmp('EmployeeEditMn').showAt([newX,newY]);
    },

    onRowModelSelectionChange: function(model, selected, eOpts) {
        // Loads record on form only if one personnel is selected
        if (selected.length > 0) {
            Ext.getCmp('EmployeeListPnl').getView().loadForm(selected[0]);
        }
    },

    onEmployeeLeftPnlRender: function(component, eOpts) {
        Ext.getStore('Employees').load();
    },

    onEmployeeBirthplaceFocus: function(component, e, eOpts) {
        Ext.getStore('CoreCities').clearFilter();
    },

    onComboboxFocus: function(component, e, eOpts) {
        Ext.getStore('CoreCities').clearFilter();
    },

    onComboboxFocus1: function(component, e, eOpts) {
        Ext.getStore('CoreCities').clearFilter();
    },

    onButtonClick5: function(button, e, eOpts) {
        // Get panel and relative form
        var pnl = Ext.getCmp('EmployeeFrm');
        var form = pnl.getForm();

        // Get employee store
        var es = Ext.getStore('Employees');

        var resTxt = {};
        if (form.isValid()){
            pnl.setLoading();
            form.submit({
                success: function(action, response){
                    mc2ui.app.showNotifySave();
                    var res = Ext.JSON.decode(response.response.responseText);
                    var q_denomination = Ext.getCmp('EmployeeSearchTxt').getValue();

                    Ext.getStore('Employees').load({
                        params: {
                            q_name: q_denomination
                        },
                        callback: function(records, operation, success) {
                            var index = es.getById(res.results.employee_id).index;
                            if (index >= 0){
                                var grid = Ext.getCmp('EmployeeListPnl');
                                // Selects the newly added employee
                                grid.getSelectionModel().select(index);
                                // Fires the 'itemclick' event on the selected row.
                                grid.getView().loadForm(es.getById(res.results.employee_id));
                            }
                        }
                    });
                    pnl.setLoading(false);
                },
                failure: function(e, res){
                    var r = Ext.decode(res.response.responseText);
                    Ext.Msg.alert('ERRORE', r.message);
                    pnl.setLoading(false);
                }
            });
        }
    },

    onGridviewItemContextMenu: function(dataview, record, item, index, e, eOpts) {
        e.stopEvent();
        var newX = e.xy[0],
            newY = e.xy[1],
            locked = record.get('locked'),
            menu = Ext.getCmp('AbsenceEditMn');
        menu.showAt([newX,newY]);

        if (record.raw.locked === false) {
            menu.items.get('contextAbsenceEdit').setDisabled(false);
            menu.items.get('contextAbsenceDelete').setDisabled(false);
        } else {
            menu.items.get('contextAbsenceEdit').setDisabled(true);
            menu.items.get('contextAbsenceDelete').setDisabled(true);
        }
    },

    onPagingtoolbarBeforeChange: function(pagingtoolbar, page, eOpts) {
        var selected = Ext.getCmp('EmployeeListPnl').getSelectionModel().getSelection(),
            employee_id = selected[0].get('employee_id'),
            store = Ext.getStore('Absences');

        store.getProxy().extraParams.employee_id = employee_id;
    },

    onPanelActivate: function(component, eOpts) {
        var selected = Ext.getCmp('EmployeeListPnl').getSelectionModel().getSelection();

        if (selected.length > 0) {
            var employee_id = selected[0].get('employee_id'),
                store = Ext.getStore('Absences');
            store.removeAll();
            store.currentPage = 1;
            store.load({
                params: {
                    employee_id: employee_id
                }
            });
        }
    },

    onEmployeeHourTabActivate: function(tab, eOpts) {
        Ext.getCmp('TimeTableTabPnl').loadData();
    },

    onTimetableMonthCmbSelect: function(combo, records, eOpts) {
        Ext.getCmp('TimeTableTabPnl').loadData();
    },

    onTimetableYearCmbSelect: function(combo, records, eOpts) {
        Ext.getCmp('TimeTableTabPnl').loadData();
    },

    onTimetableCalendarViewItemContextMenu: function(dataview, record, item, index, e, eOpts) {
        var month = Ext.getCmp('TimetableMonthCmb').getValue();
        var year = Ext.getCmp('TimetableYearCmb').getValue();
        var day = record.raw.day;
        var date = year + '-';
        var menu = Ext.getCmp('TimeTableCalendarMn');

        date = month < 10 ? date+'0'+month: date+month;
        date = date + '-';
        date = record.raw.day < 10 ? date+'0'+day: date+day;

        e.stopEvent();
        var newX = e.xy[0];
        var newY = e.xy[1];

        Ext.getCmp('TimetableCalendarView').select(index);
        Ext.getCmp('TimeTableCalendarMn').showAt([newX,newY]);

        if (date == record.raw.date && record.raw.locked === false) {
            menu.items.get('contextTimetableEdit').setDisabled(false);
            menu.items.get('contextTimetableDelete').setDisabled(false);
            menu.items.get('contextTimetableDeleteWeek').setDisabled(false);
            menu.items.get('contextTimetableDeletePeriod').setDisabled(false);
        } else {
            menu.items.get('contextTimetableEdit').setDisabled(true);
            menu.items.get('contextTimetableDelete').setDisabled(true);
            menu.items.get('contextTimetableDeleteWeek').setDisabled(true);
            menu.items.get('contextTimetableDeletePeriod').setDisabled(true);
        }
    },

    onEmployeeInOutTabActivate: function(tab, eOpts) {
        Ext.getCmp('EmployeeInOutMenuTab').loadData();
    },

    onPresencesMonthCmbSelect: function(combo, records, eOpts) {
        Ext.getCmp('EmployeeInOutMenuTab').loadData();
    },

    onPresencesYearCmbSelect: function(combo, records, eOpts) {
        Ext.getCmp('EmployeeInOutMenuTab').loadData();
    },

    onPresencesPerDayListItemContextMenu: function(dataview, record, item, index, e, eOpts) {
        e.stopEvent();
        var newX = e.xy[0];
        var newY = e.xy[1];
        var menu = Ext.getCmp('PresencesPerDayMn');

        Ext.getCmp('PresencesPerDayMn').showAt([newX,newY]);

        if (record.raw.locked === false) {
            menu.items.get('contextPresenceEdit').setDisabled(false);
            menu.items.get('contextPresenceDelete').setDisabled(false);
        } else {
            menu.items.get('contextPresenceEdit').setDisabled(true);
            menu.items.get('contextPresenceDelete').setDisabled(true);
        }
    },

    onPanelActivate1: function(component, eOpts) {
        Ext.getCmp('EmployeeProjectsGrid').setLoading();
        Ext.getStore('EmployeeProjects').load({
            params:{
                employee_id: Ext.getCmp('EmployeePnl').getSelectedEmployee().employee_id
            },
            callback: function(){
                Ext.getCmp('EmployeeProjectsGrid').setLoading(false);
            }
        });
    },

    onLocksMonthCmbSelect: function(combo, records, eOpts) {
        Ext.getCmp('EmployeeLockMonthMenuTab').loadData();
    },

    onLocksYearCmbSelect: function(combo, records, eOpts) {
        Ext.getCmp('EmployeeLockMonthMenuTab').loadData();
    },

    onStartMonthExtChange: function(field, newValue, oldValue, eOpts) {
        Ext.getCmp('EmployeeLockMonthMenuTab').setFinalTotal();
    },

    onStartMonthExtBlur: function(component, e, eOpts) {
        if (!Ext.getCmp('StartMonthExt').getValue()) {
            Ext.getCmp('StartMonthExt').setValue(0);
        }
    },

    onEndMonthExtBlur: function(component, e, eOpts) {
        if (!Ext.getCmp('EndMonthExt').getValue()) {
            Ext.getCmp('EndMonthExt').setValue(0);
        }
    },

    onLockMonthGridViewItemUpdate: function(record, index, node, eOpts) {
        Ext.getCmp('EmployeeLockMonthMenuTab').setFinalTotal();
    },

    onNumberfieldChange: function(field, newValue, oldValue, eOpts) {
        var row = Ext.getCmp('LockStacksGrid').getSelectionModel().getSelection()[0];
        if (newValue !== null) {
            row.set({
                totalEnd: parseFloat(newValue) - parseFloat(row.get('totalStartO')) + parseFloat(row.get('totalEndO'))
            });
        };
    },

    onEmployeeLockMonthMenuTabActivate: function(component, eOpts) {
        Ext.getCmp('EmployeeLockMonthMenuTab').loadData();
    },

    onNumberfieldChange1: function(field, newValue, oldValue, eOpts) {
        var grid = Ext.getCmp('EmpSettingsStacksGrid'),
            record = grid.getSelectionModel().getSelection()[0];

        if (record.get('unit') === 'h' && newValue !== parseInt(newValue)) {
            field.setValue(parseInt(newValue));
        }
    },

    onRowEditingEdit: function(editor, context, eOpts) {
        var links_store = Ext.getCmp('EmpSettingsStacksGrid').getStore();

        links_store.sync();
    },

    onRowEditingBeforeEdit1: function(editor, context, eOpts) {
        var pg = Ext.getCmp('EmpSettingsStacksGrid');

        return !pg.getPlugin().blocked;
    },

    onEmpSettingsStacksGridItemContextMenu: function(dataview, record, item, index, e, eOpts) {
        e.stopEvent();
        var newX = e.xy[0];
        var newY = e.xy[1];
        Ext.getCmp('EmpSettingsStacksMn').showAt([newX,newY]);
    },

    onPanelActivate2: function(component, eOpts) {
        var employee_id = Ext.getCmp('EmployeePnl').getSelectedEmployee().employee_id,
            data = Ext.getStore('Employees').getById(employee_id).getData(),
            form = Ext.getCmp('EmpSettingsGeneralFrm').getForm();
            stacksLinksStore = Ext.getStore('StacksPersonnelLinks');

        form.setValues(data);
        form = Ext.getCmp('EmpSettingsStrFrm').getForm();
        form.setValues(data);
        form = Ext.getCmp('EmpSettingsRecFrm').getForm();
        form.setValues(data);

        stacksLinksStore.load({
            params: {employee: employee_id}
        });

        /*var EmployeesStore = Ext.getStore('Employees'),
        EntriesChartStore = Ext.getStore('EmployeeParameters'),
        record = EmployeesStore.getById(employee_id);

        Ext.getCmp('IEMin').setValue(record.raw.min_extraordinary_in);
        Ext.getCmp('IEMax').setValue(record.raw.max_extraordinary_in);

        EntriesChartStore.add({
        employee_id:				record.raw.employee_id,
        step_in:					record.raw.step_in,
        step_in_und:				record.raw.step_in_und,
        step_out:					record.raw.step_out,
        step_out_und:				record.raw.step_out_und,
        step_total_extraordinary:	record.raw.step_total_extraordinary,
        step_total_undefined:		record.raw.step_total_undefined,
        min_extraordinary_in:		record.raw.min_extraordinary_in,
        min_extraordinary_out:		record.raw.min_extraordinary_out,
        min_extraordinary_total:	record.raw.min_extraordinary_total,
        min_undefined_in:			record.raw.min_undefined_in,
        min_undefined_out:			record.raw.min_undefined_out,
        min_undefined_total:		record.raw.min_undefined_total,
        max_extraordinary_in:		record.raw.max_extraordinary_in,
        max_extraordinary_out:		record.raw.max_extraordinary_out,
        max_extraordinary_total:	record.raw.max_extraordinary_total,
        max_undefined_in:			record.raw.max_undefined_in,
        max_undefined_out:			record.raw.max_undefined_out,
        max_undefined_total:		record.raw.max_undefined_total,
        unit_recover_hours:			record.raw.unit_recover_hours,
        recover_hours:				record.raw.recover_hours,
        max_cont_work:				record.raw.max_cont_work,
        break_after_max_work:		record.raw.break_after_max_work
        });
        EntriesChartStore.add(record);
        console.log(EntriesChartStore.first());

        -----
        Per textfield parametri
        if (Ext.getCmp('IEMin').getValue() > newValue) {
        field.setValue(oldValue);
        } else {
        var normValue = 0;
        if (newValue == 999) {
        normValue = 10;
        } else {
        normValue = 7;
        }
        Ext.getStore('EmployeeParameters').first().set('max_extraordinary_in', normValue);
        }

        Per renderer Series
        var color = ['rgba(0, 0, 0, 0)',
        'rgba(80, 80, 255, 255)',
        'rgba(0, 0, 0, 0)',
        'rgba(80, 80, 255, 255)'][index];

        return Ext.apply(attributes, {
            fill: color,
            stroke: 'rgba(0,0,0,0)'
        });
        -----*/
    },

    onEmployeePnlBoxReady: function(component, width, height, eOpts) {
        Ext.getStore('EmployeesAll').load();
        Ext.getStore('SocialPosition').load();
        Ext.getStore('CoreCities').load();
        Ext.getStore('CoreCountries').load();

        var today = new Date();
        Ext.getCmp('EmployeePnl').updateMonthYear(Ext.Date.format(today, "n"), Ext.Date.format(today, "Y"), 0);
    },

    getSelectedEmployee: function() {
        var arrEmp = Ext.getCmp('EmployeeListPnl').getSelectionModel().getSelection();
        if (arrEmp.length > 0){
            return arrEmp[0].getData();
        } else {
            return -1;
        }
    },

    formatHHMM: function(value, valueIsInDay) {
        if (valueIsInDay) {
            return value;
        }

        var negative = value < 0 ? "-" : "",
            hours = parseInt(Math.abs(value)/60, 10),
            minutes = ('0' + parseInt(Math.abs(value)%60, 10)).slice(-2);

        hours = hours < 10 ? '0' + hours : hours;

        return negative + hours + ':' + minutes;
    },

    updateMonthYear: function(month, year, area) {
        month = parseInt(month);
        year = parseInt(year);
        switch (area) {
            // Timetables update
            case 1:
            Ext.getCmp('PresencesMonthCmb').setValue(month);
            Ext.getCmp('PresencesYearCmb').setValue(year);
            Ext.getCmp('LocksMonthCmb').setValue(month);
            Ext.getCmp('LocksYearCmb').setValue(year);
            break;
            // Presences update
            case 2:
            Ext.getCmp('TimetableMonthCmb').setValue(month);
            Ext.getCmp('TimetableYearCmb').setValue(year);
            Ext.getCmp('LocksMonthCmb').setValue(month);
            Ext.getCmp('LocksYearCmb').setValue(year);
            break;
            // Lock Month update
            case 3:
            Ext.getCmp('TimetableMonthCmb').setValue(month);
            Ext.getCmp('TimetableYearCmb').setValue(year);
            Ext.getCmp('PresencesMonthCmb').setValue(month);
            Ext.getCmp('PresencesYearCmb').setValue(year);
            break;
            // Monthly Print update
            case 4:
            break;
            default:
            Ext.getCmp('TimetableMonthCmb').setValue(month);
            Ext.getCmp('TimetableYearCmb').setValue(year);
            Ext.getCmp('PresencesMonthCmb').setValue(month);
            Ext.getCmp('PresencesYearCmb').setValue(year);
            Ext.getCmp('LocksMonthCmb').setValue(month);
            Ext.getCmp('LocksYearCmb').setValue(year);
            break;
        }
    }

});