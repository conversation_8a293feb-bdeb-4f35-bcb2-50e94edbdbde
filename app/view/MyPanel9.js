/*
 * File: app/view/MyPanel9.js
 *
 * This file was generated by Sencha Architect version 3.2.0.
 * http://www.sencha.com/products/architect/
 *
 * This file requires use of the Ext JS 4.2.x library, under independent license.
 * License of Sencha Architect does not include license for Ext JS 4.2.x. For more
 * details see http://www.sencha.com/license <NAME_EMAIL>.
 *
 * This file will be auto-generated each and everytime you save your project.
 *
 * Do NOT hand edit this file.
 */

Ext.define('mc2ui.view.MyPanel9', {
    extend: 'Ext.panel.Panel',
    alias: 'widget.mypanel9',

    requires: [
        'Ext.toolbar.Toolbar',
        'Ext.form.field.ComboBox',
        'Ext.toolbar.Spacer',
        'Ext.grid.Panel',
        'Ext.grid.View',
        'Ext.tab.Panel',
        'Ext.form.Label',
        'Ext.tab.Tab',
        'Ext.grid.column.Number',
        'Ext.grid.plugin.RowEditing',
        'Ext.grid.column.Action',
        'Ext.form.field.Number',
        'Ext.selection.CheckboxModel',
        'Ext.grid.column.Date',
        'Ext.form.Panel',
        'Ext.Img',
        'Ext.form.field.Hidden',
        'Ext.form.field.Checkbox',
        'Ext.form.field.TextArea',
        'Ext.form.field.Date',
        'Ext.menu.Menu',
        'Ext.menu.Item'
    ],

    title: 'Studenti',

    layout: {
        type: 'hbox',
        align: 'stretch'
    },

    initComponent: function() {
        var me = this;

        Ext.applyIf(me, {
            items: [
                {
                    xtype: 'panel',
                    width: 250,
                    layout: 'fit',
                    title: '',
                    dockedItems: [
                        {
                            xtype: 'toolbar',
                            dock: 'top',
                            items: [
                                {
                                    xtype: 'combobox',
                                    flex: 1,
                                    id: 'CcpStudentAddressSchoolYear',
                                    itemId: 'CcpStudentAddressSchoolYear',
                                    fieldLabel: '',
                                    emptyText: 'Anno scolastico attuale ...',
                                    editable: false,
                                    displayField: 'value',
                                    forceSelection: true,
                                    queryMode: 'local',
                                    store: 'CcpTypeSchoolYears',
                                    valueField: 'value',
                                    listeners: {
                                        select: {
                                            fn: me.onComboboxSelect9,
                                            scope: me
                                        }
                                    }
                                }
                            ]
                        },
                        {
                            xtype: 'toolbar',
                            dock: 'top',
                            items: [
                                {
                                    xtype: 'combobox',
                                    id: 'CcpSubjectsStateCmb',
                                    fieldLabel: '',
                                    value: 0,
                                    editable: false,
                                    autoSelect: false,
                                    displayField: 'descrizione',
                                    forceSelection: true,
                                    queryMode: 'local',
                                    store: 'StudentStates',
                                    valueField: 'id_stato_studente_personalizzato',
                                    listeners: {
                                        select: {
                                            fn: me.onComboboxSelect7,
                                            scope: me
                                        }
                                    }
                                },
                                {
                                    xtype: 'tbspacer',
                                    flex: 1
                                },
                                {
                                    xtype: 'button',
                                    handler: function(button, e) {
                                        var subject_school_year = Ext.getCmp('CcpStudentAddressSchoolYear').getValue(),
                                            subject_type = Ext.getCmp('CcpSubjectsStateCmb').getValue();

                                        window.open('/mc2-api/ccp/export_easy_anagrafica?subject_school_year='+subject_school_year+'&subject_type='+subject_type,'_blank');
                                    },
                                    iconCls: 'icon-disk_upload',
                                    text: 'EASY'
                                }
                            ]
                        },
                        {
                            xtype: 'toolbar',
                            dock: 'top',
                            items: [
                                {
                                    xtype: 'textfield',
                                    flex: 2,
                                    id: 'CcpStudentsSearchTxt',
                                    itemId: 'CcpStudentsSearchTxt',
                                    fieldLabel: '',
                                    emptyText: 'Cerca ...',
                                    selectOnFocus: true,
                                    listeners: {
                                        change: {
                                            fn: me.onCcpStudentsSearchTxtChange,
                                            scope: me
                                        }
                                    }
                                }
                            ]
                        }
                    ],
                    items: [
                        {
                            xtype: 'gridpanel',
                            filterByStudent: function(studentIdMc) {
                                var parentPnl = Ext.getCmp('CcpStudentParentsPnl');

                                Ext.getCmp('CcpMovementsStudentGrid').setSummary();
                                Ext.getCmp('CcpStudentMovementLbl').getEl().setHTML('<b>Totali dei movimenti dello studente</b>');

                                var params = {
                                    subject_type: 'S',
                                    subject_id: studentIdMc,
                                    limit: 0,
                                    add_student_credit: true,
                                    sort: Ext.encode([{property: 'expiration_date', direction: 'ASC'}])
                                };
                                if(Ext.getCmp('CcpStudentMovementYearFilterCmb').getValue()) {
                                    params.subject_school_year = Ext.getCmp('CcpStudentMovementYearFilterCmb').getValue();
                                }

                                Ext.getStore('CcpStudentMovements').load({
                                    params: params,
                                    callback: function(records, operation, success, a, b) {

                                        var res = Ext.decode(operation.response.responseText);
                                        Ext.getCmp('CcpMovementsStudentGrid').setSummary(res);
                                    }
                                });

                                Ext.getStore('Discounts').load({
                                    params: {
                                        student_id: studentIdMc
                                    }
                                });

                                Ext.getStore('CcpStudentReceipts').load({
                                    params: {
                                        subject_id: studentIdMc
                                    }
                                });

                                Ext.getStore('CcpStudentMovementsMensa').load({
                                    params: {
                                        subject_id: studentIdMc,
                                        subject_school_year: Ext.getCmp('CcpStudentAddressSchoolYear').getValue()
                                    },
                                    callback: function(res,r,sss,rq) {
                                        if(!r.response) return;
                                        var data = Ext.decode(r.response.responseText).results;
                                        Ext.getCmp('CcpMensaTotalsMovementsLbl').setText('<b>Totale richiesto: '+data.totale_richiesto+' Totale pagato: '+data.totale_pagato+'</b>', false);
                                        Ext.getCmp('CcpMensaCredRimLbl').setText('<b>Credito rimanente: '+data.totale_richiesto+' - '+data.totale_costo_pasti+' = '+data.credito_rimasto+'</b>', false);
                                        Ext.getCmp('CcpMensaDaPagLbl').setText('<b>Totale ancora da pagare: '+data.totale_richiesto+' - '+data.totale_pagato+' = '+data.totale_ancora_da_pagare+'</b>', false);

                                        var pcs = Ext.getStore('CcpPastiConsumati');
                                        pcs.removeAll();
                                        Ext.each(data.pasti_consumati_array, function(v){
                                            pcs.add(v);
                                        });
                                        pcs.add({'descrizione': 'TOTALE', 'numero_pasti': data.pasti_consumati_totale, 'costo_totale': data.totale_costo_pasti});

                                        var prs = Ext.getStore('CcpPastiRim');
                                        prs.removeAll();
                                        Ext.each(data.pasti_rimasti_array, function(v){
                                            prs.add(v);
                                        });

                                        if(data.consolidamento_presente == 'SI') {
                                            var dd = data.ultimo_consolidamento.data_inizio_tradotta.split('-'),
                                                consolidamento = {
                                                    type_text: data.ultimo_consolidamento.descrizione,
                                                    total: data.ultimo_consolidamento.credito,
                                                    creation_date: new Date(dd[2] + '-' + dd[1] +'-'+dd[0]),
                                                    consolidamento: true
                                                };
                                            Ext.getStore('CcpStudentMovementsMensa').insert(0, consolidamento);

                                        }


                                        var strMensa = data.credito_rimasto < 0 ? '<font style=color:red>'+data.credito_rimasto+'€</font>' : data.credito_rimasto+'€';
                                        Ext.getCmp('CcpStudentMensaDataLbl').setText('Credito mensa rimasto: '+ strMensa, false);

                                    }
                                });

                                Ext.getStore('Credits').load({
                                    params: {
                                        student_id: studentIdMc
                                    }
                                });

                                Ext.getCmp('CcpStudentsTab').setLoading(true);
                                Ext.Ajax.request({
                                    method: 'GET',
                                    url: '/mc2-api/ccp/student_detail',
                                    params: {
                                        student_id: studentIdMc,
                                        subject_school_year: Ext.getCmp('CcpStudentAddressSchoolYear').getValue()
                                    },
                                    success: function(r) {
                                        var student = Ext.decode(r.responseText).results;
                                        student.subject_school_year = Ext.getCmp('CcpStudentAddressSchoolYear').getValue();
                                        Ext.getCmp('CcpStudentsAnagraficaFrm').getForm().setValues(student);

                                        Ext.getCmp('CcpStudentImg').setSrc(student.foto);

                                        var studentText = student.cognome + ' ' + student.nome + ' ('+student.classe+')';

                                        Ext.Ajax.request({
                                            method: 'GET',
                                            url: '/mc2-api/ccp/parents',
                                            params: {
                                                student_id: studentIdMc,
                                                subject_school_year: Ext.getCmp('CcpStudentAddressSchoolYear').getValue()
                                            },
                                            success: function(r) {
                                                var parents = Ext.decode(r.responseText).results;
                                                parentPnl.generateParentPnl(parents);

                                                if(parents.length > 0) {
                                                    if(parents[0].pagante == 't') {
                                                        studentText += ' - ' + parents[0].cognome + ' ' + parents[0].nome;
                                                        if(parents[0].telefono_cellulare) studentText += ' ' + parents[0].telefono_cellulare;
                                                    }
                                                }
                                                Ext.getCmp('CcpStudentDataLbl').setText(studentText, false);
                                                Ext.getCmp('CcpStudentsTab').setLoading(false);

                                            }
                                        });

                                    }
                                });



                                Ext.getStore('CcpStudentMarkets').load({
                                    params: {
                                        id_studente: studentIdMc,
                                        subject_school_year: Ext.getCmp('CcpStudentAddressSchoolYear').getValue()
                                    }
                                });

                                Ext.getStore('CcpStudentsPayments').load({
                                    params: {
                                        subject_id: studentIdMc
                                    }
                                });

                            },
                            filterSubjects: function() {
                                var state = Ext.getCmp('CcpSubjectsStateCmb').getValue(),
                                    name = Ext.getCmp('CcpStudentsSearchTxt').getValue(),
                                    store = Ext.getStore('CcpStudents');

                                store.clearFilter();

                                if(name) {
                                    /*store.filter({
                                    property: 'display',
                                    anyMatch: true,
                                    value   : name
                                    });
                                    */
                                    store.filterBy(function(rec, id) {
                                        if(rec.get('display').toLowerCase().indexOf(name.toLowerCase()) > -1 || rec.get('genitori').toLowerCase().indexOf(name.toLowerCase()) > -1) {
                                            return true;
                                        }
                                        else {
                                            return false;
                                        }
                                    });
                                }

                                if(state > 0) {
                                    store.filter({
                                        property: 'stato_studente_personalizzato',
                                        value   : state,
                                        exactMatch: true
                                    });
                                }

                            },
                            id: 'CcpStudentsGrd',
                            itemId: 'CcpStudentsGrd',
                            title: '',
                            hideHeaders: true,
                            store: 'CcpStudents',
                            columns: [
                                {
                                    xtype: 'gridcolumn',
                                    renderer: function(value, metaData, record, rowIndex, colIndex, store, view) {
                                        html = record.get('surname') + ' ' + record.get('name') + ' ('+record.get('class')+record.get('section')+' - '+record.get('school_address_code')+')';
                                        html += '<br /> <font style="font-style:italic;font-size:9px;color:#333"> ' + record.get('genitori') + '</font>';

                                        return html;
                                    },
                                    dataIndex: 'display',
                                    flex: 1
                                }
                            ],
                            viewConfig: {
                                listeners: {
                                    itemclick: {
                                        fn: me.onViewItemClick,
                                        scope: me
                                    },
                                    itemkeydown: {
                                        fn: me.onViewItemKeydown,
                                        scope: me
                                    }
                                }
                            }
                        }
                    ]
                },
                {
                    xtype: 'tabpanel',
                    reloadStudent: function() {
                        var selectedStudent = Ext.getCmp('CcpStudentsGrd').getSelectionModel().getSelection()[0];
                        if(selectedStudent) {
                            Ext.getCmp('CcpStudentsGrd').filterByStudent(selectedStudent.get('db_id'));
                        }
                    },
                    flex: 1,
                    id: 'CcpStudentsTab',
                    itemId: 'CcpStudentsTab',
                    title: '',
                    activeTab: 0,
                    dockedItems: [
                        {
                            xtype: 'toolbar',
                            dock: 'top',
                            items: [
                                {
                                    xtype: 'tbspacer',
                                    flex: 1
                                },
                                {
                                    xtype: 'label',
                                    id: 'CcpStudentDataLbl',
                                    itemId: 'CcpStudentDataLbl',
                                    padding: 5
                                },
                                {
                                    xtype: 'label',
                                    id: 'CcpStudentMensaDataLbl',
                                    padding: 5
                                },
                                {
                                    xtype: 'tbspacer',
                                    flex: 1
                                }
                            ]
                        }
                    ],
                    items: [
                        {
                            xtype: 'panel',
                            id: 'CcpStudentMovementsTab',
                            itemId: 'CcpStudentMovementsTab',
                            title: 'Movimenti',
                            layout: {
                                type: 'vbox',
                                align: 'stretch'
                            },
                            items: [
                                {
                                    xtype: 'container',
                                    maxHeight: 300,
                                    layout: {
                                        type: 'hbox',
                                        align: 'stretch'
                                    },
                                    items: [
                                        {
                                            xtype: 'gridpanel',
                                            flex: 1,
                                            height: 200,
                                            id: 'CcpCreditsGrd',
                                            padding: 10,
                                            title: 'Crediti',
                                            store: 'Credits',
                                            columns: [
                                                {
                                                    xtype: 'gridcolumn',
                                                    dataIndex: 'description',
                                                    text: 'Nome',
                                                    flex: 1,
                                                    editor: {
                                                        xtype: 'combobox',
                                                        displayField: 'description',
                                                        queryMode: 'local',
                                                        store: 'CreditsType',
                                                        valueField: 'id',
                                                        listeners: {
                                                            expand: {
                                                                fn: me.onComboboxExpand1,
                                                                scope: me
                                                            }
                                                        }
                                                    }
                                                },
                                                {
                                                    xtype: 'numbercolumn',
                                                    dataIndex: 'amount',
                                                    text: 'Importo'
                                                },
                                                {
                                                    xtype: 'numbercolumn',
                                                    dataIndex: 'used',
                                                    text: 'Utilizzati'
                                                },
                                                {
                                                    xtype: 'numbercolumn',
                                                    dataIndex: 'total',
                                                    text: 'Totale rimasto'
                                                }
                                            ],
                                            viewConfig: {
                                                listeners: {
                                                    itemdblclick: {
                                                        fn: me.onViewItemDblClick2,
                                                        scope: me
                                                    }
                                                }
                                            },
                                            dockedItems: [
                                                {
                                                    xtype: 'toolbar',
                                                    dock: 'top',
                                                    items: [
                                                        {
                                                            xtype: 'button',
                                                            handler: function(button, e) {


                                                                var grd = Ext.getCmp('CcpCreditsGrd'),
                                                                    find = false;

                                                                Ext.each(grd.getStore().getRange(), function(v){
                                                                    if(!v.get('id')) {
                                                                        find=true;

                                                                    }
                                                                });

                                                                if(find===false) {
                                                                    grd.getStore().insert(0, {});
                                                                    grd.plugins[0].startEdit(0);
                                                                }


                                                            },
                                                            iconCls: 'icon-add',
                                                            text: 'Abbina credito'
                                                        }
                                                    ]
                                                }
                                            ],
                                            plugins: [
                                                Ext.create('Ext.grid.plugin.RowEditing', {
                                                    listeners: {
                                                        beforeedit: {
                                                            fn: me.onRowEditingBeforeEdit2,
                                                            scope: me
                                                        },
                                                        edit: {
                                                            fn: me.onRowEditingEdit3,
                                                            scope: me
                                                        },
                                                        canceledit: {
                                                            fn: me.onRowEditingCanceledit2,
                                                            scope: me
                                                        }
                                                    }
                                                })
                                            ]
                                        },
                                        {
                                            xtype: 'gridpanel',
                                            removeDiscount: function(record) {
                                                Ext.Ajax.request({
                                                    method: 'POST',
                                                    url: '/mc2-api/ccp/delete_subject_discount',
                                                    params: {
                                                        type_additional_id: record.get('id'),
                                                    },
                                                    success: function (res) {
                                                        var r = Ext.decode(res.responseText);
                                                        if(r.success === true) {
                                                            Ext.getStore('Discounts').remove(record);
                                                        }
                                                    }
                                                });
                                            },
                                            updateDiscount: function(record) {
                                                Ext.Ajax.request({
                                                    method: 'POST',
                                                    url: '/mc2-api/ccp/update_subject_discount',
                                                    params: {
                                                        type_additional_id: record.get('id'),
                                                        amount: record.get('amount'),
                                                        discount_order: record.get('discount_order')
                                                    },
                                                    success: function (res) {
                                                        var r = Ext.decode(res.responseText);
                                                        if(r.success === true) {
                                                            record.commit();
                                                        }
                                                    }
                                                });
                                            },
                                            flex: 1,
                                            height: 200,
                                            id: 'CcpStudentDiscountGrd',
                                            padding: 10,
                                            autoScroll: true,
                                            title: 'Sconti',
                                            store: 'Discounts',
                                            columns: [
                                                {
                                                    xtype: 'actioncolumn',
                                                    getClass: function(v, metadata, r, rowIndex, colIndex, store) {
                                                        if(r.get('subject_type') === 'S') {
                                                            return 'icon-user_b';
                                                        }

                                                        return 'icon-group';
                                                    },
                                                    width: 35,
                                                    align: 'center',
                                                    dataIndex: 'string'
                                                },
                                                {
                                                    xtype: 'gridcolumn',
                                                    dataIndex: 'additional_text',
                                                    text: 'Nome',
                                                    flex: 1
                                                },
                                                {
                                                    xtype: 'gridcolumn',
                                                    dataIndex: 'type_text',
                                                    text: 'Tipo movimento',
                                                    flex: 1,
                                                    renderer: function(value, metaData, record, rowIndex, colIndex, store, view) {
                                                        return value + ' ('+record.get('type_school_year')+')';
                                                    }
                                                },
                                                {
                                                    xtype: 'numbercolumn',
                                                    width: 150,
                                                    align: 'center',
                                                    dataIndex: 'amount',
                                                    text: 'Ammontare sconto (%)',
                                                    format: '0.00',
                                                    editor: {
                                                        xtype: 'numberfield',
                                                        name: 'amount',
                                                        editable: false,
                                                        hideTrigger: true
                                                    }
                                                },
                                                {
                                                    xtype: 'numbercolumn',
                                                    width: 90,
                                                    dataIndex: 'discount_order',
                                                    text: 'Ordinamento',
                                                    format: '0',
                                                    editor: {
                                                        xtype: 'numberfield'
                                                    }
                                                },
                                                {
                                                    xtype: 'actioncolumn',
                                                    width: 35,
                                                    align: 'center',
                                                    iconCls: 'icon-delete',
                                                    items: [
                                                        {
                                                            handler: function(view, rowIndex, colIndex, item, e, record, row) {


                                                                if(record.get('subject_type') === 'P')
                                                                Ext.Msg.confirm('ATTENZIONE','Questo sconto è applicato al parente. Questo significa che la modifica inciderà su tutti i suoi figli. Confermi l\'operazione?', function (btn) {

                                                                    if(btn === 'yes') {
                                                                        Ext.getCmp('CcpStudentDiscountGrd').removeDiscount(record);
                                                                    }
                                                                });
                                                                else
                                                                Ext.getCmp('CcpStudentDiscountGrd').removeDiscount(record);

                                                            }
                                                        }
                                                    ]
                                                }
                                            ],
                                            plugins: [
                                                Ext.create('Ext.grid.plugin.RowEditing', {
                                                    listeners: {
                                                        edit: {
                                                            fn: me.onRowEditingEdit,
                                                            scope: me
                                                        }
                                                    }
                                                })
                                            ]
                                        }
                                    ]
                                },
                                {
                                    xtype: 'gridpanel',
                                    deleteMovement: function() {
                                        var record = Ext.getCmp('CcpMovementsStudentGrid').getSelectionModel().getSelection()[0];

                                        Ext.Msg.show({
                                            title: 'Eliminazione Movimento numero ' + record.get('number'),
                                            msg: 'Sei sicuro di voler eliminare questo Movimento?<br />Verranno eliminati anche i pagamenti associati e sarà necessario emettere nuovamente le eventuali fatture che contenevano tali pagamenti se queste erano legate ad ulteriori altri pagamenti.',
                                            buttons: Ext.Msg.YESNO,
                                            fn: function(r){
                                                if (r == 'yes') {
                                                    sMovs = Ext.getStore('CcpStudentMovements');
                                                    sTypes = Ext.getStore('CcpTypes');
                                                    sAdds = Ext.getStore('CcpAdditionals');
                                                    sCats = Ext.getStore('CcpCategories');
                                                    sMovs.remove(record);
                                                    sMovs.sync({
                                                        callback: function () {
                                                            //Ext.getCmp('CcpMovementsFilterForm').loadByFilter();
                                                            sMovs.load({params: {
                                                                subject_id: record.get('subject_id'),
                                                                subject_type: 'S'
                                                            }});
                                                            sTypes.load();
                                                            sAdds.load();
                                                            sCats.load();
                                                        },
                                                        success: function() {
                                                            Ext.Msg.alert('Successo', 'Movimento eliminato');
                                                        },
                                                        failure: function() {
                                                            Ext.Msg.alert('Attenzione', 'Movimento NON eliminato');
                                                        }
                                                    });
                                                }
                                            }
                                        });
                                    },
                                    setSummary: function(res) {
                                        Ext.getCmp('CcpStudentImg').setSrc('');
                                        Ext.getCmp('CcpStTotalCredit').setValue(Ext.util.Format.number(0, '0.000,00/i'));
                                        Ext.getCmp('CcpStTotalCreditPayed').setValue(Ext.util.Format.number(0, '0.000,00/i'));
                                        Ext.getCmp('CcpStTotalCreditToPay').setValue(Ext.util.Format.number(0, '0.000,00/i'));
                                        Ext.getCmp('CcpStTotalCreditExp').setValue(Ext.util.Format.number(0, '0.000,00/i'));
                                        Ext.getCmp('CcpStTotalCreditNotExp').setValue(Ext.util.Format.number(0, '0.000,00/i'));
                                        Ext.getCmp('CcpStTotalDebit').setValue(Ext.util.Format.number(0, '0.000,00/i'));
                                        Ext.getCmp('CcpStTotalDebitPayed').setValue(Ext.util.Format.number(0, '0.000,00/i'));
                                        Ext.getCmp('CcpStTotalDebitToPay').setValue(Ext.util.Format.number(0, '0.000,00/i'));
                                        Ext.getCmp('CcpStTotalDebitExp').setValue(Ext.util.Format.number(0, '0.000,00/i'));
                                        Ext.getCmp('CcpStTotalDebitNotExp').setValue(Ext.util.Format.number(0, '0.000,00/i'));
                                        Ext.getCmp('CcpStTotal').setValue(Ext.util.Format.number(0, '0.000,00/i'));
                                        Ext.getCmp('CcpStTotalPayed').setValue(Ext.util.Format.number(0, '0.000,00/i'));
                                        Ext.getCmp('CcpStTotalToPay').setValue(Ext.util.Format.number(0, '0.000,00/i'));
                                        Ext.getCmp('CcpStTotalExp').setValue(Ext.util.Format.number(0, '0.000,00/i'));
                                        Ext.getCmp('CcpStTotalNotExp').setValue(Ext.util.Format.number(0, '0.000,00/i'));
                                        Ext.getCmp('CcpStudentTotaDiscountTxt').setValue(Ext.util.Format.number(0, '0.000,00/i'));
                                        Ext.getCmp('CcpStudentTotaImportTxt').setValue(Ext.util.Format.number(0, '0.000,00/i'));

                                        if(res) {
                                            Ext.getCmp('CcpStTotalCredit').setValue(Ext.util.Format.number(res.total_credit, '0.000,00/i'));
                                            Ext.getCmp('CcpStTotalCreditPayed').setValue(Ext.util.Format.number(res.total_credit_payed, '0.000,00/i'));
                                            Ext.getCmp('CcpStTotalCreditToPay').setValue(Ext.util.Format.number(res.total_credit-res.total_credit_payed, '0.000,00/i'));
                                            Ext.getCmp('CcpStTotalCreditExp').setValue(Ext.util.Format.number(res.total_credit_exp, '0.000,00/i'));
                                            Ext.getCmp('CcpStTotalCreditNotExp').setValue(Ext.util.Format.number(res.total_credit_not_exp, '0.000,00/i'));
                                            Ext.getCmp('CcpStTotalDebit').setValue(Ext.util.Format.number(res.total_debit, '0.000,00/i'));
                                            Ext.getCmp('CcpStTotalDebitPayed').setValue(Ext.util.Format.number(res.total_debit_payed, '0.000,00/i'));
                                            Ext.getCmp('CcpStTotalDebitToPay').setValue(Ext.util.Format.number(res.total_debit-res.total_debit_payed, '0.000,00/i'));
                                            Ext.getCmp('CcpStTotalDebitExp').setValue(Ext.util.Format.number(res.total_debit_exp, '0.000,00/i'));
                                            Ext.getCmp('CcpStTotalDebitNotExp').setValue(Ext.util.Format.number(res.total_debit_not_exp, '0.000,00/i'));
                                            Ext.getCmp('CcpStTotal').setValue(Ext.util.Format.number(res.total_credit - res.total_debit, '0.000,00/i'));
                                            Ext.getCmp('CcpStTotalPayed').setValue(Ext.util.Format.number(res.total_credit_payed - res.total_debit_payed, '0.000,00/i'));
                                            Ext.getCmp('CcpStTotalToPay').setValue(Ext.util.Format.number((res.total_credit - res.total_debit) - (res.total_credit_payed - res.total_debit_payed), '0.000,00/i'));
                                            Ext.getCmp('CcpStTotalExp').setValue(Ext.util.Format.number(res.total_credit_exp - res.total_debit_exp, '0.000,00/i'));
                                            Ext.getCmp('CcpStTotalNotExp').setValue(Ext.util.Format.number(res.total_credit_not_exp - res.total_debit_not_exp, '0.000,00/i'));
                                            Ext.getCmp('CcpStudentTotaDiscountTxt').setValue(Ext.util.Format.number(res.total_additionals, '0.000,00/i'));
                                            Ext.getCmp('CcpStudentTotaImportTxt').setValue(Ext.util.Format.number( (parseFloat(res.total_credit)+(res.total_additionals*-1)), '0.000,00/i'));
                                        }
                                    },
                                    flex: 1,
                                    id: 'CcpMovementsStudentGrid',
                                    itemId: 'CcpMovementsStudentGrid',
                                    title: '',
                                    store: 'CcpStudentMovements',
                                    dockedItems: [
                                        {
                                            xtype: 'toolbar',
                                            dock: 'top',
                                            items: [
                                                {
                                                    xtype: 'tbspacer',
                                                    flex: 1
                                                },
                                                {
                                                    xtype: 'combobox',
                                                    id: 'CcpStudentMovementYearFilterCmb',
                                                    fieldLabel: 'Anno movimento',
                                                    emptyText: 'Tutti ...',
                                                    displayField: 'value',
                                                    queryMode: 'local',
                                                    store: 'CcpTypeSchoolYears',
                                                    valueField: 'value',
                                                    listeners: {
                                                        select: {
                                                            fn: me.onCcpStudentMovementYearFilterCmbSelect,
                                                            scope: me
                                                        },
                                                        change: {
                                                            fn: me.onComboboxChange1,
                                                            delay: 500,
                                                            scope: me
                                                        }
                                                    }
                                                },
                                                {
                                                    xtype: 'button',
                                                    handler: function(button, e) {
                                                        Ext.widget('CcpPrintTypeWin').show();
                                                        Ext.getCmp('CcpPrintTypeType').setValue('M');

                                                        Ext.getCmp('CcpPrintMovementFromStudentBtn').show();
                                                        Ext.getCmp('CcpPrintMovementFromMovementBtn').hide();
                                                    },
                                                    text: 'Stampa'
                                                }
                                            ]
                                        }
                                    ],
                                    selModel: Ext.create('Ext.selection.CheckboxModel', {
                                        checkOnly: true,
                                        listeners: {
                                            selectionchange: {
                                                fn: me.onCheckboxModelSelectionChange,
                                                scope: me
                                            }
                                        }
                                    }),
                                    columns: [
                                        {
                                            xtype: 'numbercolumn',
                                            hidden: true,
                                            width: 70,
                                            align: 'right',
                                            dataIndex: 'number',
                                            text: 'Numero',
                                            format: '0'
                                        },
                                        {
                                            xtype: 'actioncolumn',
                                            width: 20,
                                            items: [
                                                {
                                                    getClass: function(v, metadata, r, rowIndex, colIndex, store) {
                                                        if (r.get('locked')===true) return 'icon-lock';
                                                        else return'';
                                                    }
                                                }
                                            ]
                                        },
                                        {
                                            xtype: 'gridcolumn',
                                            renderer: function(value, metaData, record, rowIndex, colIndex, store, view) {
                                                if (value >  0) return '<a href="#">' + value + '</a>';
                                                else if(value<0) return '-';
                                                return value;
                                            },
                                            width: 65,
                                            align: 'right',
                                            dataIndex: 'invoice_number',
                                            text: 'Fattura'
                                        },
                                        {
                                            xtype: 'datecolumn',
                                            width: 80,
                                            align: 'center',
                                            dataIndex: 'expiration_date',
                                            text: 'Scadenza',
                                            format: 'd/m/Y'
                                        },
                                        {
                                            xtype: 'gridcolumn',
                                            renderer: function(value, metaData, record, rowIndex, colIndex, store, view) {
                                                if (record.get('description')) value+=' - '+record.get('description');

                                                return value;
                                            },
                                            dataIndex: 'type_text',
                                            text: 'Tipo movimento',
                                            flex: 1
                                        },
                                        {
                                            xtype: 'numbercolumn',
                                            width: 70,
                                            align: 'right',
                                            dataIndex: 'amount',
                                            text: 'Importo'
                                        },
                                        {
                                            xtype: 'gridcolumn',
                                            renderer: function(value, metaData, record, rowIndex, colIndex, store, view) {
                                                var color;
                                                if(record.get('incoming') === true) {
                                                    if(record.get('linked_additionals').length > 0) {
                                                        if(value<=0) color = 'green';
                                                        else color = 'red';

                                                        value = ' <font style="color:'+color+'">' + Ext.util.Format.number(value, '0,000.00') + '</font>';
                                                        return value;

                                                    }

                                                }
                                                return '';
                                            },
                                            width: 70,
                                            align: 'right',
                                            dataIndex: 'total_additionals',
                                            text: 'Sconto'
                                        },
                                        {
                                            xtype: 'gridcolumn',
                                            renderer: function(value, metaData, record, rowIndex, colIndex, store, view) {
                                                if(record.get('incoming') === true) {
                                                    return Ext.util.Format.number(value, '0,000.00');
                                                }
                                                return '';
                                            },
                                            width: 70,
                                            align: 'right',
                                            dataIndex: 'total',
                                            text: 'Credito'
                                        },
                                        {
                                            xtype: 'gridcolumn',
                                            renderer: function(value, metaData, record, rowIndex, colIndex, store, view) {
                                                if(record.get('incoming') === false) {
                                                    return Ext.util.Format.number(value, '0,000.00');
                                                }
                                                return '';
                                            },
                                            width: 70,
                                            align: 'right',
                                            dataIndex: 'total',
                                            text: 'Debito'
                                        },
                                        {
                                            xtype: 'numbercolumn',
                                            width: 70,
                                            align: 'right',
                                            dataIndex: 'total_payments',
                                            text: 'Pagati'
                                        },
                                        {
                                            xtype: 'gridcolumn',
                                            renderer: function(value, metaData, record, rowIndex, colIndex, store, view) {
                                                var value = Ext.util.Format.number(value, '0,000.00');
                                                if (record.get('expired')) {
                                                    return '<font style="color:red">'+value+'</font>';
                                                } else {
                                                    return value;
                                                }
                                            },
                                            width: 80,
                                            align: 'right',
                                            dataIndex: 'remaining',
                                            text: 'Da pagare'
                                        },
                                        {
                                            xtype: 'gridcolumn',
                                            dataIndex: 'covered_invoices',
                                            text: 'Copertura NC'
                                        },
                                        {
                                            xtype: 'gridcolumn',
                                            text: 'Ultimo pagamento',
                                            columns: [
                                                {
                                                    xtype: 'datecolumn',
                                                    width: 80,
                                                    align: 'center',
                                                    dataIndex: 'last_payment',
                                                    text: 'Data',
                                                    format: 'd/m/Y'
                                                },
                                                {
                                                    xtype: 'gridcolumn',
                                                    width: 200,
                                                    dataIndex: 'last_payment_method_text',
                                                    text: 'Metodo'
                                                }
                                            ]
                                        },
                                        {
                                            xtype: 'actioncolumn',
                                            items: [
                                                {
                                                    handler: function(view, rowIndex, colIndex, item, e, record, row) {
                                                        var s = Ext.getStore('CcpLinkedPayments');

                                                        if (record.get('count_payments') > 0) {
                                                            s.removeAll();
                                                            Ext.getCmp('CcpMovementsStudentGrid').setLoading(true);
                                                            s.load({
                                                                params: {
                                                                    linked: 'M',
                                                                    item: record.get('id')
                                                                },
                                                                callback: function(records, operation, success) {
                                                                    Ext.getCmp('CcpMovementsStudentGrid').setLoading(false);

                                                                    var i = 'agamenti';
                                                                    if (!record.get('incoming')) {
                                                                        i = 'relievi';
                                                                    }

                                                                    if (success) {
                                                                        Ext.widget('CcpLinkedPaymentsWin').show();
                                                                        Ext.getCmp('CcpLinkedPaymentsWin').linked = 'M';
                                                                        Ext.getCmp('CcpLinkedPaymentsWin').readOnly = false;
                                                                        if (record.get('number')) {
                                                                            Ext.getCmp('CcpLinkedPaymentsWin').setTitle('P' + i + ' abbinati al Movimento ' + record.get('number'));
                                                                        } else {
                                                                            Ext.getCmp('CcpLinkedPaymentsWin').setTitle('P' + i + ' abbinati al Movimento');
                                                                        }
                                                                        Ext.getCmp('CcpMovementsStudentGrid').getSelectionModel().select(record);
                                                                    } else {
                                                                        Ext.Msg.alert('Attenzione', 'Caricamento p' + i + ' abbinati al movimento fallito.');
                                                                    }
                                                                }
                                                            });
                                                        }
                                                    },
                                                    getClass: function(v, metadata, r, rowIndex, colIndex, store) {
                                                        if (r.get('count_payments') > 0) return 'icon-creditcards';
                                                    }
                                                }
                                            ]
                                        }
                                    ],
                                    viewConfig: {
                                        listeners: {
                                            itemcontextmenu: {
                                                fn: me.onViewItemContextMenu2,
                                                scope: me
                                            },
                                            cellclick: {
                                                fn: me.onViewCellClick,
                                                scope: me
                                            }
                                        }
                                    }
                                }
                            ],
                            dockedItems: [
                                {
                                    xtype: 'toolbar',
                                    flex: 1,
                                    dock: 'bottom',
                                    items: [
                                        {
                                            xtype: 'container',
                                            width: 300,
                                            layout: {
                                                type: 'vbox',
                                                align: 'stretch'
                                            },
                                            items: [
                                                {
                                                    xtype: 'textfield',
                                                    id: 'CcpStudentTotaImportTxt',
                                                    width: 200,
                                                    fieldLabel: 'Totale importi',
                                                    labelAlign: 'right',
                                                    labelWidth: 110
                                                },
                                                {
                                                    xtype: 'textfield',
                                                    id: 'CcpStudentTotaDiscountTxt',
                                                    width: 200,
                                                    fieldLabel: 'Totale sconti applicati',
                                                    labelAlign: 'right',
                                                    labelWidth: 110
                                                }
                                            ]
                                        },
                                        {
                                            xtype: 'tbspacer',
                                            flex: 1
                                        },
                                        {
                                            xtype: 'container',
                                            style: 'text-align:right',
                                            width: 300,
                                            layout: {
                                                type: 'vbox',
                                                align: 'stretch'
                                            },
                                            items: [
                                                {
                                                    xtype: 'label',
                                                    html: '<b>Totali</b>'
                                                },
                                                {
                                                    xtype: 'textfield',
                                                    id: 'CcpStTotalCredit',
                                                    itemId: 'CcpStTotalCredit',
                                                    fieldLabel: '<b>Crediti</b>',
                                                    labelWidth: 200,
                                                    listeners: {
                                                        change: {
                                                            fn: me.onCcpStTotalCreditChange,
                                                            scope: me
                                                        }
                                                    }
                                                },
                                                {
                                                    xtype: 'textfield',
                                                    id: 'CcpStTotalDebit',
                                                    fieldLabel: '<b>Debiti (compresi crediti studenti)</b>',
                                                    labelWidth: 200,
                                                    listeners: {
                                                        change: {
                                                            fn: me.onCcpStTotalDebitChange,
                                                            scope: me
                                                        }
                                                    }
                                                },
                                                {
                                                    xtype: 'textfield',
                                                    id: 'CcpStTotal',
                                                    fieldLabel: '<b>Totali</b>',
                                                    labelWidth: 200,
                                                    listeners: {
                                                        change: {
                                                            fn: me.onCcpStTotalChange,
                                                            scope: me
                                                        }
                                                    }
                                                }
                                            ]
                                        },
                                        {
                                            xtype: 'container',
                                            style: 'text-align:right',
                                            width: 100,
                                            layout: {
                                                type: 'vbox',
                                                align: 'stretch'
                                            },
                                            items: [
                                                {
                                                    xtype: 'label',
                                                    html: '<b>Pagati</b>'
                                                },
                                                {
                                                    xtype: 'textfield',
                                                    id: 'CcpStTotalCreditPayed',
                                                    fieldLabel: '',
                                                    listeners: {
                                                        change: {
                                                            fn: me.onCcpStTotalCreditPayedChange,
                                                            scope: me
                                                        }
                                                    }
                                                },
                                                {
                                                    xtype: 'textfield',
                                                    id: 'CcpStTotalDebitPayed',
                                                    fieldLabel: '',
                                                    listeners: {
                                                        change: {
                                                            fn: me.onCcpStTotalDebitPayedChange,
                                                            scope: me
                                                        }
                                                    }
                                                },
                                                {
                                                    xtype: 'textfield',
                                                    id: 'CcpStTotalPayed',
                                                    fieldLabel: '',
                                                    listeners: {
                                                        change: {
                                                            fn: me.onCcpStTotalPayedChange,
                                                            scope: me
                                                        }
                                                    }
                                                }
                                            ]
                                        },
                                        {
                                            xtype: 'container',
                                            style: 'text-align:right',
                                            width: 100,
                                            layout: {
                                                type: 'vbox',
                                                align: 'stretch'
                                            },
                                            items: [
                                                {
                                                    xtype: 'label',
                                                    html: '<b>Da pagare</b>',
                                                    text: ''
                                                },
                                                {
                                                    xtype: 'textfield',
                                                    id: 'CcpStTotalCreditToPay',
                                                    fieldLabel: '',
                                                    listeners: {
                                                        change: {
                                                            fn: me.onCcpStTotalCreditToPayChange,
                                                            scope: me
                                                        }
                                                    }
                                                },
                                                {
                                                    xtype: 'textfield',
                                                    id: 'CcpStTotalDebitToPay',
                                                    fieldLabel: '',
                                                    listeners: {
                                                        change: {
                                                            fn: me.onCcpStTotalDebitToPayChange,
                                                            scope: me
                                                        }
                                                    }
                                                },
                                                {
                                                    xtype: 'textfield',
                                                    id: 'CcpStTotalToPay',
                                                    fieldLabel: '',
                                                    listeners: {
                                                        change: {
                                                            fn: me.onCcpStTotalToPayChange,
                                                            scope: me
                                                        }
                                                    }
                                                }
                                            ]
                                        },
                                        {
                                            xtype: 'container',
                                            style: 'text-align:right',
                                            width: 100,
                                            layout: {
                                                type: 'vbox',
                                                align: 'stretch'
                                            },
                                            items: [
                                                {
                                                    xtype: 'label',
                                                    html: '<b>Scaduti</b>'
                                                },
                                                {
                                                    xtype: 'textfield',
                                                    id: 'CcpStTotalCreditExp',
                                                    fieldLabel: '',
                                                    listeners: {
                                                        change: {
                                                            fn: me.onCcpStTotalCreditExpChange,
                                                            scope: me
                                                        }
                                                    }
                                                },
                                                {
                                                    xtype: 'textfield',
                                                    id: 'CcpStTotalDebitExp',
                                                    fieldLabel: '',
                                                    listeners: {
                                                        change: {
                                                            fn: me.onCcpStTotalDebitExpChange,
                                                            scope: me
                                                        }
                                                    }
                                                },
                                                {
                                                    xtype: 'textfield',
                                                    id: 'CcpStTotalExp',
                                                    fieldLabel: '',
                                                    listeners: {
                                                        change: {
                                                            fn: me.onCcpStTotalExpChange,
                                                            scope: me
                                                        }
                                                    }
                                                }
                                            ]
                                        },
                                        {
                                            xtype: 'container',
                                            style: 'text-align:right',
                                            width: 100,
                                            layout: {
                                                type: 'vbox',
                                                align: 'stretch'
                                            },
                                            items: [
                                                {
                                                    xtype: 'label',
                                                    html: '<b>Non scaduti</b>'
                                                },
                                                {
                                                    xtype: 'textfield',
                                                    id: 'CcpStTotalCreditNotExp',
                                                    fieldLabel: '',
                                                    listeners: {
                                                        change: {
                                                            fn: me.onCcpStTotalCreditNotExpChange,
                                                            scope: me
                                                        }
                                                    }
                                                },
                                                {
                                                    xtype: 'textfield',
                                                    id: 'CcpStTotalDebitNotExp',
                                                    fieldLabel: '',
                                                    listeners: {
                                                        change: {
                                                            fn: me.onCcpStTotalDebitNotExpChange,
                                                            scope: me
                                                        }
                                                    }
                                                },
                                                {
                                                    xtype: 'textfield',
                                                    id: 'CcpStTotalNotExp',
                                                    fieldLabel: '',
                                                    listeners: {
                                                        change: {
                                                            fn: me.onCcpStTotalNotExpChange,
                                                            scope: me
                                                        }
                                                    }
                                                }
                                            ]
                                        }
                                    ]
                                },
                                {
                                    xtype: 'toolbar',
                                    dock: 'bottom',
                                    items: [
                                        {
                                            xtype: 'tbspacer',
                                            flex: 1
                                        },
                                        {
                                            xtype: 'label',
                                            html: '',
                                            id: 'CcpStudentMovementLbl'
                                        },
                                        {
                                            xtype: 'tbspacer',
                                            flex: 1
                                        }
                                    ]
                                }
                            ]
                        },
                        {
                            xtype: 'panel',
                            checkYearsStudentExists: function(parent) {
                                var studentData = Ext.getCmp('CcpStudentsAnagraficaFrm').getValues();

                                Ext.getCmp('CcpStudentsTab').setLoading(true);

                                Ext.Ajax.request({
                                    method: 'POST',
                                    url: '/mc2-api/ccp/get_mc_db_list/students/' + studentData.id_studente,
                                    success: function(r) {
                                        var res = Ext.decode(r.responseText);
                                        if(res.success===true) {
                                            var years = res.results;
                                            Ext.widget('CcpYearsToSaveWin').show();
                                            if(parent===true) {
                                                Ext.getCmp('CcpYearsToSaveFrm').getForm().url = '/mc2-api/ccp/parent_save';
                                            }
                                            Ext.getCmp('CcpYearsToSaveWin').createCheckBox(years);
                                        } else {
                                            Ext.Msg.alert('ERRORE', res.message);
                                        }
                                    },
                                    callback: function() {
                                        Ext.getCmp('CcpStudentsTab').setLoading(false);
                                    }
                                });
                            },
                            id: 'CcpAnagraficaPnl',
                            itemId: 'CcpAnagraficaPnl',
                            title: 'Anagrafica',
                            layout: {
                                type: 'vbox',
                                align: 'stretch'
                            },
                            dockedItems: [
                                {
                                    xtype: 'toolbar',
                                    flex: 1,
                                    dock: 'top',
                                    items: [
                                        {
                                            xtype: 'button',
                                            handler: function(button, e) {
                                                Ext.getCmp('CcpAnagraficaPnl').checkYearsStudentExists();
                                            },
                                            iconCls: 'icon-disk',
                                            text: 'Salva'
                                        }
                                    ]
                                }
                            ],
                            items: [
                                {
                                    xtype: 'form',
                                    flex: 1,
                                    id: 'CcpStudentsAnagraficaFrm',
                                    itemId: 'CcpStudentsAnagraficaFrm',
                                    bodyPadding: 10,
                                    title: '',
                                    layout: {
                                        type: 'vbox',
                                        align: 'stretch'
                                    },
                                    items: [
                                        {
                                            xtype: 'container',
                                            layout: {
                                                type: 'hbox',
                                                align: 'stretch'
                                            },
                                            items: [
                                                {
                                                    xtype: 'container',
                                                    flex: 1,
                                                    minWidth: 220,
                                                    style: 'text-align:center',
                                                    items: [
                                                        {
                                                            xtype: 'image',
                                                            id: 'CcpStudentImg',
                                                            itemId: 'CcpStudentImg',
                                                            minHeight: 200,
                                                            style: 'text-align:center'
                                                        }
                                                    ]
                                                },
                                                {
                                                    xtype: 'container',
                                                    flex: 5,
                                                    padding: 5,
                                                    items: [
                                                        {
                                                            xtype: 'container',
                                                            layout: 'table',
                                                            items: [
                                                                {
                                                                    xtype: 'textfield',
                                                                    fieldLabel: 'Cognome',
                                                                    name: 'cognome',
                                                                    readOnly: true
                                                                },
                                                                {
                                                                    xtype: 'textfield',
                                                                    margin: '0 0 5 5',
                                                                    fieldLabel: 'Nome',
                                                                    labelWidth: 50,
                                                                    name: 'nome',
                                                                    readOnly: true
                                                                }
                                                            ]
                                                        },
                                                        {
                                                            xtype: 'container',
                                                            layout: 'table',
                                                            items: [
                                                                {
                                                                    xtype: 'textfield',
                                                                    fieldLabel: 'Codice fiscale',
                                                                    name: 'codice_fiscale',
                                                                    readOnly: true
                                                                },
                                                                {
                                                                    xtype: 'textfield',
                                                                    margin: '0 0 5 5 ',
                                                                    fieldLabel: 'Matricola',
                                                                    labelWidth: 50,
                                                                    name: 'matricola',
                                                                    readOnly: true
                                                                }
                                                            ]
                                                        },
                                                        {
                                                            xtype: 'container',
                                                            layout: 'table',
                                                            items: [
                                                                {
                                                                    xtype: 'textfield',
                                                                    fieldLabel: 'Nato a',
                                                                    name: 'descrizione_nascita',
                                                                    readOnly: true
                                                                },
                                                                {
                                                                    xtype: 'textfield',
                                                                    margin: '0 0 5 5',
                                                                    fieldLabel: 'il',
                                                                    labelWidth: 50,
                                                                    name: 'data_nascita',
                                                                    readOnly: true
                                                                }
                                                            ]
                                                        },
                                                        {
                                                            xtype: 'hiddenfield',
                                                            fieldLabel: 'Label',
                                                            name: 'id_studente'
                                                        },
                                                        {
                                                            xtype: 'container',
                                                            layout: 'table',
                                                            items: [
                                                                {
                                                                    xtype: 'textfield',
                                                                    width: 255,
                                                                    fieldLabel: 'Residente in via',
                                                                    name: 'indirizzo',
                                                                    readOnly: true
                                                                },
                                                                {
                                                                    xtype: 'textfield',
                                                                    margin: '0 0 5 5',
                                                                    fieldLabel: 'a',
                                                                    labelWidth: 50,
                                                                    name: 'descrizione_residenza',
                                                                    readOnly: true
                                                                },
                                                                {
                                                                    xtype: 'textfield',
                                                                    margin: '0 0 5 5',
                                                                    width: 150,
                                                                    fieldLabel: 'CAP',
                                                                    labelWidth: 50,
                                                                    name: 'cap_residenza',
                                                                    readOnly: true
                                                                }
                                                            ]
                                                        },
                                                        {
                                                            xtype: 'container',
                                                            layout: 'table',
                                                            items: [
                                                                {
                                                                    xtype: 'textfield',
                                                                    fieldLabel: 'Classe',
                                                                    name: 'classe',
                                                                    readOnly: true
                                                                },
                                                                {
                                                                    xtype: 'textfield',
                                                                    margin: '0 0 5 5',
                                                                    width: 360,
                                                                    fieldLabel: 'Indirizzo',
                                                                    labelWidth: 50,
                                                                    name: 'descrizione_indirizzi',
                                                                    readOnly: true
                                                                }
                                                            ]
                                                        },
                                                        {
                                                            xtype: 'container',
                                                            layout: 'table',
                                                            items: [
                                                                {
                                                                    xtype: 'textfield',
                                                                    fieldLabel: '<b>N. tesserino</b>',
                                                                    name: 'badge',
                                                                    enableKeyEvents: true,
                                                                    listeners: {
                                                                        keyup: {
                                                                            fn: me.onTextfieldChange2,
                                                                            scope: me
                                                                        }
                                                                    }
                                                                },
                                                                {
                                                                    xtype: 'combobox',
                                                                    margin: '0 0 0 10',
                                                                    fieldLabel: '<b>Stato studente</b>',
                                                                    name: 'stato_studente_personalizzato',
                                                                    displayField: 'descrizione',
                                                                    queryMode: 'local',
                                                                    store: 'StudentStates',
                                                                    valueField: 'id_stato_studente_personalizzato'
                                                                },
                                                                {
                                                                    xtype: 'checkboxfield',
                                                                    margin: '0 0 0 10',
                                                                    fieldLabel: '<b>Fratelli</b>',
                                                                    name: 'fratelli',
                                                                    boxLabel: '',
                                                                    inputValue: 'SI',
                                                                    uncheckedValue: 'NO'
                                                                }
                                                            ]
                                                        },
                                                        {
                                                            xtype: 'container',
                                                            items: [
                                                                {
                                                                    xtype: 'numberfield',
                                                                    fieldLabel: '<b>Numero entrate senza tesserino</b>',
                                                                    labelWidth: 200,
                                                                    name: 'numero_entrate_senza_tesserino',
                                                                    hideTrigger: true
                                                                }
                                                            ]
                                                        }
                                                    ]
                                                }
                                            ]
                                        },
                                        {
                                            xtype: 'hiddenfield',
                                            flex: 1,
                                            fieldLabel: 'Label',
                                            name: 'subject_school_year'
                                        },
                                        {
                                            xtype: 'container',
                                            flex: 1,
                                            items: [
                                                {
                                                    xtype: 'textareafield',
                                                    width: 500,
                                                    resizable: true,
                                                    fieldLabel: 'Note amministrative',
                                                    labelWidth: 230,
                                                    name: 'annotazioni_amministrative'
                                                }
                                            ]
                                        }
                                    ]
                                }
                            ]
                        },
                        {
                            xtype: 'panel',
                            id: 'CcpParentsPnl',
                            itemId: 'CcpParentsPnl',
                            autoScroll: true,
                            layout: 'fit',
                            title: 'Parenti',
                            items: [
                                {
                                    xtype: 'form',
                                    id: 'CcpParentFrm',
                                    itemId: 'CcpParentFrm',
                                    title: 'Parenti',
                                    layout: {
                                        type: 'vbox',
                                        align: 'stretch'
                                    },
                                    items: [
                                        {
                                            xtype: 'panel',
                                            generateParentPnl: function(parents) {
                                                var parentPnl = Ext.getCmp('CcpStudentParentsPnl'),
                                                    fieldset,
                                                    idPagante = null;

                                                parentPnl.removeAll();
                                                Ext.each(parents, function(p, i) {
                                                    idPagante = p.pagante == 't' ? p.id_parente: false;
                                                    fieldset = new Ext.form.FieldSet({
                                                        layout: {
                                                            type: 'vbox',
                                                            align: 'stretch'
                                                        },
                                                        title: p.cognome +' ' +p.nome,
                                                        items: [
                                                        {
                                                            xtype: 'textfield',
                                                            fieldLabel: 'Cognome',
                                                            value: p.cognome,
                                                            readOnly: true,
                                                            name: p.id_parente + '-cognome'
                                                        },
                                                        {
                                                            xtype: 'textfield',
                                                            fieldLabel: 'Nome',
                                                            value: p.nome,
                                                            readOnly: true,
                                                            name: p.id_parente + '-nome'
                                                        },
                                                        {
                                                            xtype: 'textfield',
                                                            fieldLabel: 'Codice fiscale',
                                                            value: p.codice_fiscale,
                                                            readOnly: true,
                                                            name: p.id_parente + '-codice_fiscale'
                                                        },
                                                        {
                                                            xtype: 'textfield',
                                                            fieldLabel: 'Residente in via',
                                                            value: p.indirizzo,
                                                            readOnly: true,
                                                            name: p.id_parente + '-indirizzo'
                                                        },
                                                        {
                                                            xtype: 'textfield',
                                                            fieldLabel: 'Città',
                                                            value: p.descrizione,
                                                            readOnly: true,
                                                            name: p.id_parente + '-descrizione'
                                                        },
                                                        {
                                                            xtype: 'textfield',
                                                            fieldLabel: 'CAP',
                                                            value: p.cap_residenza,
                                                            readOnly: true,
                                                            name: p.id_parente + '-cap_residenza'
                                                        },
                                                        {
                                                            xtype: 'textfield',
                                                            fieldLabel: 'Email',
                                                            value: p.email,
                                                            readOnly: true,
                                                            name: p.id_parente + '-email'
                                                        },
                                                        {
                                                            xtype: 'textfield',
                                                            fieldLabel: 'Tel. cellulare',
                                                            value: p.telefono_cellulare,
                                                            readOnly: true,
                                                            name: p.id_parente + '-telefono_cellulare'
                                                        },
                                                        {
                                                            xtype: 'textfield',
                                                            fieldLabel: 'Tel. abitazione',
                                                            value: p.telefono_abitazione,
                                                            readOnly: true,
                                                            name: p.id_parente + '-telefono_abitazione'
                                                        },
                                                        {
                                                            xtype: 'textfield',
                                                            fieldLabel: '<b>IBAN</b>',
                                                            value: p.iban,
                                                            name: p.id_parente + '-iban',
                                                        },
                                                        {
                                                            xtype: 'checkbox',
                                                            fieldLabel: '<b>RID</b>',
                                                            value: p.rid,
                                                            inputValue: 'SI',
                                                            uncheckedValue: 'NO',
                                                            name: p.id_parente + '-rid',
                                                        },
                                                        {
                                                            xtype: 'container',
                                                            layout: {
                                                                type: 'hbox',
                                                                //align: 'stretch'
                                                            },
                                                            items: [
                                                            {
                                                                xtype: 'textfield',
                                                                fieldLabel: '<b>Riferimento mandato</b>',
                                                                value: p.codice_rid,
                                                                name: p.id_parente + '-codice_rid',
                                                                flex: 1,
                                                                id: 'CcpParent' + p.id_parente + 'RidCode'
                                                            },
                                                            {
                                                                xtype: 'button',
                                                                text: '',
                                                                iconCls: 'icon-arrow_refresh',
                                                                margin: '5 0 0 5',
                                                                flex:0,
                                                                handler: function() {
                                                                    Ext.Ajax.request({
                                                                        url: '/mc2-api/ccp/generate_parent_rid?subject_id=1223',
                                                                        success: function(res) {
                                                                            var r = Ext.decode(res.responseText);
                                                                            if(r.success) {
                                                                                Ext.getCmp('CcpParent' + p.id_parente + 'RidCode').setValue(r.result);
                                                                            }
                                                                        }
                                                                    });

                                                                }
                                                            }
                                                            ]
                                                        },
                                                        {
                                                            xtype: 'datefield',
                                                            fieldLabel: '<b>Data mandato RID</b>',
                                                            format: 'd/m/Y',
                                                            submitFormat: 'Y-m-d',
                                                            value: p.data_mandato_rid,// === 't' ? 'on' : null,
                                                            name: p.id_parente + '-data_mandato_rid',
                                                        },
                                                        {
                                                            xtype: 'checkbox',
                                                            fieldLabel: '<b>Primo invio SEPA</b>',
                                                            value: p.first_sepa,
                                                            inputValue: 'SI',
                                                            uncheckedValue: 'NO',
                                                            name: p.id_parente + '-first_sepa',
                                                            readOnly: mc2ui.app.settings.sepaUpdateInfo,
                                                            disabled: mc2ui.app.settings.sepaUpdateInfo
                                                        },
                                                        {
                                                            xtype: 'combobox',
                                                            fieldLabel: '<b>Tipo di addebito</b>',
                                                            store: 'CcpPaymentMethods',
                                                            id: 'CcpAddebitoCmb' + p.id_parente,
                                                            displayField: 'name',
                                                            valueField: 'id',
                                                            queryMode: 'local',
                                                            value: p.tipo_addebito,
                                                            name: p.id_parente + '-tipo_addebito',
                                                        },
                                                        {
                                                            xtype: 'textfield',
                                                            fieldLabel: '<b>Intestazione conto corrente</b>',
                                                            value: p.intestazione_cc,
                                                            name: p.id_parente + '-intestazione_cc',
                                                        },
                                                        {
                                                            xtype: 'textfield',
                                                            fieldLabel: '<b>Indirizzo conto corrente</b>',
                                                            value: p.indirizzo_cc,
                                                            name: p.id_parente + '-indirizzo_cc',
                                                        },
                                                        {
                                                            xtype: 'radio',
                                                            fieldLabel: '<b>Pagante</b>',
                                                            value: p.pagante === 't' ? p.id_parente : null,
                                                            inputValue: p.id_parente,
                                                            name: 'pagante'
                                                        },
                                                        {
                                                            xtype: 'checkbox',
                                                            fieldLabel: '<b>Opposizione invio AdE</b>',
                                                            value: p.opposizione_ade,
                                                            inputValue: 'SI',
                                                            uncheckedValue: 'NO',
                                                            name: p.id_parente + '-opposizione_ade',
                                                        },
                                                        {
                                                            xtype: 'datefield',
                                                            fieldLabel: '<b>Data opposizione AdE</b>',
                                                            format: 'd/m/Y',
                                                            submitFormat: 'Y-m-d',
                                                            value: p.data_opposizione_ade,// === 't' ? 'on' : null,
                                                            name: p.id_parente + '-data_opposizione_ade',
                                                        }
                                                        ]
                                                    });
                                                    parentPnl.add(fieldset);
                                                    //Ext.getCmp('CcpAddebitoCmb' + p.id_parente).setValue(p.tipo_addebito);

                                                });


                                            },
                                            flex: 1,
                                            id: 'CcpStudentParentsPnl',
                                            itemId: 'CcpStudentParentsPnl',
                                            autoScroll: true,
                                            bodyPadding: 10,
                                            collapseFirst: false,
                                            collapsed: false,
                                            layout: {
                                                type: 'vbox',
                                                align: 'stretch',
                                                padding: 5
                                            }
                                        }
                                    ]
                                }
                            ],
                            dockedItems: [
                                {
                                    xtype: 'toolbar',
                                    dock: 'top',
                                    items: [
                                        {
                                            xtype: 'button',
                                            handler: function(button, e) {
                                                Ext.getCmp('CcpAnagraficaPnl').checkYearsStudentExists(true);
                                            },
                                            iconCls: 'icon-disk',
                                            text: 'Salva'
                                        }
                                    ]
                                }
                            ]
                        },
                        {
                            xtype: 'panel',
                            layout: 'fit',
                            title: 'Servizi',
                            items: [
                                {
                                    xtype: 'gridpanel',
                                    id: 'CcpStudentMovementsServiziGrd',
                                    title: '',
                                    store: 'CcpStudentMarkets',
                                    columns: [
                                        {
                                            xtype: 'gridcolumn',
                                            width: 174,
                                            dataIndex: 'tipo',
                                            text: 'Tipo'
                                        },
                                        {
                                            xtype: 'gridcolumn',
                                            dataIndex: 'descrizione',
                                            text: 'Descrizione',
                                            flex: 1,
                                            editor: {
                                                xtype: 'combobox',
                                                id: 'CcpStudentMarketCmb',
                                                name: 'id_marketplace',
                                                displayField: 'descrizione',
                                                forceSelection: true,
                                                queryMode: 'local',
                                                store: 'CcpServizi',
                                                valueField: 'id_marketplace',
                                                listeners: {
                                                    select: {
                                                        fn: me.onCcpStudentMarketCmbSelect,
                                                        scope: me
                                                    }
                                                }
                                            }
                                        },
                                        {
                                            xtype: 'datecolumn',
                                            align: 'center',
                                            dataIndex: 'data_inserimento',
                                            text: 'Data inserimento',
                                            format: 'd/m/Y',
                                            editor: {
                                                xtype: 'datefield',
                                                format: 'd/m/Y',
                                                submitFormat: 'c'
                                            }
                                        },
                                        {
                                            xtype: 'gridcolumn',
                                            align: 'center',
                                            dataIndex: 'contabilizzato',
                                            text: 'Contabilizzato',
                                            editor: {
                                                xtype: 'combobox',
                                                value: 'NO',
                                                editable: false,
                                                forceSelection: true,
                                                store: [
                                                    'SI',
                                                    'NO'
                                                ]
                                            }
                                        },
                                        {
                                            xtype: 'datecolumn',
                                            align: 'center',
                                            dataIndex: 'validita_inizio',
                                            text: 'Inizio validità',
                                            format: 'd/m/Y',
                                            editor: {
                                                xtype: 'datefield',
                                                format: 'd/m/Y',
                                                submitFormat: 'c'
                                            }
                                        },
                                        {
                                            xtype: 'datecolumn',
                                            align: 'center',
                                            dataIndex: 'validita_fine',
                                            text: 'Fine validità',
                                            format: 'd/m/Y',
                                            editor: {
                                                xtype: 'datefield',
                                                format: 'd/m/Y',
                                                submitFormat: 'c'
                                            }
                                        },
                                        {
                                            xtype: 'actioncolumn',
                                            align: 'center',
                                            text: 'Elimina',
                                            items: [
                                                {
                                                    handler: function(view, rowIndex, colIndex, item, e, record, row) {
                                                        var student = Ext.getCmp('CcpStudentsGrd').getSelectionModel().getSelection()[0];
                                                        Ext.getCmp('CcpStudentMovementsServiziGrd').setLoading(true);
                                                        Ext.Ajax.request({
                                                            url: '/mc2-api/ccp/marketplace/purchase/delete',
                                                            method:'DELETE',
                                                            jsonData: {
                                                                id_acquisto: record.get('id_acquisto'),
                                                                subject_school_year: record.get('subject_school_year')
                                                            },
                                                            success: function(r) {
                                                                var res = Ext.decode(r.responseText);
                                                                if(res.success === true) {
                                                                    Ext.getCmp('CcpStudentMovementsServiziGrd').setLoading(false);
                                                                    Ext.getCmp('CcpStudentsGrd').filterByStudent(student.get('db_id'));
                                                                }
                                                            }
                                                        });
                                                    },
                                                    iconCls: 'icon-cancel'
                                                }
                                            ]
                                        }
                                    ],
                                    dockedItems: [
                                        {
                                            xtype: 'toolbar',
                                            dock: 'top',
                                            items: [
                                                {
                                                    xtype: 'button',
                                                    handler: function(button, e) {
                                                        var student = Ext.getCmp('CcpStudentsGrd').getSelectionModel().getSelection()[0];

                                                        Ext.getStore('CcpStudentMarkets').add({
                                                            contabilizzato: 'NO',
                                                            validita_inizio: new Date(),
                                                            validita_fine: new Date(),
                                                            subject_school_address: student.get('subject_school_address')
                                                        });
                                                        Ext.getCmp('CcpStudentMovementsServiziGrd').editingPlugin.startEdit(Ext.getStore('CcpStudentMarkets').getRange().length-1);
                                                        Ext.getCmp('CcpStudentMarketCmb').expand();
                                                    },
                                                    iconCls: 'icon-add',
                                                    text: 'Nuovo'
                                                }
                                            ]
                                        }
                                    ],
                                    plugins: [
                                        Ext.create('Ext.grid.plugin.RowEditing', {
                                            errorSummary: false,
                                            listeners: {
                                                canceledit: {
                                                    fn: me.onRowEditingCanceledit1,
                                                    scope: me
                                                },
                                                edit: {
                                                    fn: me.onRowEditingEdit2,
                                                    scope: me
                                                },
                                                beforeedit: {
                                                    fn: me.onRowEditingBeforeEdit1,
                                                    scope: me
                                                }
                                            }
                                        })
                                    ]
                                }
                            ]
                        },
                        {
                            xtype: 'panel',
                            title: 'Mensa',
                            layout: {
                                type: 'vbox',
                                align: 'stretch'
                            },
                            items: [
                                {
                                    xtype: 'gridpanel',
                                    flex: 1,
                                    id: 'CcpStudentMovementsMensaGrd',
                                    title: '',
                                    store: 'CcpStudentMovementsMensa',
                                    dockedItems: [
                                        {
                                            xtype: 'toolbar',
                                            dock: 'top',
                                            items: [
                                                {
                                                    xtype: 'button',
                                                    handler: function(button, e) {
                                                        var student = Ext.getCmp('CcpStudentsGrd').getSelectionModel().getSelection()[0];
                                                        Ext.getStore('CcpStudentMovementsMensa').add({
                                                            creation_date: new Date()
                                                        });
                                                        Ext.getCmp('CcpStudentMovementsMensaGrd').editingPlugin.startEdit(Ext.getStore('CcpStudentMovementsMensa').getRange().length-1);
                                                    },
                                                    iconCls: 'icon-add',
                                                    text: 'Nuovo'
                                                }
                                            ]
                                        }
                                    ],
                                    columns: [
                                        {
                                            xtype: 'gridcolumn',
                                            dataIndex: 'type_text',
                                            text: 'Tipo movimento',
                                            flex: 1,
                                            editor: {
                                                xtype: 'combobox',
                                                name: 'type_id',
                                                allowBlank: false,
                                                editable: false,
                                                displayField: 'name',
                                                forceSelection: true,
                                                queryMode: 'local',
                                                store: 'CcpTypes',
                                                valueField: 'id',
                                                listeners: {
                                                    expand: {
                                                        fn: me.onComboboxExpand,
                                                        scope: me
                                                    }
                                                }
                                            }
                                        },
                                        {
                                            xtype: 'numbercolumn',
                                            dataIndex: 'total',
                                            text: 'Totale',
                                            editor: {
                                                xtype: 'numberfield',
                                                hideTrigger: true
                                            }
                                        },
                                        {
                                            xtype: 'numbercolumn',
                                            dataIndex: 'total_payments',
                                            text: 'Pagati'
                                        },
                                        {
                                            xtype: 'datecolumn',
                                            align: 'center',
                                            dataIndex: 'creation_date',
                                            text: 'Data',
                                            format: 'd-m-Y',
                                            editor: {
                                                xtype: 'datefield',
                                                name: 'creation_date',
                                                format: 'd-m-Y',
                                                submitFormat: 'c'
                                            }
                                        },
                                        {
                                            xtype: 'gridcolumn',
                                            align: 'center',
                                            dataIndex: 'school_year',
                                            text: 'Anno scolastico'
                                        },
                                        {
                                            xtype: 'actioncolumn',
                                            width: 35,
                                            align: 'center',
                                            items: [
                                                {
                                                    handler: function(view, rowIndex, colIndex, item, e, record, row) {
                                                        if(record.get('linked_receipts').length === 0) {
                                                            Ext.Msg.confirm('INFORMAZIONI',
                                                            'Sto per creare la ricevuta. Procedere?',
                                                            function(btn) {
                                                                if(btn=='yes') {
                                                                    Ext.Ajax.request({
                                                                        url: '/mc2-api/ccp/receipt',
                                                                        method: 'POST',
                                                                        jsonData: {
                                                                            number: null,
                                                                            date: null,
                                                                            linked_payments: Ext.encode(record.get('linked_payments'))
                                                                        },
                                                                        success: function(r) {
                                                                            var receipt = Ext.decode(r.responseText).results;
                                                                            Ext.getCmp('CcpStudentsGrd').filterByStudent(record.get('subject_id'));
                                                                            Ext.Ajax.request({
                                                                                url: '/mc2-api/core/print',
                                                                                params: {
                                                                                    newSpool: 1,
                                                                                    print: mc2ui.app.settings.prints.receipt,
                                                                                    namespace: 'CCP',
                                                                                    type: 'PDF',
                                                                                    mime: 'application/pdf',
                                                                                    receipt_id: receipt.id,
                                                                                    number: receipt.number,
                                                                                    date: receipt.date
                                                                                },
                                                                                success: function(response, opts) {
                                                                                    var res = Ext.decode(response.responseText);
                                                                                    mc2ui.app.showNotifyPrint(res);
                                                                                }
                                                                            });
                                                                        }
                                                                    });
                                                                }
                                                            });

                                                        } else {
                                                            Ext.each(record.get('linked_receipts'), function(receipt){
                                                                Ext.Ajax.request({
                                                                    url: '/mc2-api/core/print',
                                                                    params: {
                                                                        newSpool: 1,
                                                                        print: mc2ui.app.settings.prints.receipt,
                                                                        namespace: 'CCP',
                                                                        type: 'PDF',
                                                                        mime: 'application/pdf',
                                                                        receipt_id: receipt.id,
                                                                        number: receipt.number,
                                                                        date: receipt.date
                                                                    },
                                                                    success: function(response, opts) {
                                                                        var res = Ext.decode(response.responseText);
                                                                        mc2ui.app.showNotifyPrint(res);
                                                                    }
                                                                });
                                                            });
                                                        }
                                                    },
                                                    getClass: function(v, metadata, r, rowIndex, colIndex, store) {
                                                        if(!r.get('last_payment')) {
                                                            return '';
                                                        }
                                                        return 'icon-page';

                                                    },
                                                    iconCls: 'icon-page',
                                                    tooltip: 'Stamp/Emetti ricevuta'
                                                }
                                            ]
                                        }
                                    ],
                                    plugins: [
                                        Ext.create('Ext.grid.plugin.RowEditing', {
                                            listeners: {
                                                edit: {
                                                    fn: me.onRowEditingEdit1,
                                                    scope: me
                                                },
                                                canceledit: {
                                                    fn: me.onRowEditingCanceledit,
                                                    scope: me
                                                },
                                                beforeedit: {
                                                    fn: me.onRowEditingBeforeEdit,
                                                    scope: me
                                                }
                                            }
                                        })
                                    ]
                                },
                                {
                                    xtype: 'container',
                                    padding: 10,
                                    items: [
                                        {
                                            xtype: 'label',
                                            id: 'CcpMensaTotalsMovementsLbl'
                                        }
                                    ]
                                },
                                {
                                    xtype: 'container',
                                    flex: 2,
                                    layout: {
                                        type: 'hbox',
                                        padding: 5
                                    },
                                    items: [
                                        {
                                            xtype: 'gridpanel',
                                            flex: 1,
                                            title: 'Pasti consumati',
                                            store: 'CcpPastiConsumati',
                                            columns: [
                                                {
                                                    xtype: 'gridcolumn',
                                                    dataIndex: 'descrizione',
                                                    text: 'Menu',
                                                    flex: 1
                                                },
                                                {
                                                    xtype: 'numbercolumn',
                                                    width: 50,
                                                    align: 'center',
                                                    dataIndex: 'numero_pasti',
                                                    text: 'Num.',
                                                    format: '0'
                                                },
                                                {
                                                    xtype: 'numbercolumn',
                                                    width: 70,
                                                    align: 'center',
                                                    dataIndex: 'costo_unitario',
                                                    text: 'Costo unit.'
                                                },
                                                {
                                                    xtype: 'numbercolumn',
                                                    width: 70,
                                                    align: 'center',
                                                    dataIndex: 'costo_totale',
                                                    text: 'Totale'
                                                }
                                            ]
                                        },
                                        {
                                            xtype: 'container',
                                            flex: 1,
                                            layout: {
                                                type: 'vbox',
                                                align: 'center',
                                                pack: 'center'
                                            },
                                            items: [
                                                {
                                                    xtype: 'label',
                                                    flex: 1,
                                                    html: '<b>RIEPILOGO</b>',
                                                    margin: '0 0 10 0'
                                                },
                                                {
                                                    xtype: 'label',
                                                    flex: 1,
                                                    id: 'CcpMensaCredRimLbl'
                                                },
                                                {
                                                    xtype: 'label',
                                                    flex: 1,
                                                    id: 'CcpMensaDaPagLbl'
                                                }
                                            ]
                                        },
                                        {
                                            xtype: 'gridpanel',
                                            flex: 1,
                                            title: 'Possibili pasti rimasti',
                                            store: 'CcpPastiRim',
                                            columns: [
                                                {
                                                    xtype: 'gridcolumn',
                                                    dataIndex: 'descrizione',
                                                    text: 'Menu',
                                                    flex: 1
                                                },
                                                {
                                                    xtype: 'numbercolumn',
                                                    width: 50,
                                                    align: 'center',
                                                    dataIndex: 'numero_pasti',
                                                    text: 'Num.',
                                                    format: '0'
                                                },
                                                {
                                                    xtype: 'numbercolumn',
                                                    width: 70,
                                                    align: 'center',
                                                    dataIndex: 'costo_unitario',
                                                    text: 'Costo unit.'
                                                },
                                                {
                                                    xtype: 'numbercolumn',
                                                    width: 70,
                                                    align: 'right',
                                                    dataIndex: 'costo_totale',
                                                    text: 'Totale'
                                                }
                                            ]
                                        }
                                    ]
                                }
                            ]
                        },
                        {
                            xtype: 'panel',
                            layout: 'fit',
                            title: 'Ricevute',
                            items: [
                                {
                                    xtype: 'gridpanel',
                                    id: 'CcpStudentReceiptsGrid',
                                    title: '',
                                    store: 'CcpStudentReceipts',
                                    columns: [
                                        {
                                            xtype: 'gridcolumn',
                                            align: 'right',
                                            dataIndex: 'number',
                                            text: 'Numero',
                                            renderer: function(value, metaData, record, rowIndex, colIndex, store, view) {
                                                var result = value;
                                                if(record.get('suffix')) result += '/' + record.get('suffix');
                                                return result;
                                            },
                                        },
                                        {
                                            xtype: 'datecolumn',
                                            align: 'center',
                                            dataIndex: 'date',
                                            text: 'Data emissione',
                                            format: 'd/m/Y'
                                        },
                                        {
                                            xtype: 'gridcolumn',
                                            dataIndex: 'payers',
                                            text: 'Paganti',
                                            flex: 1
                                        },
                                        {
                                            xtype: 'gridcolumn',
                                            renderer: function(value, metaData, record, rowIndex, colIndex, store, view) {
                                                if (value===true) {
                                                    return 'Ricevuta';
                                                } else {
                                                    return 'Attestazione';
                                                }
                                            },
                                            align: 'center',
                                            dataIndex: 'receipt',
                                            text: 'Tipo'
                                        },
                                        {
                                            xtype: 'numbercolumn',
                                            align: 'right',
                                            dataIndex: 'total',
                                            text: 'Importo'
                                        }
                                    ],
                                    viewConfig: {
                                        listeners: {
                                            itemcontextmenu: {
                                                fn: me.onViewItemContextMenu,
                                                scope: me
                                            }
                                        }
                                    },
                                    dockedItems: [
                                        {
                                            xtype: 'toolbar',
                                            dock: 'top',
                                            items: [
                                                {
                                                    xtype: 'tbspacer',
                                                    flex: 1
                                                },
                                                {
                                                    xtype: 'button',
                                                    handler: function(button, e) {
                                                        Ext.widget('CcpAttestazionePrintWin').show();
                                                    },
                                                    text: 'Stampa dichiarazione'
                                                }
                                            ]
                                        }
                                    ]
                                },
                                {
                                    xtype: 'menu',
                                    id: 'CcpStudentsReceiptMn',
                                    width: 120,
                                    items: [
                                        {
                                            xtype: 'menuitem',
                                            handler: function(item, e) {
                                                var r = Ext.getCmp('CcpStudentReceiptsGrid').getSelectionModel().getSelection()[0],
                                                    print = r.get('receipt') === true ? mc2ui.app.settings.prints.receipt : mc2ui.app.settings.prints.attestation;

                                                Ext.Ajax.request({
                                                    url: '/mc2-api/core/print',
                                                    params: {
                                                        newSpool: 1,
                                                        print: print,
                                                        namespace: 'CCP',
                                                        type: 'PDF',
                                                        mime: 'application/pdf',
                                                        receipt_id: r.get('id'),
                                                        number: r.get('number'),
                                                        date: r.get('date')
                                                    },
                                                    success: function(response, opts) {
                                                        var res = Ext.decode(response.responseText);
                                                        mc2ui.app.showNotifyPrint(res);
                                                    }
                                                });
                                            },
                                            iconCls: 'icon-printer',
                                            text: 'Stampa'
                                        }
                                    ]
                                }
                            ]
                        },
                        {
                            xtype: 'panel',
                            layout: 'fit',
                            title: 'Pagamenti',
                            items: [
                                {
                                    xtype: 'gridpanel',
                                    id: 'CcpStudentPaymentsGrd',
                                    title: '',
                                    store: 'CcpStudentsPayments',
                                    columns: [
                                        {
                                            xtype: 'actioncolumn',
                                            width: 50,
                                            defaultWidth: 50,
                                            align: 'center',
                                            items: [
                                                {
                                                    getClass: function(v, metadata, r, rowIndex, colIndex, store) {
                                                        if (r.get('receipt_id')) {
                                                            return 'icon-page';
                                                        }
                                                    },
                                                    getTip: function(v, metadata, r) {
                                                        if (r.get('receipt_id')) {
                                                            return 'Ricevuta n.' + r.get('receipt_number') + ' emessa il ' + Ext.Date.format(r.get('receipt_date'), 'd/m/Y H:i');
                                                        }
                                                    }
                                                }
                                            ]
                                        },
                                        {
                                            xtype: 'datecolumn',
                                            align: 'center',
                                            dataIndex: 'operation_date',
                                            text: 'Data operazione',
                                            format: 'd/m/Y'
                                        },
                                        {
                                            xtype: 'datecolumn',
                                            align: 'center',
                                            dataIndex: 'accountable_date',
                                            text: 'Data pagamento',
                                            format: 'd/m/Y'
                                        },
                                        {
                                            xtype: 'gridcolumn',
                                            dataIndex: 'type_text',
                                            text: 'Tipo movimento',
                                            flex: 1
                                        },
                                        {
                                            xtype: 'gridcolumn',
                                            defaultWidth: 150,
                                            dataIndex: 'payment_method_text',
                                            text: 'Metodo'
                                        },
                                        {
                                            xtype: 'gridcolumn',
                                            defaultWidth: 150,
                                            dataIndex: 'account_text',
                                            text: 'Conto Corrente'
                                        },
                                        {
                                            xtype: 'gridcolumn',
                                            dataIndex: 'payer_data',
                                            text: 'Pagante'
                                        },
                                        {
                                            xtype: 'gridcolumn',
                                            renderer: function(value, metaData, record, rowIndex, colIndex, store, view) {
                                                if(record.get('subject_class')){
                                                    return value + ' - ' + record.get('subject_class');
                                                }
                                                return '';
                                            },
                                            dataIndex: 'subject_data',
                                            text: 'Riferito a',
                                            flex: 1
                                        },
                                        {
                                            xtype: 'gridcolumn',
                                            renderer: function(value, metaData, record, rowIndex, colIndex, store, view) {
                                                if(record.get('incoming') === true && value>=0) {
                                                    return Ext.util.Format.number(value, '0,000.00');
                                                }
                                                return '';
                                            },
                                            dataIndex: 'amount',
                                            text: 'Entrate'
                                        },
                                        {
                                            xtype: 'gridcolumn',
                                            renderer: function(value, metaData, record, rowIndex, colIndex, store, view) {
                                                if(record.get('incoming') === false) {
                                                    return Ext.util.Format.number(value, '0,000.00');
                                                } else {
                                                    if(value<0) {
                                                        value*=-1;
                                                        return Ext.util.Format.number(value, '0,000.00');
                                                    }
                                                }
                                                return '';
                                            },
                                            dataIndex: 'amount',
                                            text: 'Uscite'
                                        }
                                    ],
                                    selModel: Ext.create('Ext.selection.CheckboxModel', {

                                    }),
                                    dockedItems: [
                                        {
                                            xtype: 'toolbar',
                                            dock: 'top',
                                            items: [
                                                {
                                                    xtype: 'button',
                                                    text: 'Azioni elementi selezionati',
                                                    menu: {
                                                        xtype: 'menu',
                                                        width: 120,
                                                        items: [
                                                            {
                                                                xtype: 'menuitem',
                                                                handler: function(item, e) {
                                                                    var ids = [];

                                                                    Ext.each(Ext.getCmp('CcpStudentPaymentsGrd').getSelectionModel().getSelection(), function(value) {
                                                                        ids.push(value.get('id'));
                                                                    });

                                                                    Ext.widget('CcpReceiptEditMultiWin').show();

                                                                    Ext.getCmp('CcpNewReceiptStudentPayments').hide();
                                                                    Ext.getCmp('CcpReceiptEditMultiWin').setWidth(800);
                                                                    Ext.getCmp('CcpReceiptEditMultiWin').setHeight(200);
                                                                    Ext.getCmp('CcpReceiptEditMultiWin').center();
                                                                    Ext.getCmp('CcpReceiptEditMultiSuffix').hide();

                                                                    Ext.getCmp('CcpReceiptEditMultiLinkedPayments').setValue(Ext.encode(ids));

                                                                },
                                                                iconCls: 'icon-add',
                                                                text: 'Emetti ricevuta'
                                                            }
                                                        ]
                                                    }
                                                }
                                            ]
                                        }
                                    ]
                                }
                            ]
                        }
                    ]
                }
            ],
            listeners: {
                show: {
                    fn: me.onPanelShow,
                    scope: me
                }
            }
        });

        me.callParent(arguments);
    },

    onComboboxSelect9: function(combo, records, eOpts) {
        Ext.getCmp('CcpSubjectsStateCmb').setValue(0);
        Ext.getCmp('CcpStudentsSearchTxt').setValue();
        Ext.getStore('CcpStudents').removeAll();
        Ext.getStore('CcpStudents').load({
            params : {
                subject_school_year: records[0].get('value')
            }
        });

        Ext.getStore('CcpServizi').load({
            params : {
                subject_school_year: records[0].get('value')
            }
        });

    },

    onComboboxSelect7: function(combo, records, eOpts) {
        Ext.getCmp('CcpStudentsGrd').filterSubjects();


    },

    onCcpStudentsSearchTxtChange: function(field, newValue, oldValue, eOpts) {
        Ext.getCmp('CcpStudentsGrd').filterSubjects();

    },

    onViewItemClick: function(dataview, record, item, index, e, eOpts) {


        setTimeout(function(){
            var studentIdMc = record.get('db_id');
            Ext.getCmp('CcpStudentsGrd').filterByStudent(studentIdMc);
            Ext.getCmp('CcpMovementsStudentGrid').getSelectionModel().deselectAll();
        }, 200);
    },

    onViewItemKeydown: function(dataview, record, item, index, e, eOpts) {

        // only if event key is Ext.EventObject.UP or Ext.EventObject.DOWN
        if(e.getKey() !== Ext.EventObject.UP && e.getKey() !== Ext.EventObject.DOWN) {
            return;
        }


        setTimeout(function(){
            var studentIdMc = Ext.getCmp('CcpStudentsGrd').getSelectionModel().getSelection()[0].get('db_id');
            Ext.getCmp('CcpStudentsGrd').filterByStudent(studentIdMc);
        }, 200);


    },

    onComboboxExpand1: function(field, eOpts) {
        var ids = [];
        Ext.each(Ext.getStore('Credits').getRange(), function(c) {
            ids.push(c.get('credit_type_id'));
        });

        Ext.getStore('CreditsType').filterBy(function(rec, id) {
            if(ids.includes(rec.get('id'))) {
                return false;
            }
            else {
                return true;
            }
        });
    },

    onViewItemDblClick2: function(dataview, record, item, index, e, eOpts) {
        if(record.get('id') && record.get('movement')===false) {

            Ext.widget('CcpDepositWin').show();
            Ext.getStore('CcpDeposits').load({
                params: {
                    credit_id: record.get('id')
                }
            });

            Ext.getCmp('CcpDepositWin').credit_id = record.get('id');
        }

    },

    onRowEditingBeforeEdit2: function(editor, context, eOpts) {
        if(context.record.get('id')) return false;


    },

    onRowEditingEdit3: function(editor, context, eOpts) {
        var s = Ext.getCmp('CcpStudentsGrd').getSelectionModel().getSelection()[0];

        Ext.Ajax.request({
            url: '/mc2-api/ccp/credits',
            method: 'POST',
            params: {
                credit_type_id: context.record.get('description'),
                subject_type: 'S',
                subject_id: s.get('db_id')
            },
            success: function() {
                Ext.getCmp('CcpStudentsGrd').filterByStudent(s.get('db_id'));
            }
        });
    },

    onRowEditingCanceledit2: function(editor, context, eOpts) {
        if(!context.newValues.description) {
            Ext.getCmp('CcpCreditsGrd').getStore().removeAt(0);
        }
    },

    onRowEditingEdit: function(editor, context, eOpts) {

        if(context.record.get('subject_type') === 'P')
            Ext.Msg.confirm('ATTENZIONE','Questo sconto è applicato al parente. Questo significa che la modifica inciderà su tutti i suoi figli. Confermi l\'operazione?', function (btn) {

                if(btn === 'yes') {
                    Ext.getCmp('CcpStudentDiscountGrd').updateDiscount(context.record);
                } else {
                    context.record.reject();
                    mc2ui.app.rec = context;
                }
            });
        else
            Ext.getCmp('CcpStudentDiscountGrd').updateDiscount(context.record);



    },

    onCcpStudentMovementYearFilterCmbSelect: function(combo, records, eOpts) {
        var studentIdMc = Ext.getCmp('CcpStudentsGrd').getSelectionModel().getSelection()[0].get('db_id');
        Ext.getCmp('CcpStudentsGrd').filterByStudent(studentIdMc);
        Ext.getCmp('CcpMovementsStudentGrid').getSelectionModel().deselectAll();
    },

    onComboboxChange1: function(field, newValue, oldValue, eOpts) {
        if (!newValue) {
            var studentIdMc = Ext.getCmp('CcpStudentsGrd').getSelectionModel().getSelection()[0].get('db_id');
            Ext.getCmp('CcpStudentsGrd').filterByStudent(studentIdMc);
            Ext.getCmp('CcpMovementsStudentGrid').getSelectionModel().deselectAll();
        }
    },

    onCheckboxModelSelectionChange: function(model, selected, eOpts) {

        var ids = [],
            selectedStudent = Ext.getCmp('CcpStudentsGrd').getSelectionModel().getSelection()[0];

        Ext.each(selected, function(record) {
            ids.push(record.get('id'));
        });


        if(ids.length===0) {
            Ext.getCmp('CcpStudentsTab').reloadStudent();
        } else {
            Ext.Ajax.request({
                method: 'GET',
                url: '/mc2-api/ccp/movement',
                params: {
                    'ids[]': ids,
                    limit: 0,
                    add_student_credit: true,
                    subject_type: 'S',
                    subject_id: selectedStudent.get('db_id'),
                },
                success: function(r) {
                    var res = Ext.decode(r.responseText);
                    if(res.success===true) {
                        Ext.getCmp('CcpMovementsStudentGrid').setSummary(res);
                        Ext.getCmp('CcpStudentMovementLbl').getEl().setHTML('<b style="color:red">Totali dei movimenti selezionati</b>');
                    } else {
                        Ext.Msg.alert('ERRORE', 'Errore durante la richiesta dei totali. Ricaricare lo studente per sicurezza');
                    }

                }
            });

        }

    },

    onViewItemContextMenu2: function(dataview, record, item, index, e, eOpts) {
        var mm,
            mc, mcr,
            items = [],
            ap = Ext.getCmp('ArchivePnl');

        Ext.getCmp('CcpMovementsStudentGrid').getSelectionModel().select(index, true);

        // Edit
        if(mc2ui.app.permissionUI.CcpMovementEditMn == null ||
           mc2ui.app.permissionUI.CcpMovementEditMn.contextCcpMovementEdit !== 'hide') {
            items.push({
                xtype: 'menuitem',
                text: 'Modifica',
                iconCls: 'icon-pencil',
                handler: function() {
                    var r = Ext.getCmp('CcpMovementsStudentGrid').getSelectionModel().getSelection()[0];
                    mc2ui.view.CcpPnl.openEditMovementWin(r.get('id'), 'movement_student');

                }
            });
        }


        // Detail
        items.push(
            {
                xtype: 'menuitem',
                text: 'Dettaglio',
                iconCls: 'icon-information',
                handler: function() {
                    var r = Ext.getCmp('CcpMovementsStudentGrid').getSelectionModel().getSelection()[0];
                    mc2ui.view.CcpPnl.openEditMovementWin(r.get('id'), 'movement_student');
                    Ext.getCmp('CcpSaveMovementIdBtn').hide();
                }
            }
        );

        items.push(
            {
                xtype: 'menuitem',
                text: 'Esportazione EASY',
                handler: function() {
                    Ext.widget('ExportEasyWin').show();
                    Ext.getCmp('ExportEasyWin').inStudent = true;
                }
            }
        );

        // Aggiungi a fattura
        if(!record.get('invoice_id')) {
            var invs = [],
                check = [];
            Ext.each(Ext.getCmp('CcpMovementsStudentGrid').getStore().getRange(), function(v){

                if(check.indexOf(v.get('invoice_number')) < 0 && v.get('invoice_number') > 0) {
                    check.push(v.get('invoice_number'));
                    var ii = Ext.create('Ext.menu.Item', {
                        text: v.get('invoice_number'),


                        handler: function() {
                            var mids = [];
                            Ext.each(Ext.getCmp('CcpMovementsStudentGrid').getSelectionModel().getSelection(), function(v){
                                mids.push(v.get('id'));
                            });

                            Ext.Msg.confirm('INFO', 'Stai per modificare la fattura '+v.get('invoice_number')+'. Confermi?',
                                            function(res){
                                                if(res == 'yes') {
                                                    Ext.getCmp('CcpMovementPnl').addMovementToInvoice(mids, v.get('invoice_id'));
                                                }
                                            });
                        }
                    });
                    invs.push(ii);
                }
            });

            items.push(
                {
                    xtype: 'menuitem',
                    text: 'Aggiungi a fattura',
                    iconCls: 'icon-folder_page',
                    menu: {
                        xtype: 'menu',
                        id: 'CcpAddToInvoiceMn',
                        itemId: 'CcpAddToInvoiceMn',
                        items: invs
                    }
                }
            );
        }


        // Pagamento standard

        if(mc2ui.app.permissionUI.CcpLinkedPaymentsToolbar == null ||
           mc2ui.app.permissionUI.CcpLinkedPaymentsToolbar.CcpPaymentNewBtn !== 'hide') {
            items.push(
                {
                    xtype: 'menuitem',
                    text: 'Paga',
                    iconCls: 'icon-page_portrait',
                    handler: function() {
                        Ext.getCmp('CcpMovementPnl').openPaymentWin('movement_student');
                    }
                }
            );


            // Pagamento da credito

            items.push(
                {
                    xtype: 'menuitem',
                    text: 'Paga da/Ricarica credito',
                    iconCls: 'icon-user',
                    hidden: record.get('incoming')===false,
                    menu: {
                        xtype: 'menu',
                        id: 'CcpPaymentByCreditMn',
                        itemId: 'CcpPaymentByCreditMn',
                        //width: 120
                    }
                }
            );
        }

        var rs = Ext.getCmp('CcpMovementsStudentGrid').getSelectionModel().getSelection();
        var payments = [], receipts = [];
        Ext.each(rs, function(v){
            payments = payments.concat(v.get('linked_payments'));
            receipts = receipts.concat(v.get('linked_receipts'));
        });

        if(payments.length > 0) {
            if(receipts.length === 0) {

                //if(record.get('last_payment')) {
                //    if(record.get('linked_receipts').length === 0) {
                items.push(
                    {
                        xtype: 'menuitem',
                        text: 'Emetti ricevuta',
                        iconCls: 'icon-page',
                        handler: function(){
                            Ext.Ajax.request({
                                url: '/mc2-api/ccp/receipt',
                                method: 'POST',
                                jsonData: {
                                    number: null,
                                    date: null,
                                    //linked_payments: Ext.encode(record.get('linked_payments'))
                                    linked_payments: Ext.encode(payments)
                                },
                                success: function(r) {
                                    var receipt = Ext.decode(r.responseText).results;
                                    Ext.getCmp('CcpStudentsGrd').filterByStudent(record.get('subject_id'));
                                    Ext.getStore('CcpReceipts').load();
                                    Ext.Ajax.request({
                                        url: '/mc2-api/core/print',
                                        params: {
                                            newSpool: 1,
                                            print: mc2ui.app.settings.prints.receipt,
                                            namespace: 'CCP',
                                            type: 'PDF',
                                            mime: 'application/pdf',
                                            receipt_id: receipt.id,
                                            number: receipt.number,
                                            date: receipt.date
                                        },
                                        success: function(response, opts) {
                                            var res = Ext.decode(response.responseText);
                                            mc2ui.app.showNotifyPrint(res);

                                        }
                                    });
                                }
                            });
                        }
                    }
                );
            } else {
                items.push(
                    {
                        xtype: 'menuitem',
                        text: 'Stampa ricevuta/e',
                        iconCls: 'icon-page',
                        handler: function(){

                            Ext.each(record.get('linked_receipts'), function(receipt){
                                var print  = receipt.receipt === true ? mc2ui.app.settings.prints.receipt : 'Attestation';

                                Ext.Ajax.request({
                                    url: '/mc2-api/core/print',
                                    params: {
                                        newSpool: 1,
                                        print: print,
                                        namespace: 'CCP',
                                        type: 'PDF',
                                        mime: 'application/pdf',
                                        receipt_id: receipt.id,
                                        number: receipt.number,
                                        date: receipt.date
                                    },
                                    success: function(response, opts) {
                                        var res = Ext.decode(response.responseText);
                                        mc2ui.app.showNotifyPrint(res);
                                    }
                                });
                            });


                        }
                    }
                );
            }
        }

        // Delete
        items.push(
            {
                xtype: 'menuitem',
                text: 'Cancella',
                iconCls: 'icon-cancel',
                handler: function() {
                    Ext.getCmp('CcpMovementsStudentGrid').deleteMovement();
                }
            }
        );

        mm = Ext.getCmp('CcpPaymentMn');
        if(typeof mm !== 'undefined'){
            mm.destroy();
        }

        mm = Ext.create('Ext.menu.Menu', {
            id: 'CcpPaymentMn',
            items: items
        });


        Ext.each(Ext.getStore('Credits').getRange(), function(v) {
            mc = Ext.create('Ext.menu.Item', {
                text: v.get('description'),
                iconCls: 'icon-user',
                handler: function() {
                    Ext.getCmp('CcpMovementPnl').openPaymentWin('movement_student');
                    Ext.getCmp('CcpPaymentCreditId').setValue(v.get('id'));
                    Ext.getCmp('CcpPaymentCreditRemain').setValue(v.get('total'));
                    Ext.getCmp('CcpPaymentPaymentMethodId').hide();
                }
            });


            Ext.getCmp('CcpPaymentByCreditMn').add(mc);

        });
        // CcpPaymentMn


        mm.showAt(e.xy[0], e.xy[1]);
    },

    onViewCellClick: function(tableview, td, cellIndex, record, tr, rowIndex, e, eOpts) {
        if( cellIndex === 2 && parseInt(record.get('invoice_number')) > 0) {
            var invoiceNumber = record.get('invoice_number');

            Ext.each(tableview.getStore().getRange(), function(v, i) {
                if( v.get('invoice_number') == invoiceNumber ) {
                    tableview.getSelectionModel().select(i, true);
                }
            });
        }
    },

    onCcpStTotalCreditChange: function(field, newValue, oldValue, eOpts) {
        Ext.getCmp('CcpTotaleToolBar').setColors(field, newValue);
    },

    onCcpStTotalDebitChange: function(field, newValue, oldValue, eOpts) {
        Ext.getCmp('CcpTotaleToolBar').setColors(field, newValue);
    },

    onCcpStTotalChange: function(field, newValue, oldValue, eOpts) {
        Ext.getCmp('CcpTotaleToolBar').setColors(field, newValue);
    },

    onCcpStTotalCreditPayedChange: function(field, newValue, oldValue, eOpts) {
        Ext.getCmp('CcpTotaleToolBar').setColors(field, newValue);
    },

    onCcpStTotalDebitPayedChange: function(field, newValue, oldValue, eOpts) {
        Ext.getCmp('CcpTotaleToolBar').setColors(field, newValue);
    },

    onCcpStTotalPayedChange: function(field, newValue, oldValue, eOpts) {
        Ext.getCmp('CcpTotaleToolBar').setColors(field, newValue);
    },

    onCcpStTotalCreditToPayChange: function(field, newValue, oldValue, eOpts) {
        Ext.getCmp('CcpTotaleToolBar').setColors(field, newValue);
    },

    onCcpStTotalDebitToPayChange: function(field, newValue, oldValue, eOpts) {
        Ext.getCmp('CcpTotaleToolBar').setColors(field, newValue);
    },

    onCcpStTotalToPayChange: function(field, newValue, oldValue, eOpts) {
        Ext.getCmp('CcpTotaleToolBar').setColors(field, newValue);
    },

    onCcpStTotalCreditExpChange: function(field, newValue, oldValue, eOpts) {
        Ext.getCmp('CcpTotaleToolBar').setColors(field, newValue);
    },

    onCcpStTotalDebitExpChange: function(field, newValue, oldValue, eOpts) {
        Ext.getCmp('CcpTotaleToolBar').setColors(field, newValue);
    },

    onCcpStTotalExpChange: function(field, newValue, oldValue, eOpts) {
        Ext.getCmp('CcpTotaleToolBar').setColors(field, newValue);
    },

    onCcpStTotalCreditNotExpChange: function(field, newValue, oldValue, eOpts) {
        Ext.getCmp('CcpTotaleToolBar').setColors(field, newValue);
    },

    onCcpStTotalDebitNotExpChange: function(field, newValue, oldValue, eOpts) {
        Ext.getCmp('CcpTotaleToolBar').setColors(field, newValue);
    },

    onCcpStTotalNotExpChange: function(field, newValue, oldValue, eOpts) {
        Ext.getCmp('CcpTotaleToolBar').setColors(field, newValue);
    },

    onTextfieldChange2: function(textfield, e, eOpts) {

        /*if(e.getKey() === 13) {
            Ext.getCmp('CcpAnagraficaPnl').saveStudent();
        }*/
    },

    onCcpStudentMarketCmbSelect: function(combo, records, eOpts) {
        var r = records[0];
        if (r.get('tipo') == 'EXTRATIME') {
            Ext.widget('MpExtraTimeEditWin').show();
            Ext.getCmp('MpExtraTimeEditWin').loadData(r);
                Ext.getCmp('MpExtraTimeEditWin').center();
        }
    },

    onRowEditingCanceledit1: function(editor, context, eOpts) {
        if(!context.record.get('id_acquisto')) {
            Ext.getStore('CcpStudentMarkets').remove(context.record);
        }
    },

    onRowEditingEdit2: function(editor, context, eOpts) {
        var student = Ext.getCmp('CcpStudentsGrd').getSelectionModel().getSelection()[0],
            url= '/mc2-api/ccp/marketplace/purchase/add',
            method = 'POST',
            serviziStore = Ext.getStore('CcpServizi'),
            servizio, jsonData;

        serviziStore.filter({
            property: 'id_marketplace',
            value: context.record.get('id_marketplace'),
            exactMatch: true,
            caseSensitive: true
        });

        servizio = serviziStore.getRange()[0];
        serviziStore.removeFilter();

        if(context.record.get('id_acquisto') > 0) {
            // url += '?id=' + context.record.get('id_acquisto');

            url = '/mc2-api/ccp/marketplace/purchase/update';
        }


        if(context.originalValues.contabilizzato == 'NO' && context.record.get('contabilizzato') == 'SI' && parseInt(servizio.get('id_tipo_movimento')) >0) {
            Ext.Ajax.request({
                url: '/mc2-api/ccp/type/' + servizio.get('id_tipo_movimento'),
                success: function(r) {
                    var res = Ext.decode(r.responseText),
                        amount=0,
                        school_year,
                        expirations=[],
                        creation_date = new Date().toISOString();

                    school_year = res.results.school_year == 'TUTTI' ? mc2ui.app.settings.mcDb.schoolYear:res.results.school_year ;

                    Ext.each(res.results.expirations, function(v) {
                        if(parseFloat(v.value)>0) {
                            expirations.push({
                                expiration: v.expiration ? Ext.Date.parse(v.expiration, 'd/m/Y').toISOString() : creation_date,
                                value: v.value,
                                da_ratei: v.da_ratei,
                                a_ratei: v.a_ratei
                            });
                            amount += v.value;
                        }
                    });


                    if(expirations.length > 0) {

                        Ext.Msg.confirm('ATTENZIONE', 'Verranno creati i movimenti relativi, '+expirations.length+' in questo caso. Confermi?', function(btn) {
                            if(btn == 'yes') {
                                Ext.getCmp('CcpStudentMovementsServiziGrd').setLoading(true);
                                Ext.Ajax.request({
                                    url: '/mc2-api/ccp/movement',
                                    method: 'POST',
                                    jsonData: {
                                        subjects: [{
                                            "id": student.get('db_id'),
                                            "school_year": school_year,
                                            "type": student.get('type'),
                                            "class": student.get('class')+student.get('section'),
                                            "data": "("+student.get('type')+") "+student.get('surname')+" "+student.get('name')+" - "+student.get('class')+student.get('section')+" ("+student.get('school_address_code')+")",
                                            "school_address": student.get('school_address'),
                                            "school_address_code": student.get('school_address_code')
                                        }],
                                        "subject_school_year": student.get('subject_school_year') ? student.get('subject_school_year') : mc2ui.app.settings.mcDb.schoolYear,
                                        creation_date: creation_date,
                                        expirations: expirations,
                                        amount: amount,
                                        school_year: school_year,
                                        type_id: servizio.get('id_tipo_movimento')
                                    },
                                    success: function(r) {
                                        Ext.getCmp('CcpStudentMovementsServiziGrd').setLoading(false);
                                        var res = Ext.decode(r.responseText);
                                        if(res.success === true) {

                                            jsonData = {
                                                contabilizzato: context.record.get('contabilizzato'),
                                                validita_inizio: context.record.get('validita_inizio'),
                                                validita_fine: context.record.get('validita_fine'),
                                                subject_school_year: student.get('subject_school_year')
                                            };
                                            if(context.record.get('id_acquisto') > 0){
                                                jsonData.id_acquisto = context.record.get('id_acquisto');
                                            } else {
                                                jsonData.id_studente = student.get('db_id');
                                                jsonData.id_marketplace = context.record.get('id_marketplace');
                                            }
                                            Ext.Ajax.request({
                                                url: url,
                                                method: method,
                                                jsonData: jsonData,
                                                success: function(r) {
                                                    var res = Ext.decode(r.responseText);
                                                    if(res.success === true) {
                                                        Ext.getCmp('CcpStudentsGrd').filterByStudent(student.get('db_id'));
                                                    }
                                                }
                                            });

                                        }
                                    }
                                });
                            }
                        });
                    }


                }
            });
        } else {
            jsonData = {
                contabilizzato: context.record.get('contabilizzato'),
                validita_inizio: context.record.get('validita_inizio'),
                validita_fine: context.record.get('validita_fine'),
                subject_school_year: student.get('subject_school_year')
            };
            if(context.record.get('id_acquisto') > 0){
                jsonData.id_acquisto = context.record.get('id_acquisto');
            } else {
                jsonData.id_studente = student.get('db_id');
                jsonData.id_marketplace = context.record.get('id_marketplace');
            }
            Ext.getCmp('CcpStudentMovementsServiziGrd').setLoading(true);
            Ext.Ajax.request({
                url: url,
                method: method,
                jsonData: jsonData,
                success: function(r) {
                    var res = Ext.decode(r.responseText);
                    if(res.success === true) {
                        Ext.getCmp('CcpStudentsGrd').filterByStudent(student.get('db_id'));
                        Ext.getCmp('CcpStudentMovementsServiziGrd').setLoading(false);
                    }
                }
            });
        }


    },

    onRowEditingBeforeEdit1: function(editor, context, eOpts) {
        if(context.record.get('id_acquisto') > 0) Ext.getCmp('CcpStudentMarketCmb').disable();
    },

    onComboboxExpand: function(field, eOpts) {
        Ext.getStore('CcpTypes').load({
            params: {section:'MCM'}
        });
    },

    onRowEditingEdit1: function(editor, context, eOpts) {
        var student = Ext.getCmp('CcpStudentsGrd').getSelectionModel().getSelection()[0],
            url = '/mc2-api/ccp/movement',
            method = 'POST';

        if(context.record.get('id') > 0) {
            method= 'PUT';
            url += '/' + context.record.get('id');
        }

        Ext.Ajax.request({
            url: url,
            method: method,
            jsonData: {
                subjects: [{
                    "id": student.get('db_id'),
                    "type": student.get('type'),
                    "class": student.get('class')+student.get('section'),
                    "data": "("+student.get('type')+") "+student.get('surname')+" "+student.get('name')+" - "+student.get('class')+student.get('section')+" ("+student.get('school_address_code')+")",
                    "school_address": student.get('school_address'),
                    "school_address_code": student.get('school_address_code')
                }],
                creation_date: context.newValues.creation_date,
                expirations: [{
                    expiration: context.newValues.creation_date,
                    value: context.newValues.total
                }],
                subject_school_year: student.get('subject_school_year') ? student.get('subject_school_year') : mc2ui.app.settings.mcDb.schoolYear,
                amount: context.newValues.total,
                school_year: Ext.getStore('CcpTypes').getById(context.newValues.type_id).get('school_year'),
                type_id: context.newValues.type_id
            },
            success: function(r) {
                var res = Ext.decode(r.responseText);
                if(res.success === true) {
                    Ext.getCmp('CcpStudentsGrd').filterByStudent(student.get('db_id'));
                }
            }
        });

    },

    onRowEditingCanceledit: function(editor, context, eOpts) {
        if(!context.record.get('id')) {
            Ext.getStore('CcpStudentMovementsMensa').remove(context.record);
        }
    },

    onRowEditingBeforeEdit: function(editor, context, eOpts) {

        if(context.record.get('consolidamento')) {
            return false;
        }
    },

    onViewItemContextMenu: function(dataview, record, item, index, e, eOpts) {
        e.stopEvent();
        Ext.getCmp('CcpStudentsReceiptMn').showAt([e.xy[0], e.xy[1]]);


    },

    onPanelShow: function(component, eOpts) {
        Ext.getCmp('CcpStudentsGrd').filterSubjects();

        /*var search = Ext.getCmp('CcpStudentsSearchTxt').getValue();
        Ext.getStore('CcpSubjects').clearFilter();

        if (search) {
            Ext.getCmp('CcpStudentsSearchTxt').setValue();
            Ext.getCmp('CcpStudentsSearchTxt').setValue(search);
        }*/
    }

});