const path = require('path');
const HtmlWebpackPlugin = require('html-webpack-plugin');
const CopyWebpackPlugin = require('copy-webpack-plugin');


module.exports = {
    mode: 'development',
    entry: {
        ext: './ext/ext-all.js',
        notification: './resources/js/Notification.js',
        lang: './resources/js/ext-lang-it.js',
        sencha: {
            import: './app.js',
            dependOn: 'ext'
        },
        bootstrap: './bootstrap.css',
        mc2: './src/index.js',
    },
    output: {
        filename: '[name].bundle.js',
        path: path.resolve(__dirname, 'dist'),
        clean: true,
    },
    module: {
        rules: [
            {
                test: /ext-all\.js$/,
                use: [
                    {
                        loader: 'script-loader',
                    },
                ],
            },
            {
                test: /\.css$/,
                use: ['style-loader', 'css-loader'],
            },
            // Rule for images
            {
                test: /\.(png|jpe?g|gif|svg)$/i,
                type: 'asset/resource',
                generator: {
                    filename: 'resources/[name].[contenthash][ext]'
                }
            },
        ],
    },
    devServer: {
        static: path.resolve(__dirname),
        port: 3000,
        open: false,
        hot: true,
        proxy: [
            {
                context: ['/mc2', '/mc2-api'],
                target: 'http://localhost',
                changeOrigin: true,
                secure: false,
            },
            {
                context: ['/next-api'],
                target: 'http://mastertest9.registroelettronico.com',
                changeOrigin: true,
                secure: false,
            },
        ],
    },
    plugins: [
        new HtmlWebpackPlugin({
            template: 'index.html',
            favicon: path.resolve(__dirname, 'resources', 'images', 'favicon.png'),

        }),
        new CopyWebpackPlugin({
            patterns: [
                { from: path.resolve(__dirname, 'app'), to: 'app' },
                { from: path.resolve(__dirname, 'resources', 'images', 'background.jpg'), to: 'resources/images/background.jpg' },
                { from: path.resolve(__dirname, 'resources', 'icons', 'accept.png'), to: 'resources/icons/accept.png' },
                { from: path.resolve(__dirname, 'resources', 'icons', 'delete.png'), to: 'resources/icons/delete.png' },
                { from: path.resolve(__dirname, 'resources', 'icons', 'attach.png'), to: 'resources/icons/attach.png' },
            ]
        }),
    ],
};