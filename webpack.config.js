const path = require('path');
const HtmlWebpackPlugin = require('html-webpack-plugin');
const CopyWebpackPlugin = require('copy-webpack-plugin');
const MiniCssExtractPlugin = require('mini-css-extract-plugin');

const isProduction = process.env.NODE_ENV === 'production';

module.exports = {
    mode: isProduction ? 'production' : 'development',
    entry: isProduction ? {
        // Produzione: singolo bundle
        bundle: './sencha-entry-simple.js'
    } : {
        // Sviluppo: entry multipli come prima
        ext: './ext/ext-all.js',
        notification: './resources/js/Notification.js',
        lang: './resources/js/ext-lang-it.js',
        sencha: {
            import: './app.js',
            dependOn: 'ext'
        },
        bootstrap: './bootstrap.css',
        mc2: './src/index.js',
    },
    output: {
        filename: isProduction ? 'bundle.js' : '[name].bundle.js',
        path: path.resolve(__dirname, 'dist'),
        clean: true,
        // Importante per mantenere compatibilità con Ext.Loader
        library: isProduction ? undefined : '[name]',
        libraryTarget: 'umd',
        globalObject: 'this'
    },
    module: {
        rules: [
            {
                test: /ext-all\.js$/,
                use: [
                    {
                        loader: 'script-loader',
                    },
                ],
            },
            {
                test: /\.css$/,
                use: isProduction
                    ? [MiniCssExtractPlugin.loader, 'css-loader']
                    : ['style-loader', 'css-loader'],
            },
            {
                test: /\.js$/,
                include: [
                    path.resolve(__dirname, 'app'),
                    path.resolve(__dirname, 'src')
                ],
                exclude: [
                    /node_modules/,
                    /ext\/ext-all\.js$/,
                    /resources\/js\//
                ],
                use: {
                    loader: 'imports-loader',
                    options: {
                        // Assicura che Ext sia disponibile globalmente per il codice Sencha
                        imports: 'default Ext ext/ext-all.js'
                    }
                }
            },
            // Rule for images
            {
                test: /\.(png|jpe?g|gif|svg)$/i,
                type: 'asset/resource',
                generator: {
                    filename: 'resources/[name].[contenthash][ext]'
                }
            },
        ],
    },
    devServer: {
        static: path.resolve(__dirname),
        port: 3000,
        open: false,
        hot: true,
        proxy: [
            {
                context: ['/mc2', '/mc2-api'],
                target: 'http://localhost',
                changeOrigin: true,
                secure: false,
            },
            {
                context: ['/next-api'],
                target: 'http://mastertest9.registroelettronico.com',
                changeOrigin: true,
                secure: false,
            },
        ],
    },
    plugins: [
        new HtmlWebpackPlugin({
            template: 'index.html',
            favicon: path.resolve(__dirname, 'resources', 'images', 'favicon.png'),
        }),
        ...(isProduction ? [
            new MiniCssExtractPlugin({
                filename: 'bundle.css'
            })
        ] : []),
        new CopyWebpackPlugin({
            patterns: [
                // In produzione non copiamo app/ perché è bundlato
                ...(isProduction ? [] : [
                    { from: path.resolve(__dirname, 'app'), to: 'app' }
                ]),
                { from: path.resolve(__dirname, 'resources', 'images', 'background.jpg'), to: 'resources/images/background.jpg' },
                { from: path.resolve(__dirname, 'resources', 'icons', 'accept.png'), to: 'resources/icons/accept.png' },
                { from: path.resolve(__dirname, 'resources', 'icons', 'delete.png'), to: 'resources/icons/delete.png' },
                { from: path.resolve(__dirname, 'resources', 'icons', 'attach.png'), to: 'resources/icons/attach.png' },
            ]
        }),
    ],
    optimization: {
        minimize: isProduction,
        // In produzione, tutto in un singolo chunk
        splitChunks: isProduction ? {
            chunks: 'all',
            cacheGroups: {
                default: {
                    minChunks: 1,
                    priority: -20,
                    reuseExistingChunk: true,
                    name: false
                }
            }
        } : undefined
    },
};