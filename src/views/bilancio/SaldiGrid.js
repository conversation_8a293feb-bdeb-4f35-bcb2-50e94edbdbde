import { getSaldi } from "../../stores/SaldiStore.js";
import { parseEuro } from "../../services/formatters.js";
import Mc2Table from "../../services/Mc2Table.js";


export default class SaldiGrid extends HTMLElement {

    filter = {};

    constructor() {
        super();
    }

    async connectedCallback() {

        this.innerHTML = `

            <div style="display: flex;flex-direction: row;justify-content: space-between;padding: 5px;">
                <div>

                </div>
                <button id="saldi_print_xls" class="mc2-btn secondary">Esportazione xls</button>
            </div>

            <div id="table" style="flex:1"></div>
            <grid-pagination page-size="50" total="0"></grid-pagination>
        `

        this.table = new Mc2Table(this.querySelector("#table"), {
            columnDefaults: {
                headerSort: false
            },
            columns: [
                { title: "Nome studente", field: "subject_data" },
                { title: "Crediti", field: "credit", width: 100, hozAlign: "right", headerHozAlign: "right", formatter: (cell) => parseEuro(cell.getValue()) },
                { title: "Debiti", field: "debit", width: 100, hozAlign: "right", headerHozAlign: "right", formatter: (cell) => parseEuro(cell.getValue()) },
                { title: "Crediti pagati", field: "credit_payments", width: 100, hozAlign: "right", headerHozAlign: "right", formatter: (cell) => parseEuro(cell.getValue()) },
                { title: "Debiti pagati", field: "debit_payments", width: 100, hozAlign: "right", headerHozAlign: "right", formatter: (cell) => parseEuro(cell.getValue()) },
                { title: "Crediti da pagare", field: "credit_to_pay", width: 100, hozAlign: "right", headerHozAlign: "right", formatter: (cell) => parseEuro(cell.getValue()) },
                { title: "Debiti da pagare", field: "debit_to_pay", width: 100, hozAlign: "right", headerHozAlign: "right", formatter: (cell) => parseEuro(cell.getValue()) },
                { title: "Crediti fatturati", field: "credit_invoice", width: 100, hozAlign: "right", headerHozAlign: "right", formatter: (cell) => parseEuro(cell.getValue()) },
                { title: "Saldo fatturati", field: "credit_invoice_to_pay", width: 100, hozAlign: "right", headerHozAlign: "right", formatter: (cell) => parseEuro(cell.getValue()) },
                { title: "Saldo", field: "balance", width: 100, hozAlign: "right", headerHozAlign: "right", formatter: (cell) => parseEuro(cell.getValue()) },
            ],
        });

        this.pagination = this.querySelector("grid-pagination");
        this.pagination.onPageChange = async (paginationData) => {
            await this.updateSaldi(this.filter, paginationData);
            this.pagination.setAttribute("current-page", paginationData.page);
        }

        this.table.on("tableBuilt", () => this.updateSaldi());

        this.querySelector("#saldi_print_xls").addEventListener("click", () => {
            Ext.widget('CcpExportResidualsWin').show();
        });
    }

    updateSaldi = async (filter = {}, pagination = this.pagination.getInitialData()) => {
        this.filter = filter;

        this.table.dataLoader.alertLoader();
        try {
            const res = await getSaldi({
                ...pagination,
                ...filter
            });
            this.table.setData(res.results);
            this.pagination.setAttribute("total", res.total);
            this.pagination.setAttribute("current-page", pagination);

        } catch (error) {
            console.error(error);
        }
        this.table.dataLoader.clearAlert()
    }

}

customElements.define("saldi-grid", SaldiGrid)


