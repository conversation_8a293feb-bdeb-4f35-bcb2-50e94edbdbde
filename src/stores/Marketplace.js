import { get, update } from '../services/Api.js';

let marketplaceWithoutMovementType = [];

const fetchMarketplaceWithoutMovementType = async (filter = null) => {
    const res = await get('/next-api/v1/marketplace/lista_marketplace_senza_tipo_movimento');
    if (res.dati) {
        marketplaceWithoutMovementType = res.dati;
    }
};

const filterMarketplaceWithoutMovementType = () => {
    return marketplaceWithoutMovementType.map(marketplace => {
        return {
            id: marketplace.id_marketplace,
            description: marketplace.descrizione,
            type_id_previous_year: marketplace.id_tipo_movimento_anno_precedente,
        }
    });
}

const updateMarketplaceMovementType = async (id, type_id) => {
    const res = await update('/next-api/v1/marketplace/aggiorna_tipo_movimento', {
        'id_marketplace': id,
        'id_tipo_movimento': type_id
    });
    return res;
}

export {
    fetchMarketplaceWithoutMovementType,
    filterMarketplaceWithoutMovementType,
    updateMarketplaceMovementType
}