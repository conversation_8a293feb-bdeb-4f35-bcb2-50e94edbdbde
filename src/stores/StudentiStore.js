import { get } from '../services/Api.js';

let statiStudenti = null;

const refreshStatiStudenti = async () => {
    if (!statiStudenti) {
        const response = await get('/mc2-api/ccp/student/states');
        statiStudenti = response.results;
    }
}

const getStudenti = async (filter = null) => {
    let url = '/ccp/subject';
    if (filter) {
        const queryParams = new URLSearchParams(filter).toString();
        url += `?${queryParams}`;
    }
    return await get(url);
};

const getStatiStudenteCombo = async () => {
    await refreshStatiStudenti();
    return statiStudenti.map(stato => {
        return {
            name: stato.descrizione,
            value: stato.id_stato_studente_personalizzato,
        }
    });
}


export {
  getStudenti,
  getStatiStudenteCombo
}