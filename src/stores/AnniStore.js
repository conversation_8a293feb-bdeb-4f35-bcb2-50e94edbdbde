import { get } from '../services/Api.js';

let anniScolastici = null;

const refreshAnniScolastici = async () => {
    if (!anniScolastici) {
        const response = await get('/mc2-api/ccp/mc_db');
        anniScolastici = response.results;
    }
}

const getAnniScolastici = async (filter = null) => {
    await refreshAnniScolastici();
    const result =  anniScolastici.map(anno => {
        return {
            name: anno.name,
            value: anno.name,
        }
    });
    const resInJson = JSON.stringify(result);
    console.log("getAnniScolastici", resInJson);
    return result;
};

const getAnnoScolasticoCorrente = async () => {
    await refreshAnniScolastici();
    const annoCorrente = anniScolastici.filter(anno => anno.current === true);
    return annoCorrente.length > 0 ? annoCorrente[0] : null;
}

const getCurrentSchoolYear = () => {
    return anniScolastici.find(anno => anno.current === true);
}


export {
    getAnniScolastici,
    getAnnoScolasticoCorrente,
    getCurrentSchoolYear,
    refreshAnniScolastici
}