import { get } from '../services/Api.js';

let movementType = [];

const fetchMovementType = async (filter = null) => {
    const res = await get('/mc2-api/ccp/type');
    if (res.results) {
        movementType = res.results;
    }
};

const getMovementTypeById = (value) => {
    let res = movementType.find(mType => mType.id === value);
    return res;
}

const filterMovementType = (filters = null) => {
    return movementType.filter(mType => {
        if (filters) {
            for (let key in filters) {
                if (mType[key] !== filters[key]) {
                    return false;
                }
            }
        }
        return true;
    });
}


export {
    fetchMovementType,
    getMovementTypeById,
    filterMovementType
}