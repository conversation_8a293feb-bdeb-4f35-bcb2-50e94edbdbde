@charset "UTF-8";

.mc2 button {
  background-color: var(--color-primary);
  /* Versione più chiara */
  color: var(--color-background);
  /* Testo scuro per miglior contrasto */
  border: none;
  padding: 5px 10px;
  border-radius: 4px;
  cursor: pointer;
}

.mc2 button:hover {
  background-color: var(--color-primary-hover);
  /* Leggermente più scuro per hover */
}

.mc2 input {
  width: 100%;
  padding: 3px;
  border: 1px solid #7a9bc5;
  background-color: white;
  box-sizing: border-box;
}

.dropdown-container {
  position: relative;
  display: inline-block;
}

.dropdown-menu {
  position: absolute;
  background-color: white;
  border: 1px solid #ccc;
  box-shadow: 0px 8px 16px rgba(0, 0, 0, 0.2);
  padding: 10px;
  z-index: 1000;
  min-width: 150px;
}

.dropdown-menu button {
  display: block;
  width: 100%;
  text-align: left;
  background: none;
  border: none;
  padding: 8px 5px;
  cursor: pointer;
}

.dropdown-menu button:hover {
  background-color: #f0f0f0;
}

/** *
    Stile per la paginazione delle tabelle
 */
grid-pagination {
  padding: 5px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

grid-pagination input {
  width: 45px;
}

grid-pagination button {
  display: flex;
  align-items: center;
  padding: 2px 5px;
  justify-content: center;
  cursor: pointer;
  border: 0px;

}

grid-pagination button:hover {
  background-color: var(--color-light-gray);
  /* Leggermente più scuro per hover */
}

grid-pagination .pagination-pages {
  display: flex;
  align-items: center;
  gap: 5px;
}

grid-pagination .pagination-pages .chevron-right {
  width: 20px;
  height: 20px;
}

grid-pagination .pagination-pages .chevron-left {
  width: 20px;
  height: 20px;
}

grid-pagination .pagination-pages .chevron-fisrt {
  width: 20px;
  height: 20px;
}

grid-pagination .pagination-pages .chevron-last {
  width: 20px;
  height: 20px;
}

/** *
    Stile base per la classica visualizzazione mc2 a due colonne filtro + griglia
 */
.filter-grid-layout {
  display: flex;
  flex-direction: row;
  gap: 5px;
  padding: 5px;
  height: 100%;
}

.filter-grid-layout .filter-panel {
  width: 320px;
  height: 100%;
  background-color: #f9f9f9;
  border: 1px solid #99bce8;
  border-radius: 4px;
  display: flex;
  flex-direction: column;
}

.filter-grid-layout .filter-panel form {
  display: flex;
  flex-direction: column;
  padding: 5px;
  row-gap: 10px;
  height: 100%;
  width: 100%;
  overflow: auto;
}

.filter-grid-layout .filter-panel .title {
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 10px;
  color: #ffffff;
  background-color: #99bce8;
  padding: 5px;
  border: 1px solid #99bce8;
  border-radius: 3px 3px 0px 0px;
}

.filter-grid-layout .grid-panel {
  display: flex;
  flex-direction: column;
  flex: 1;
  height: 100%;
  overflow: auto;
}

/**
 * Stile per i vari tipi di campi
 */


combo-box {
  display: flex;
  flex-direction: column;
  row-gap: 2px;
}

combo-box label {
  color: #15428b;
}

combo-box select {
  width: 100%;
  border: 1px solid #ccc;
  border-radius: 2px;
  padding: 5px;
  background-color: #FFF;
}

.print-list-btn {
  position: fixed;
  top: 10px;
  right: 10px;
  z-index: 1000;
  border-radius: 100%;
  width: 50px;
  height: 50px;
  border: 0px;
  box-shadow: 0px 0px 15px;
  background-color: var(--color-primary);
  opacity: 0.8;
  background-image: url("../../resources/images/print_spool.png");
  background-size: 30px;
  background-position: center;
  background-repeat: no-repeat;
  cursor: pointer;
}

.print-list-btn:hover {
  opacity: 1;
}

:root {
  --color-primary: #82abdf;
  --color-primary-hover: #6594ce;
  --color-secondary: #6c757d;
  --color-secondary-hover: #5a6268;
  --color-background: #ffffff;
  --color-text: #212529;
  --color-success: #28a745;
  --color-success-hover: #218838;
  --color-warning: #ffc107;
  --color-warning-hover: #e0a800;
  --color-danger: #dc3545;
  --color-danger-hover: #c82333;
  --color-border: #dee2e6;
  --color-overlay: rgba(0, 0, 0, 0.5);
  --color-text-input: #ffffff;
  --color-light-gray: #f8f9fa;
  --color-medium-gray: #e9ecef;
  --border-radius: 3px;
}

.material-symbols-outlined {
  font-family: "Material Symbols Outlined";
  font-weight: normal;
  font-style: normal;
  font-size: 15px;
  /* Preferred icon size */
  display: inline-block;
  text-transform: none;
  letter-spacing: normal;
  word-wrap: normal;
  white-space: nowrap;
  direction: ltr;
}

.material-symbols-outlined.size-20 {
  font-size: 20px;
}

.h-box {
  display: flex;
  flex-direction: row;
  width: 100%;
}

.v-box {
  display: flex;
  flex-direction: column;
}

.h-full {
  height: 100%;
}

.w-full {
  width: 100%;
}

.mc2-select {
  width: 100%;
  padding: 5px;
}