.tabulator {
  background-color: var(--color-background);
  border: 1px solid var(--color-medium-gray);
  font-size: 12px;
}

.tabulator .tabulator-header {
  background-color: var(--color-medium-gray);
  color: var(--color-text);
  border-bottom: 0px;
  padding: 5px;
}

.tabulator .tabulator-header .tabulator-col  {
  background-color: var(--color-medium-gray);
}

.tabulator-row {
    min-height: 30px;
    align-items: center;
    display: flex;
}

.tabulator-row.tabulator-row-even{
    background-color: var(--color-light-gray);
}

.tabulator-row .tabulator-cell {
  border-right: none;
}


@media (hover: hover) and (pointer: fine) {
  .tabulator-row.tabulator-selectable:hover {
    background-color: var(--color-medium-gray);
    cursor: pointer;
  }
}

/* Fix per z-index degli editor list - sempre visibili sopra tutto */
.tabulator-edit-select-list {
  z-index: 99999999999 !important;
  position: absolute !important;
}

/* Fix per tutti gli editor di tabulator */
.tabulator-editor {
  z-index: 99999999999 !important;
  position: relative !important;
}

/* Fix specifico per select editor */
.tabulator-editor select {
  z-index: 99999999999 !important;
  position: relative !important;
}

/* Fix per dropdown di tabulator in generale */
.tabulator .tabulator-edit-select-list {
  z-index: 99999999999 !important;
}

/* Fix per elementi dentro modal/popup */
ui-modal .tabulator-edit-select-list,
.modal .tabulator-edit-select-list {
  z-index: 99999999999 !important;
}

ui-modal .tabulator-editor,
.modal .tabulator-editor {
  z-index: 99999999999 !important;
}

/* Fix per tutti i dropdown select di tabulator */
.tabulator-editor-list {
  z-index: 99999999999 !important;
}

/* Regola globale per tutti gli elementi select di tabulator */
select.tabulator-editor-select {
  z-index: 99999999999 !important;
  position: relative !important;
}

/* Forza tutti gli elementi con classe che contiene 'tabulator' e 'edit' */
[class*="tabulator"][class*="edit"] {
  z-index: 99999999999 !important;
}



