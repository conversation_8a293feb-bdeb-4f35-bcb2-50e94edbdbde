import Mc2Element from "./Mc2Element";

export default class Modal extends Mc2Element {


    constructor() {
        super();
    }

    connectedCallback() {
        let title = this.getAttribute("title") || "";

        this.innerHTML = `
            <style>
                .modal {
                    position: fixed;
                    z-index: 999999999;
                    left: 0;
                    top: 0;
                    width: 100%;
                    height: 100%;
                    background-color: var(--color-overlay);
                    display: flex;
                    justify-content: center;
                    align-items: center;
                }

                .modal ui-panel {
                    width: 70%;
                    height: 70%;
                }

                /* Fix per z-index degli editor di tabulator dentro il modal */
                .modal .tabulator-edit-select-list {
                    z-index: 9999999999 !important;
                    position: absolute !important;
                }

                .modal .tabulator-editor {
                    z-index: 9999999999 !important;
                }

                .modal .tabulator-editor select {
                    z-index: 9999999999 !important;
                }
            </style>

            <ui-panel title="${title}" closable="true">
                ${this.content}
            </ui-panel>
        `;


        // add event click on X
        this.querySelector("button").addEventListener("click", () => {
            this.remove();
        });

        this.classList.add("modal");
    }

    static get observedAttributes() {
        return ["title"];
    }


}

customElements.define('ui-modal', Modal);
