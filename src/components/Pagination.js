
export default class GridPagination extends HTMLElement {
    constructor() {
        super();
    }

    onPageChange = () => { }

    connectedCallback() {
        this.render();
    }

    getCurrentPage() {
        return parseInt(this.getAttribute('current-page')) || 1;
    }

    getTotalPages() {
        return Math.max(Math.ceil(this.getTotal() / this.getPageSize()), 1);
    }

    getTotal() {
        return parseInt(this.getAttribute('total')) || 0;
    }

    getPageSize() {
        return parseInt(this.getAttribute('page-size')) || 50;
    }

    static get observedAttributes() {
        return ['total', 'current-page'];
    }

    attributeChangedCallback(name, oldValue, newValue) {
        this.render();
    }

    prepareData(page) {
        return {
            page: page,
            start: (page - 1) * this.getPageSize(),
            limit: this.getPageSize(),
        };
    }

    getInitialData() {
        return {
            page: 1,
            start: 0,
            limit: this.getPageSize(),
        }
    }

    getMaxElementCurrentPage() {
        return Math.min(this.getCurrentPage() * this.getPageSize(), this.getTotal());
    }

    render() {
        this.innerHTML = `
            <div class="pagination-pages">
                <button id="first-page"> <span class="material-symbols-outlined size-20">first_page</span> </button>
                <button id="prev-page"> <span class="material-symbols-outlined size-20">chevron_left</span> </button>
                <div>
                    <input type="number" min="1" max="${this.getTotalPages()}" value="${this.getCurrentPage()}" />
                    <span>/ ${this.getTotalPages()}</span>
                </div>
                <button id="next-page"> <span class="material-symbols-outlined size-20">chevron_right</span> </button>
                <button id="last-page"> <span class="material-symbols-outlined size-20">last_page</span> </button>
            </div>
            <div>
                Elementi ${((this.getCurrentPage() - 1) * this.getPageSize()) + 1} - ${this.getMaxElementCurrentPage()} di ${this.getTotal()}
            </div>
        `;

        this.querySelector("#first-page").addEventListener("click", (event) => this.onPageChange(this.prepareData(1)));
        this.querySelector("#last-page").addEventListener("click", (event) => this.onPageChange(this.prepareData(this.getTotalPages())));
        this.querySelector("#prev-page").addEventListener("click", (event) => {
            if (this.getCurrentPage() > 1) this.onPageChange(this.prepareData(this.getCurrentPage() - 1));
        });
        this.querySelector("#next-page").addEventListener("click", (event) => {
            if (this.getCurrentPage() < this.getTotalPages()) this.onPageChange(this.prepareData(this.getCurrentPage() + 1));
        });


    }


}

customElements.define('grid-pagination', GridPagination);