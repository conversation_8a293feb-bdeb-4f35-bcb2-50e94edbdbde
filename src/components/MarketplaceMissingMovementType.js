import Mc2Element from "./Mc2Element.js";
import { filterMarketplaceWithoutMovementType, fetchMarketplaceWithoutMovementType, updateMarketplaceMovementType } from '../stores/Marketplace.js';
import Mc2Table from "../services/Mc2Table.js";
import { getMovementTypeById, filterMovementType } from "../stores/MovementTypeStore.js";
import { getCurrentSchoolYear } from "../stores/AnniStore.js";

class MarketplaceMissingMovementType extends Mc2Element {
    movementType = [];

    constructor() {
        super();
    }

    connectedCallback() {
        this.render();

        this.movementType = filterMovementType({ 'school_year': getCurrentSchoolYear().name });

        this.table = new Mc2Table(this.querySelector("#table"), {
            columnDefaults: {
                headerSort: false,
                vertAlign: "middle"
            },
            columns: [
                { field: "id", visible: false },
                { title: "Nome servizio", field: "description", hozAlign: 'left' },
                {
                    title: "Tipo movimento anno precedente", field: "type_id_previous_year", hozAlign: 'left', formatter: (cell) => {
                        const movementType = getMovementTypeById(cell.getValue());
                        return movementType ? movementType.name : '';
                    }
                },
                {
                    title: "Nuovo tipo movimento",
                    field: "new_type_id",  // <-- senza questo, l’editor non funziona
                    editor: "list",
                    formatter: (cell) => {
                        const movementType = getMovementTypeById(cell.getValue());
                        return movementType ? movementType.name : '---';
                    },
                    editorParams: {
                        values: [
                            { label: "---", value: "" },
                            ...this.movementType.map(mType => {
                                return { label: mType.name, value: mType.id };
                            })
                        ]
                    }
                }

            ],
        });

        this.table.on("tableBuilt", () => {
            this.loadData();
        });

        this.querySelector("button").addEventListener("click", () => {
            this.saveData();
        });
    }

    async saveData() {
        this.table.dataLoader.alertLoader();
        const data = this.table.getData();

        await Promise.all(data
            .filter(d => d.new_type_id)
            .map(d => updateMarketplaceMovementType(d.id, d.new_type_id))
        );
        await fetchMarketplaceWithoutMovementType();

        this.loadData();
        this.table.dataLoader.clearAlert();

    }

    loadData() {
        let data = filterMarketplaceWithoutMovementType();
        if (data) {
            this.table.setData(data);
        }
    }

    render() {
        this.innerHTML = `
            <div class="v-box h-full">
                <div style="color: var(--color-background); background-color: var(--color-danger); border-radius: 3px;padding: 10px;text-align: center;margin-bottom: 20px;font-size: 16px;">
                    Ci sono alcuni servizi che non hanno alcun un tipo di movimento associato dopo che è stato finalizzato il cambio anno, ma che lo avevano nell'anno precedente. <br />
                    Chiediamo quindi di selezionare il tipo di movimento da associare a ciascun servizio. L'operazione può essere effettuata anche in momenti diversi.
                    E' infatti possibile salvarli anche solo in parte e poi tornare su questa sezione tramite il pulsante "Servizi -> Abbina tipo di movimento" presente nella sezione "Gestione rette". <br />
                    Al prossimo accesso, se ne rimangono da associare, verrà comunque riproposto questo messaggio.
                </div>
                <div id="table" class="h-full" style="overflow:auto;z-index:999999999"></div>
                <div class="h-box" style="justify-content: end;">
                    <button class="mc2-btn primary md">Salva</mc2-button>
                </div>
            </div>

        `;

    }


}

customElements.define('marketplace-missing-movement-type', MarketplaceMissingMovementType);