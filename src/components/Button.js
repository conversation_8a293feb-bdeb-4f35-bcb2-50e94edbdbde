import Mc2Element from "./Mc2Element";

export default class <PERSON><PERSON> extends Mc2Element {
    constructor() {
        super();
    }

    connectedCallback() {
        this.render();
    }

    render() {
        const type = this.getAttribute('type') || 'primary';
        // add attribute size default null
        const size = this.getAttribute('size') || 'md';

        this.innerHTML = `
            <style>
                .mc2-btn {
                    border: none;
                    padding: 10px;
                    border-radius: var(--border-radius);
                    cursor: pointer;
                    font-weight: bold;
                }

                .mc2-btn.sm {
                    font-size: 15px;
                }

                .mc2-btn.md {
                    font-size: 20px;
                }

                .mc2-btn.lg {
                    font-size: 25px;
                }

                .mc2-btn.primary {
                    background-color: var(--color-primary);
                    color: var(--color-background);
                }

                .mc2-btn.primary:hover {
                    background-color: var(--color-primary-hover);
                }

                .mc2-btn.secondary {
                    background-color: var(--color-secondary);
                    color: var(--color-background);
                }

                .mc2-btn.secondary:hover {
                    background-color: var(--color-secondary-hover);
                }

                .mc2-btn.danger {
                    background-color: var(--color-danger);
                    color: var(--color-background);
                }

                .mc2-btn.danger:hover {
                    background-color: var(--color-danger-hover);
                }

                .mc2-btn.success {
                    background-color: var(--color-success);
                    color: var(--color-background);
                }

                .mc2-btn.success:hover {
                    background-color: var(--color-success-hover);
                }

                .mc2-btn.warning {
                    background-color: var(--color-warning);
                    color: var(--color-background);
                }

                .mc2-btn.warning:hover {
                    background-color: var(--color-warning-hover);
                }
            </style>

            <button class="mc2-btn ${type} ${size}">
                ${this.content}
            </button>
        `;
    }
}

customElements.define('mc2-button', Button);