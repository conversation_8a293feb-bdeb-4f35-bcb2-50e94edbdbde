import Mc2Element from "./Mc2Element";


export default class Panel extends Mc2Element {

    constructor() {
        super();
    }

    connectedCallback() {
        this.render();
    }

    static get observedAttributes() {
        return ["title", "closable"];
    }

    render() {

        let title = this.getAttribute("title") || "";
        let close = this.getAttribute("closable") || false;

        this.innerHTML = `

        <style>
            .ui-panel {
                display: flex;
                flex-direction: column;
                height: 100%;
            }

            .ui-panel-header {
                background-color: var(--color-primary);
                color: var(--color-text-input);
                padding: 10px;
                border-radius: var(--border-radius) var(--border-radius) 0 0;
                display: flex;
                justify-content: space-between;
                align-items: center;
            }

            .ui-panel-header button {
                background-color: var(--color-primary);
                color: var(--color-text-input);
                border: 0;
                border-radius: 100%;
                display: flex;
                justify-content: space-between;
                align-items: center;
                padding: 2px;
                cursor: pointer;
            }
            .ui-panel-header button:hover {
                background-color: var(--color-primary-hover);
            }

            .ui-panel-content {
                padding: 5px;
                flex: 1;
                border-style: solid;
                border-color: var(--color-primary);
                background-color: var(--color-background);
                height: 100%;
                border-width: 0px 5px 5px 5px;
                display:flex;
                flex-direction: column;
            }

            .ui-panel-header span {
                font-weight: bold;
                font-size: 20px;
            }

        </style>

        <div class="ui-panel">
            <div class="ui-panel-header">
                <span>${title}</span>
                ${close ? `<button><span class="material-symbols-outlined">close</span></button>` : ""}
            </div>
            <div class="ui-panel-content">${this.content}</div>
        </div>
        `;




    }
}

customElements.define('ui-panel', Panel);
