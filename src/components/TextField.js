
export default class TextField extends HTMLElement {
    constructor() {
        super();

    }

    connectedCallback() {
        this.render();
    }

    render() {
        // Get attributes with default values
        const labelText = this.getAttribute('label') || '';
        const inputName = this.getAttribute('name') || 'query';
        const placeholder = this.getAttribute('placeholder') || 'Cerca ...';

        this.innerHTML = `
            <style>
                text-field {
                    display: flex;
                    flex-direction: column;
                    row-gap: 2px;
                }

                text-field label {
                    color: #15428b;
                }

                text-field input {
                    width: 100%;
                    border: 1px solid #ccc;
                    border-radius: 2px 0px 0px 2px;
                    padding: 5px;
                    background-color: #fff;
                }

                text-field button {
                    background: #eee;
                    border: 1px solid #ccc;
                    cursor: pointer;
                    border-left: 0px;
                    border-radius: 0px 2px 2px 0px;
                }

                text-field button:hover {
                    background-color: #ccc;
                }
            </style>

            <div>
                <label for="filter-input">${labelText}</label>
                <div style="display: flex; align-items: center;flex-direction: row;">
                    <input type="text" id="filter-input" name="${inputName}" placeholder="${placeholder}" />
                    <button>
                        <span class="material-symbols-outlined size-20">search</span>
                    </button>
                </div>
            </div>
        `;

    }
}

customElements.define('text-field', TextField);
