import Panel from "./Panel";
import { getPrintQueue, deletePrint } from "../stores/PrintQueue";
import Mc2Table from "../services/Mc2Table.js";

export default class PrintQueueList extends HTMLElement {

    constructor() {
        super();
    }

    async connectedCallback() {
        this.render();

        this.table = new Mc2Table(this.querySelector("#table"), {
            columnDefaults: {
                headerSort: false,
                vertAlign:"middle"

            },
            columns: [
                { field: "id", visible: false },
                {
                    field: 'completed', hozAlign: 'center', width: 50, formatter: function (cell, formatterParams, onRendered) {
                        if (cell.getValue() === true) {
                            //return '<ui-icon name="accept"></ui-icon>';
                            return '<i class="material-symbols-outlined" style="color: var(--color-success)">check</i>'
                        } else {
                            return '<i class="material-symbols-outlined">timer</i>'
                        }
                    },
                },
                {
                    title: "Nome", field: "name", hozAlign: 'left', formatter: function (cell, formatterParams, onRendered) {
                        if (cell.getRow().getData().completed) {
                            return `<a href="/mc2-api/core/print/${cell.getRow().getData().id}" target="_blank">${cell.getValue()}</a>`;
                        }
                        return cell.getValue();
                    }
                },
                {
                    width: 50, hozAlign: 'center', formatter: function (cell, formatterParams, onRendered) {
                        // return '<span style="display: flex;align-items: center;justify-content: center;"><i class="icon-delete"></i></span>';
                        return '<i style="color: var(--color-danger)" class="material-symbols-outlined">delete</i>';
                    },
                    cellClick: async (e, cell) => {
                        this.table.dataLoader.alertLoader();
                        const printId = cell.getRow().getData().id;
                        try {
                            res = await deletePrint(printId);
                        } catch (error) {
                            console.error(error);
                        }
                        this.table.dataLoader.clearAlert();
                        this.loadData()
                    }
                },
            ],
        });

        this.loadData()

    }

    async loadData() {
        this.table.dataLoader.alertLoader();
        let res = null;
        try {
            res = await getPrintQueue();
            this.table.setData(res.results);
        } catch (error) {
            console.error(error);
        }
        this.table.dataLoader.clearAlert();
    }

    render() {
        this.innerHTML = `
            <div id="table" style="flex:1;height:100%;overflow:auto;"></div>
        `;

    }
}

customElements.define('print-queue-list', PrintQueueList);
