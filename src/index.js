import { fetchMarketplaceWithoutMovementType, filterMarketplaceWithoutMovementType } from './stores/Marketplace.js';
import { fetchMovementType } from './stores/MovementTypeStore.js';
import { refreshAnniScolastici } from './stores/AnniStore.js';

const requireContext = require.context('.', true, /\.js$/);
requireContext.keys().forEach(requireContext);


document.afterMainViewReady = function() {
    document.addPrintQueueButton();
    document.checkMarketplaceMissMovementType();
}

document.addPrintQueueButton = function () {
    const printButton = document.createElement("button");
    printButton.classList.add("print-list-btn", "shadow-primary");
    document.body.appendChild(printButton);

    // event click
    printButton.addEventListener("click", function() {
        document.createModal('Stampe', '<print-queue-list></print-queue-list>');
    });
};

document.createModal = function(title, content) {
    const modal = document.createElement('ui-modal');
    modal.setAttribute('title', title);
    modal.setContent(content);
    document.body.appendChild(modal);
};



document.checkMarketplaceMissMovementType = async function(auto=true) {
    if(auto===true) return;
    try {
        await fetchMarketplaceWithoutMovementType();
    } catch (error) {
        console.log(error);
        return;
    }


    if (filterMarketplaceWithoutMovementType().length > 0) {

        await refreshAnniScolastici();
        fetchMovementType().then(() => {
            document.createModal('Servizi senza tipo di movimento', '<marketplace-missing-movement-type class="h-full"></marketplace-missing-movement-type>');
        });
    } else {
        if (auto===false) Ext.Msg.alert('INFO', 'Non ci sono servizi senza tipo di movimento');
    }


}

