/**
 * Script per generare automaticamente la lista di import per tutti i file Sencha
 * Esegui con: node generate-sencha-imports.js
 */

const fs = require('fs');
const path = require('path');

function getAllJSFiles(dir, fileList = []) {
    const files = fs.readdirSync(dir);
    
    files.forEach(file => {
        const filePath = path.join(dir, file);
        const stat = fs.statSync(filePath);
        
        if (stat.isDirectory()) {
            getAllJSFiles(filePath, fileList);
        } else if (file.endsWith('.js')) {
            fileList.push(filePath);
        }
    });
    
    return fileList;
}

function generateImports() {
    const appDir = './app';
    const allFiles = getAllJSFiles(appDir);
    
    // Ordina i file per categoria
    const models = allFiles.filter(f => f.includes('/model/')).sort();
    const stores = allFiles.filter(f => f.includes('/store/')).sort();
    const views = allFiles.filter(f => f.includes('/view/')).sort();
    const controllers = allFiles.filter(f => f.includes('/controller/')).sort();
    
    console.log('// MODELS');
    models.forEach(file => {
        const importPath = file.replace(/\\/g, '/');
        console.log(`import '${importPath}';`);
    });
    
    console.log('\n// STORES');
    stores.forEach(file => {
        const importPath = file.replace(/\\/g, '/');
        console.log(`import '${importPath}';`);
    });
    
    console.log('\n// VIEWS');
    views.forEach(file => {
        const importPath = file.replace(/\\/g, '/');
        console.log(`import '${importPath}';`);
    });
    
    console.log('\n// CONTROLLERS');
    controllers.forEach(file => {
        const importPath = file.replace(/\\/g, '/');
        console.log(`import '${importPath}';`);
    });
    
    // Genera anche un file con tutti gli import
    const allImports = [
        '// Auto-generated Sencha imports',
        '// MODELS',
        ...models.map(f => `import '${f.replace(/\\/g, '/')}';`),
        '',
        '// STORES', 
        ...stores.map(f => `import '${f.replace(/\\/g, '/')}';`),
        '',
        '// VIEWS',
        ...views.map(f => `import '${f.replace(/\\/g, '/')}';`),
        '',
        '// CONTROLLERS',
        ...controllers.map(f => `import '${f.replace(/\\/g, '/')}';`)
    ].join('\n');
    
    fs.writeFileSync('sencha-imports.js', allImports);
    console.log('\n✅ File sencha-imports.js generato con successo!');
}

generateImports();
