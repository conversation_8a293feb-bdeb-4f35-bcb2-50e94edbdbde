/**
 * Entry point semplificato per la produzione
 * Approccio manuale con concatenazione diretta
 */

// 1. ExtJS Core
import './ext/ext-all.js';

// 2. Estensioni e localizzazioni
import './resources/js/Notification.js';
import './resources/js/ext-lang-it.js';

// 3. CSS
import './bootstrap.css';

// 4. Importa tutti i file Sencha usando require.context
// Questo carica automaticamente tutti i file .js nella cartella app/
function requireAll(r) {
    r.keys().forEach(r);
}

// Carica tutti i modelli prima
requireAll(require.context('./app/model', true, /\.js$/));

// Poi gli store
requireAll(require.context('./app/store', true, /\.js$/));

// Poi le view
requireAll(require.context('./app/view', true, /\.js$/));

// Infine i controller
requireAll(require.context('./app/controller', true, /\.js$/));

// 5. Carica l'applicazione principale Sencha
import './app.js';

// 6. Carica il codice moderno ES6
import './src/index.js';

console.log('Bundle unificato caricato con successo!');
