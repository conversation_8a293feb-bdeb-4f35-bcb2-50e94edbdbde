/**
 * Entry point unificato per la produzione
 * Questo file carica tutto il codice Sencha e moderno in ordine corretto
 */

// 1. Prima carichiamo ExtJS core
import './ext/ext-all.js';

// 2. Poi le estensioni e localizzazioni
import './resources/js/Notification.js';
import './resources/js/ext-lang-it.js';

// 3. Carichiamo tutti i CSS
import './bootstrap.css';

// 4. Funzione per caricare dinamicamente tutti i file Sencha nell'ordine corretto
function loadSenchaFiles() {
    // Ordine di caricamento: Models -> Stores -> Views -> Controllers -> App
    const senchaFiles = [
        // Models (devono essere caricati per primi)
        './app/model/ProtocolProtocol.js',
        './app/model/SettingsGroup.js',
        './app/model/Permission.js',
        './app/model/SettingsUser.js',
        './app/model/Employee.js',
        './app/model/CoreCity.js',
        './app/model/CoreCountry.js',
        './app/model/SocialPosition.js',
        './app/model/Absence.js',
        './app/model/AbsenceKind.js',
        './app/model/DayTimeTable.js',
        './app/model/TimeTable.js',
        './app/model/WeekBorder.js',
        './app/model/Presence.js',
        './app/model/BudgetActivities.js',
        './app/model/AbsenceStack.js',
        './app/model/ExtraordinaryStored.js',
        './app/model/ExtraordinaryAbsStack.js',
        './app/model/HomeInfo.js',
        './app/model/Institute.js',
        './app/model/HomeReleaseNote.js',
        './app/model/EventError.js',
        './app/model/UserLog.js',
        './app/model/Decreto.js',
        './app/model/CcpMovement.js',
        './app/model/CcpType.js',
        './app/model/Classe.js',
        './app/model/CcpBollettino.js',
        './app/model/CorePrintSpool.js',
        './app/model/CoreBankAccount.js',
        './app/model/CcpPaymentMethod.js',
        './app/model/EmployeeParameters.js',
        './app/model/EmployeeTree.js',
        './app/model/CcpSubject.js',
        './app/model/PersonnelHourType.js',
        './app/model/PersonnelProject.js',
        './app/model/CcpStudent.js',
        './app/model/CcpSchoolYear.js',
        './app/model/MC2Table.js',
        './app/model/DataLookupResult.js',
        './app/model/MC2Parameter.js',
        './app/model/InstituteType.js',
        './app/model/StackPersonnelLink.js',
        './app/model/ArchiveDocument.js',
        './app/model/ProtocolSubjectKind.js',
        './app/model/ProtocolSendMethod.js',
        './app/model/ProtocolType.js',
        './app/model/ProtocolAction.js',
        './app/model/ArchiveMetadata.js',
        './app/model/ArchiveClass.js',
        './app/model/CoreParameter.js',
        './app/model/ProtocolCorrespondent.js',
        './app/model/TrasparenzaVoice.js',
        './app/model/AlboPublication.js',
        './app/model/AlboEntity.js',
        './app/model/AlboCategory.js',
        './app/model/ProtocolHistory.js',
        './app/model/ArchiveOrigin.js',
        './app/model/ProtocolCorrespondentOrigin.js',
        './app/model/AlboArea.js',
        './app/model/AlboHistory.js',
        './app/model/CcpCategory.js',
        './app/model/CcpAdditional.js',
        './app/model/CcpAdditionalLinked.js',
        './app/model/CcpResidual.js',
        './app/model/CcpPayment.js',
        './app/model/CcpPayer.js',
        './app/model/CcpReceipt.js',
        './app/model/PersonnelHourTypeLinked.js',
        './app/model/PersonnelProjectLinked.js',
        './app/model/Register.js',
        './app/model/ArchiveMailAccount.js',
        './app/model/ArchiveMail.js',
        './app/model/DocumentFlow.js',
        './app/model/DocumentStep.js',
        './app/model/ArchiveClassStep.js',
        './app/model/ArchiveMailAttachment.js',
        './app/model/ArchiveDocumentFile.js',
        './app/model/RemoteClass.js',
        './app/model/MyModel.js',
        './app/model/Assignee.js',
        './app/model/ArchiveOffice.js',
        './app/model/ArchiveTemplate.js',
        './app/model/ArchiveDossier.js',
        './app/model/Dossier.js',
        './app/model/Raccolte.js',
        './app/model/Contact.js',
        './app/model/ContactGroup.js',
        './app/model/CcpTypeStep.js',
        './app/model/GenericSearch.js',
        './app/model/CcpInvoice.js',
        './app/model/CcpInvoiceTransmission.js',
        './app/model/CcpDepositSlip.js',
        './app/model/Credit.js',
        './app/model/CcpStudentMarket.js',
        './app/model/CcpParent.js',
        './app/model/CcpReminder.js',
        './app/model/CcpServizi.js',
        './app/model/CcpDeposit.js',
        './app/model/CreditType.js',
        './app/model/Discount.js',
        './app/model/CcpVatCode.js',
        './app/model/Invoice.js',
        './app/model/Supplier.js',
        './app/model/Indirizzo.js',
        './app/model/StudentBalance.js',
        './app/model/MagisterEsercizio.js',
        './app/model/Corrispettivo.js',
        './app/model/CcpReport.js',
        './app/model/CcpInvoiceDepositSlip.js'
    ];

    // Carica tutti i file in sequenza
    return Promise.all(senchaFiles.map(file => import(file)));
}

// 5. Funzione per caricare gli store
function loadSenchaStores() {
    // Lista degli store da caricare (versione ridotta per esempio)
    const storeFiles = [
        './app/store/ProtocolProtocols.js',
        './app/store/SettingsGroups.js',
        './app/store/SettingsUsers.js',
        './app/store/Employees.js',
        // ... aggiungi tutti gli altri store necessari
    ];

    return Promise.all(storeFiles.map(file => import(file)));
}

// 6. Funzione per caricare le view
function loadSenchaViews() {
    const viewFiles = [
        './app/view/LoginView.js',
        './app/view/GroupEditWin.js',
        './app/view/UserEditWin.js',
        './app/view/PaswdEditWin.js',
        './app/view/AbsenceNewWin.js',
        './app/view/PresencesNewWin.js',
        './app/view/EmployeeProjectsEditWin.js',
        './app/view/EmployeeHolidayCalendarWin.js',
        './app/view/EmployeeAbsenceStackWin.js',
        './app/view/EmployeePresencesPrintWin.js',
        './app/view/DispatcherView.js',
        './app/view/InstituteEditWin.js',
        './app/view/CcpMovementEditWin.js',
        './app/view/MainView.js',
        // ... aggiungi tutte le altre view necessarie
    ];

    return Promise.all(viewFiles.map(file => import(file)));
}

// 7. Carica il controller
function loadSenchaControllers() {
    return import('./app/controller/PermissionController.js');
}

// 8. Inizializzazione sequenziale
async function initializeSencha() {
    try {
        console.log('Caricamento Models...');
        await loadSenchaFiles();
        
        console.log('Caricamento Stores...');
        await loadSenchaStores();
        
        console.log('Caricamento Views...');
        await loadSenchaViews();
        
        console.log('Caricamento Controllers...');
        await loadSenchaControllers();
        
        console.log('Inizializzazione app Sencha...');
        // Ora carica l'applicazione principale
        await import('./app.js');
        
        console.log('Sencha caricato con successo!');
    } catch (error) {
        console.error('Errore nel caricamento Sencha:', error);
    }
}

// 9. Carica il codice moderno ES6
async function initializeModernCode() {
    try {
        console.log('Caricamento codice moderno...');
        await import('./src/index.js');
        console.log('Codice moderno caricato con successo!');
    } catch (error) {
        console.error('Errore nel caricamento codice moderno:', error);
    }
}

// 10. Inizializzazione principale
async function initialize() {
    // Prima inizializza Sencha
    await initializeSencha();
    
    // Poi il codice moderno
    await initializeModernCode();
    
    console.log('Applicazione completamente inizializzata!');
}

// Avvia l'inizializzazione quando il DOM è pronto
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initialize);
} else {
    initialize();
}
